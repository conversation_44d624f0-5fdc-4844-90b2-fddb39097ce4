import React, { useEffect, useState } from 'react';

interface AnimatedCounterProps {
  value: number;
  suffix?: string;
  duration?: number;
  className?: string;
}

/**
 * 숫자를 애니메이션으로 카운트업하는 컴포넌트
 * 통계 섹션에서 사용자의 시선을 끌기 위한 효과
 */
const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  suffix = '',
  duration = 2000,
  className = ''
}) => {
  const [currentValue, setCurrentValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Intersection Observer를 사용하여 화면에 보일 때만 애니메이션 실행
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById(`counter-${value}`);
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [value, isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    const startTime = Date.now();
    const startValue = 0;
    const endValue = value;

    const updateCounter = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // easeOutCubic 이징 함수 적용
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const newValue = Math.floor(startValue + (endValue - startValue) * easedProgress);

      setCurrentValue(newValue);

      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      }
    };

    requestAnimationFrame(updateCounter);
  }, [isVisible, value, duration]);

  return (
    <span
      id={`counter-${value}`}
      className={`text-4xl font-bold text-etri-blue-600 font-tabular tracking-normal ${className}`}
      style={{ fontFamily: 'Pretendard', fontFeatureSettings: '"tnum" 1' }}
    >
      {currentValue.toLocaleString()}{suffix}
    </span>
  );
};

export default AnimatedCounter;
