import React from 'react';
import type { NoticeSearchParams } from '@/types/notice';
import { noticeCategories, noticeStatuses } from '@/data/mockNotices';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NoticeSearchProps {
  searchParams: NoticeSearchParams;
  onSearchChange: (params: NoticeSearchParams) => void;
  className?: string;
}

export const NoticeSearch: React.FC<NoticeSearchProps> = ({
  searchParams,
  onSearchChange,
  className,
}) => {
  const handleQueryChange = (query: string) => {
    onSearchChange({ ...searchParams, query });
  };

  const handleCategoryChange = (category: string) => {
    onSearchChange({ 
      ...searchParams, 
      category: category as NoticeSearchParams['category'] 
    });
  };

  const handleStatusChange = (status: string) => {
    onSearchChange({ 
      ...searchParams, 
      status: status as NoticeSearchParams['status'] 
    });
  };

  const handleImportantChange = (isImportant: boolean | undefined) => {
    onSearchChange({ ...searchParams, isImportant });
  };

  const handleReset = () => {
    onSearchChange({
      query: '',
      category: 'all',
      status: 'all',
      isImportant: undefined,
    });
  };

  const hasActiveFilters = 
    searchParams.query ||
    (searchParams.category && searchParams.category !== 'all') ||
    (searchParams.status && searchParams.status !== 'all') ||
    searchParams.isImportant !== undefined;

  return (
    <div className={cn("space-y-4", className)}>
      {/* 검색어 입력 */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="공지사항 제목, 내용으로 검색..."
          value={searchParams.query || ''}
          onChange={(e) => handleQueryChange(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* 필터 옵션 */}
      <div className="flex flex-wrap gap-3 items-center">
        <div className="flex items-center gap-1">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">필터:</span>
        </div>

        {/* 카테고리 필터 */}
        <Select
          value={searchParams.category || 'all'}
          onValueChange={handleCategoryChange}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="카테고리" />
          </SelectTrigger>
          <SelectContent>
            {noticeCategories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 상태 필터 */}
        <Select
          value={searchParams.status || 'all'}
          onValueChange={handleStatusChange}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="상태" />
          </SelectTrigger>
          <SelectContent>
            {noticeStatuses.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 중요 공지 필터 */}
        <div className="flex items-center gap-2">
          <Button
            variant={searchParams.isImportant === true ? "default" : "outline"}
            size="sm"
            onClick={() => handleImportantChange(
              searchParams.isImportant === true ? undefined : true
            )}
          >
            중요 공지만
          </Button>
        </div>

        {/* 필터 초기화 */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4 mr-1" />
            초기화
          </Button>
        )}
      </div>

      {/* 활성 필터 표시 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchParams.query && (
            <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
              검색: "{searchParams.query}"
            </div>
          )}
          {searchParams.category && searchParams.category !== 'all' && (
            <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
              카테고리: {noticeCategories.find(c => c.value === searchParams.category)?.label}
            </div>
          )}
          {searchParams.status && searchParams.status !== 'all' && (
            <div className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">
              상태: {noticeStatuses.find(s => s.value === searchParams.status)?.label}
            </div>
          )}
          {searchParams.isImportant && (
            <div className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
              중요 공지
            </div>
          )}
        </div>
      )}
    </div>
  );
};
