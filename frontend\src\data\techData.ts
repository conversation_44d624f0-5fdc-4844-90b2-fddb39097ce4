

export interface TechData {
  id: string;
  name: string;
  summary: string;
  guide: string;
  icon: string;
  code?: string;
  codeLanguage?: string;
  guideSummary?: string;
  guideFeatures?: string[];
}

export const techList: TechData[] = [
  {
    id: 'ai-vision',
    name: 'AI 비전 분석',
    summary: '이미지/영상 데이터에서 객체를 탐지하고 분석하는 AI 기술',
    guide: 'AI 비전 분석은 딥러닝 기반의 이미지/영상 처리 기술로, 객체 탐지, 분류, 추적 등 다양한 응용이 가능합니다. 주요 프레임워크로는 YOLO, Detectron2 등이 있습니다.',
    icon: 'Camera',
    code: `import cv2\nfrom yolov5 import YOLOv5\n\nyolo = YOLOv5('yolov5s.pt')\nimg = cv2.imread('image.jpg')\nresults = yolo.predict(img)\nresults.show()`,
    codeLanguage: 'python',
    guideSummary: 'YOLO, Detectron2 등 최신 딥러닝 기반 객체 탐지 기술을 활용한 비전 분석',
    guideFeatures: [
      '실시간 객체 탐지 및 분류',
      '다양한 프레임워크 지원',
      '이미지/영상 데이터 처리',
    ],
  },
  {
    id: 'nlp',
    name: '자연어 처리',
    summary: '텍스트 데이터의 의미를 분석하고 처리하는 기술',
    guide: '자연어 처리는 텍스트 데이터의 의미 분석, 감정 분석, 요약, 번역 등 다양한 분야에 활용됩니다. 대표 라이브러리로는 HuggingFace Transformers, KoNLPy 등이 있습니다.',
    icon: 'FileText',
    code: `from transformers import pipeline\nsummarizer = pipeline('summarization')\ntext = '딥러닝 기반 자연어 처리 기술은...'\nprint(summarizer(text))`,
    codeLanguage: 'python',
    guideSummary: '텍스트 의미 분석, 감정 분석, 번역 등 다양한 NLP 응용',
    guideFeatures: [
      'BERT, GPT 등 최신 모델 지원',
      '한국어 자연어 처리 라이브러리',
      '문서 요약, 분류, 번역',
    ],
  },
  {
    id: 'iot',
    name: 'IoT 센서 네트워크',
    summary: '다양한 센서 데이터를 수집/분석하는 IoT 네트워크 기술',
    guide: 'IoT 센서 네트워크는 다양한 센서로부터 데이터를 수집하고, 이를 실시간으로 분석/처리하는 기술입니다. MQTT, LoRaWAN 등 다양한 프로토콜이 사용됩니다.',
    icon: 'RadioTower',
    code: `import paho.mqtt.client as mqtt\ndef on_message(client, userdata, msg):\n    print(msg.topic, msg.payload)\nclient = mqtt.Client()\nclient.on_message = on_message\nclient.connect('broker.hivemq.com', 1883)\nclient.subscribe('iot/sensor')\nclient.loop_forever()`,
    codeLanguage: 'python',
    guideSummary: '실시간 센서 데이터 수집 및 분석, 다양한 IoT 프로토콜 지원',
    guideFeatures: [
      'MQTT, LoRaWAN 등 프로토콜',
      '센서 데이터 실시간 처리',
      '분산 네트워크 구성',
    ],
  },
  {
    id: 'blockchain',
    name: '블록체인 기반 데이터 관리',
    summary: '데이터의 위변조 방지와 투명한 기록을 위한 블록체인 기술',
    guide: '블록체인 기술은 데이터의 위변조 방지, 투명한 기록, 스마트 컨트랙트 등 다양한 분야에 활용됩니다. 이더리움, 하이퍼레저 등이 대표적입니다.',
    icon: 'Link',
    code: `pragma solidity ^0.8.0;\ncontract SimpleStorage {\n  uint data;\n  function set(uint x) public { data = x; }\n  function get() public view returns (uint) { return data; }\n}`,
    codeLanguage: 'solidity',
    guideSummary: '데이터 위변조 방지, 투명한 기록, 스마트 컨트랙트 활용',
    guideFeatures: [
      '스마트 컨트랙트',
      '분산 원장',
      '데이터 무결성 보장',
    ],
  },
  {
    id: 'cloud',
    name: '클라우드 인프라',
    summary: '확장성과 유연성을 제공하는 클라우드 컴퓨팅 인프라',
    guide: '클라우드 인프라는 AWS, Azure, GCP 등 다양한 서비스로 제공되며, 인프라 자동화, 서버리스, 컨테이너 오케스트레이션 등이 주요 기술입니다.',
    icon: 'Cloud',
    code: `# AWS EC2 인스턴스 생성 예시 (boto3)\nimport boto3\nec2 = boto3.resource('ec2')\ninstance = ec2.create_instances(ImageId='ami-123456', MinCount=1, MaxCount=1, InstanceType='t2.micro')`,
    codeLanguage: 'python',
    guideSummary: 'AWS, Azure, GCP 등 다양한 클라우드 서비스 활용',
    guideFeatures: [
      '인프라 자동화',
      '서버리스 컴퓨팅',
      '컨테이너 오케스트레이션',
    ],
  },
  {
    id: 'bigdata',
    name: '빅데이터 분석',
    summary: '대용량 데이터의 저장, 처리, 분석을 위한 빅데이터 기술',
    guide: '빅데이터 분석은 Hadoop, Spark, Presto 등 대용량 데이터 처리 프레임워크를 활용하여 데이터 저장, 처리, 분석을 수행합니다.',
    icon: 'BarChart',
    code: `from pyspark.sql import SparkSession\nspark = SparkSession.builder.appName('BigData').getOrCreate()\ndf = spark.read.csv('data.csv')\ndf.groupBy('category').count().show()`,
    codeLanguage: 'python',
    guideSummary: 'Hadoop, Spark 등 대용량 데이터 처리/분석',
    guideFeatures: [
      '분산 데이터 처리',
      '실시간/배치 분석',
      '대규모 데이터 저장',
    ],
  },
]; 