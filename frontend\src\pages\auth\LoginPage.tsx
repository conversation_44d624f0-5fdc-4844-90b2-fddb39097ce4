import React, { useState } from 'react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';

const USER_TYPES = [
  { key: 'institution', label: '기관' },
  { key: 'company', label: '기업' },
  { key: 'person', label: '개인' },
];

// 로그인 페이지 컴포넌트
const LoginPage: React.FC = () => {
  const [userType, setUserType] = useState<'institution' | 'company' | 'person'>('person');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // 로그인 폼 제출 핸들러
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    // 간단한 유효성 검사
    if (!email || !password) {
      setError('이메일과 비밀번호를 모두 입력하세요.');
      return;
    }
    setLoading(true);
    // 실제 로그인 API 연동은 추후 구현
    setTimeout(() => {
      setLoading(false);
      alert(`${USER_TYPES.find(t=>t.key===userType)?.label} 로그인 성공! (실제 구현에서는 API 연동)`);
    }, 1000);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] py-12">
      <div className="w-full max-w-md bg-white rounded-xl shadow-lg p-8 border border-gray-100">
        <h2 className="text-2xl font-bold mb-6 text-center">로그인</h2>
        {/* 유형 선택 탭 */}
        <div className="flex justify-center mb-8 gap-2">
          {USER_TYPES.map(type => (
            <button
              key={type.key}
              type="button"
              className={`px-4 py-2 rounded-lg text-sm font-semibold border transition-all ${userType===type.key ? 'bg-etri-blue-600 text-white border-etri-blue-600' : 'bg-white text-etri-blue-700 border-etri-blue-100 hover:bg-etri-blue-50'}`}
              onClick={() => setUserType(type.key as any)}
            >
              {type.label}
            </button>
          ))}
        </div>
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700">이메일</label>
            <Input
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              autoComplete="username"
              required
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700">비밀번호</label>
            <Input
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              placeholder="비밀번호"
              autoComplete="current-password"
              required
            />
          </div>
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? '로그인 중...' : '로그인'}
          </Button>
        </form>
        <div className="mt-6 text-center text-sm text-gray-500">
          계정이 없으신가요? <a href="/signup" className="text-etri-blue-600 hover:underline">회원가입</a>
        </div>
      </div>
    </div>
  );
};

export default LoginPage; 