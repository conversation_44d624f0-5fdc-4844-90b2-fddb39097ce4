# SQLAlchemy 모델 구현 가이드

## 1. 기본 설정

### 1.1 Base 모델 클래스
```python
# app/models/base.py
from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import declared_attr

Base = declarative_base()

class TimestampMixin:
    """타임스탬프 필드를 위한 Mixin 클래스"""
    
    @declared_attr
    def created_at(cls):
        return Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    @declared_attr
    def updated_at(cls):
        return Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

class BaseModel(Base, TimestampMixin):
    """모든 모델의 기본 클래스"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
```

### 1.2 사용자 관리 모델
```python
# app/models/user.py
from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, DateTime, Enum, CheckConstraint
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from .base import BaseModel

class UserType(PyEnum):
    INDIVIDUAL = "individual"
    COMPANY = "company"
    INSTITUTION = "institution"
    ADMIN = "admin"

class User(BaseModel):
    __tablename__ = "users"
    
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    user_type = Column(Enum(UserType), nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    
    # 관계 설정
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    authored_notices = relationship("Notice", back_populates="author", foreign_keys="Notice.author_id")
    applications = relationship("Application", back_populates="applicant", foreign_keys="Application.applicant_id")
    reviewed_applications = relationship("Application", back_populates="reviewer", foreign_keys="Application.reviewer_id")
    uploaded_files = relationship("FileUpload", back_populates="uploader")
    audit_logs = relationship("AuditLog", back_populates="user")
    
    # 제약조건
    __table_args__ = (
        CheckConstraint(
            "email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'",
            name="ck_users_email_format"
        ),
    )
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', type='{self.user_type.value}')>"

class UserProfile(BaseModel):
    __tablename__ = "user_profiles"
    
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), unique=True, nullable=False)
    full_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    organization = Column(String(200), nullable=True, index=True)
    position = Column(String(100), nullable=True)
    bio = Column(Text, nullable=True)
    profile_image_url = Column(String(500), nullable=True)
    address = Column(JSON, nullable=True)
    social_links = Column(JSON, nullable=True)
    
    # 관계 설정
    user = relationship("User", back_populates="profile")
    
    # 제약조건
    __table_args__ = (
        CheckConstraint(
            "phone IS NULL OR phone ~* '^\\+?[0-9\\-\\s\\(\\)]{10,20}$'",
            name="ck_user_profiles_phone_format"
        ),
    )
    
    def __repr__(self):
        return f"<UserProfile(id={self.id}, user_id={self.user_id}, name='{self.full_name}')>"
```

### 1.3 공지사항 모델
```python
# app/models/notice.py
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from .base import BaseModel

class Notice(BaseModel):
    __tablename__ = "notices"
    
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    summary = Column(String(500), nullable=True)
    author_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    category = Column(String(50), default="general", nullable=False, index=True)
    priority = Column(Integer, default=0, nullable=False)
    is_pinned = Column(Boolean, default=False, nullable=False, index=True)
    is_published = Column(Boolean, default=False, nullable=False)
    view_count = Column(Integer, default=0, nullable=False)
    published_at = Column(DateTime(timezone=True), nullable=True, index=True)
    
    # 관계 설정
    author = relationship("User", back_populates="authored_notices")
    attachments = relationship("NoticeAttachment", back_populates="notice", cascade="all, delete-orphan")
    
    # 제약조건
    __table_args__ = (
        CheckConstraint("priority >= 0 AND priority <= 10", name="ck_notices_priority_range"),
        CheckConstraint("LENGTH(title) >= 5", name="ck_notices_title_length"),
    )
    
    def __repr__(self):
        return f"<Notice(id={self.id}, title='{self.title[:30]}...', published={self.is_published})>"

class NoticeAttachment(BaseModel):
    __tablename__ = "notice_attachments"
    
    notice_id = Column(Integer, ForeignKey("notices.id", ondelete="CASCADE"), nullable=False, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_path = Column(String(500), nullable=False)
    download_count = Column(Integer, default=0, nullable=False)
    
    # 관계 설정
    notice = relationship("Notice", back_populates="attachments")
    
    def __repr__(self):
        return f"<NoticeAttachment(id={self.id}, filename='{self.filename}')>"
```

### 1.4 신청서 관리 모델
```python
# app/models/application.py
from sqlalchemy import Column, String, Text, Integer, Numeric, DateTime, ForeignKey, Boolean, CheckConstraint, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSON
from enum import Enum as PyEnum
from .base import BaseModel

class ApplicationStatus(PyEnum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"

class Application(BaseModel):
    __tablename__ = "applications"
    
    applicant_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    application_type = Column(String(50), nullable=False)
    status = Column(Enum(ApplicationStatus), default=ApplicationStatus.DRAFT, nullable=False, index=True)
    priority = Column(Integer, default=0, nullable=False)
    budget_requested = Column(Numeric(15, 2), nullable=True)
    project_duration_months = Column(Integer, nullable=True)
    application_data = Column(JSON, nullable=False)
    reviewer_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    review_notes = Column(Text, nullable=True)
    submitted_at = Column(DateTime(timezone=True), nullable=True, index=True)
    reviewed_at = Column(DateTime(timezone=True), nullable=True)
    
    # 관계 설정
    applicant = relationship("User", back_populates="applications", foreign_keys=[applicant_id])
    reviewer = relationship("User", back_populates="reviewed_applications", foreign_keys=[reviewer_id])
    attachments = relationship("ApplicationAttachment", back_populates="application", cascade="all, delete-orphan")
    
    # 제약조건
    __table_args__ = (
        CheckConstraint("budget_requested IS NULL OR budget_requested > 0", name="ck_applications_budget_positive"),
        CheckConstraint("project_duration_months IS NULL OR project_duration_months > 0", name="ck_applications_duration_positive"),
        CheckConstraint("budget_requested IS NULL OR (budget_requested >= 1000000 AND budget_requested <= 10000000000)", name="ck_applications_budget_range"),
    )
    
    def __repr__(self):
        return f"<Application(id={self.id}, title='{self.title[:30]}...', status='{self.status.value}')>"

class ApplicationAttachment(BaseModel):
    __tablename__ = "application_attachments"
    
    application_id = Column(Integer, ForeignKey("applications.id", ondelete="CASCADE"), nullable=False, index=True)
    attachment_type = Column(String(50), nullable=False, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_path = Column(String(500), nullable=False)
    is_required = Column(Boolean, default=False, nullable=False)
    
    # 관계 설정
    application = relationship("Application", back_populates="attachments")
    
    def __repr__(self):
        return f"<ApplicationAttachment(id={self.id}, type='{self.attachment_type}', filename='{self.filename}')>"
```

### 1.5 밸리관리 모델
```python
# app/models/valley.py
from sqlalchemy import Column, String, Integer, Text, Boolean, Date, ForeignKey, CheckConstraint, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSON
from enum import Enum as PyEnum
from .base import BaseModel

class CompanySize(PyEnum):
    STARTUP = "startup"
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"

class Company(BaseModel):
    __tablename__ = "companies"
    
    name = Column(String(200), nullable=False, index=True)
    business_registration_number = Column(String(50), unique=True, nullable=True)
    company_type = Column(String(50), nullable=False, index=True)
    industry_sector = Column(String(100), nullable=True, index=True)
    company_size = Column(Enum(CompanySize), nullable=True, index=True)
    founded_year = Column(Integer, nullable=True)
    website_url = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    address = Column(JSON, nullable=True)
    contact_info = Column(JSON, nullable=True)
    financial_info = Column(JSON, nullable=True)
    technology_stack = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 관계 설정
    relationships = relationship("CompanyRelationship", back_populates="company", foreign_keys="CompanyRelationship.company_id")
    related_relationships = relationship("CompanyRelationship", back_populates="related_company", foreign_keys="CompanyRelationship.related_company_id")
    
    # 제약조건
    __table_args__ = (
        CheckConstraint(
            "founded_year IS NULL OR (founded_year >= 1800 AND founded_year <= EXTRACT(YEAR FROM CURRENT_DATE))",
            name="ck_companies_founded_year"
        ),
    )
    
    def __repr__(self):
        return f"<Company(id={self.id}, name='{self.name}', type='{self.company_type}')>"

class Institution(BaseModel):
    __tablename__ = "institutions"
    
    name = Column(String(200), nullable=False, index=True)
    institution_type = Column(String(50), nullable=False, index=True)
    parent_organization = Column(String(200), nullable=True)
    established_year = Column(Integer, nullable=True)
    website_url = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    address = Column(JSON, nullable=True)
    contact_info = Column(JSON, nullable=True)
    research_areas = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 관계 설정
    company_relationships = relationship("CompanyRelationship", back_populates="institution")
    
    def __repr__(self):
        return f"<Institution(id={self.id}, name='{self.name}', type='{self.institution_type}')>"

class CompanyRelationship(BaseModel):
    __tablename__ = "company_relationships"
    
    company_id = Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False, index=True)
    related_company_id = Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=True, index=True)
    institution_id = Column(Integer, ForeignKey("institutions.id", ondelete="CASCADE"), nullable=True, index=True)
    relationship_type = Column(String(50), nullable=False, index=True)
    relationship_strength = Column(Integer, default=1, nullable=False)
    description = Column(Text, nullable=True)
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 관계 설정
    company = relationship("Company", back_populates="relationships", foreign_keys=[company_id])
    related_company = relationship("Company", back_populates="related_relationships", foreign_keys=[related_company_id])
    institution = relationship("Institution", back_populates="company_relationships")
    
    # 제약조건
    __table_args__ = (
        CheckConstraint("relationship_strength >= 1 AND relationship_strength <= 10", name="ck_company_relationships_strength"),
        CheckConstraint(
            "(related_company_id IS NOT NULL AND institution_id IS NULL) OR (related_company_id IS NULL AND institution_id IS NOT NULL)",
            name="ck_company_relationships_target"
        ),
        CheckConstraint("company_id != related_company_id", name="ck_company_relationships_not_self"),
    )
    
    def __repr__(self):
        return f"<CompanyRelationship(id={self.id}, type='{self.relationship_type}', strength={self.relationship_strength})>"

### 1.6 시스템 모델
```python
# app/models/system.py
from sqlalchemy import Column, String, Integer, Text, Boolean, DateTime, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSON, INET
from .base import BaseModel

class AuditLog(BaseModel):
    __tablename__ = "audit_logs"

    table_name = Column(String(100), nullable=False, index=True)
    record_id = Column(Integer, nullable=False, index=True)
    action = Column(String(20), nullable=False)
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    ip_address = Column(INET, nullable=True)
    user_agent = Column(Text, nullable=True)

    # 관계 설정
    user = relationship("User", back_populates="audit_logs")

    # 제약조건
    __table_args__ = (
        CheckConstraint("action IN ('INSERT', 'UPDATE', 'DELETE')", name="ck_audit_logs_action"),
    )

    def __repr__(self):
        return f"<AuditLog(id={self.id}, table='{self.table_name}', action='{self.action}')>"

class FileUpload(BaseModel):
    __tablename__ = "file_uploads"

    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_hash = Column(String(64), nullable=False, index=True)
    uploader_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    upload_purpose = Column(String(50), nullable=True)
    is_temporary = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)

    # 관계 설정
    uploader = relationship("User", back_populates="uploaded_files")

    # 제약조건
    __table_args__ = (
        CheckConstraint("file_size > 0", name="ck_file_uploads_size_positive"),
        CheckConstraint("file_size <= 104857600", name="ck_file_uploads_size_limit"),  # 100MB
    )

    def __repr__(self):
        return f"<FileUpload(id={self.id}, filename='{self.filename}', size={self.file_size})>"

class NoticeCategory(BaseModel):
    __tablename__ = "notice_categories"

    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    color_code = Column(String(7), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)

    def __repr__(self):
        return f"<NoticeCategory(id={self.id}, name='{self.name}')>"
```

## 2. 데이터베이스 연결 설정

### 2.1 데이터베이스 설정
```python
# app/core/database.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from app.core.config import settings
import logging

# 데이터베이스 엔진 생성
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,
    max_overflow=30,
    echo=settings.DEBUG  # 개발 환경에서만 SQL 로그 출력
)

# 세션 팩토리 생성
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 의존성 주입용 함수
def get_db() -> Session:
    """데이터베이스 세션 의존성"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        db.rollback()
        logging.error(f"Database error: {e}")
        raise
    finally:
        db.close()

# 데이터베이스 초기화
def init_db():
    """데이터베이스 테이블 생성"""
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)

# 데이터베이스 연결 테스트
def test_db_connection():
    """데이터베이스 연결 테스트"""
    try:
        with engine.connect() as connection:
            result = connection.execute("SELECT 1")
            return result.fetchone()[0] == 1
    except Exception as e:
        logging.error(f"Database connection failed: {e}")
        return False
```

### 2.2 환경 설정
```python
# app/core/config.py
from pydantic import BaseSettings, PostgresDsn
from typing import Optional

class Settings(BaseSettings):
    # 데이터베이스 설정
    DATABASE_URL: PostgresDsn
    DATABASE_ECHO: bool = False

    # 개발 환경 설정
    DEBUG: bool = False
    TESTING: bool = False

    # 보안 설정
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

## 3. 모델 사용 예제

### 3.1 사용자 생성 및 조회
```python
# 사용자 생성
from app.models.user import User, UserProfile, UserType
from app.core.database import SessionLocal

def create_user_with_profile(email: str, password: str, user_type: UserType, full_name: str):
    db = SessionLocal()
    try:
        # 사용자 생성
        user = User(
            email=email,
            hashed_password=password,  # 실제로는 해시된 비밀번호
            user_type=user_type,
            is_active=True,
            is_verified=False
        )
        db.add(user)
        db.flush()  # ID 생성을 위해 flush

        # 프로필 생성
        profile = UserProfile(
            user_id=user.id,
            full_name=full_name
        )
        db.add(profile)
        db.commit()

        return user
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

# 사용자 조회 (프로필 포함)
def get_user_with_profile(user_id: int):
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            # 관계를 통해 프로필 자동 로드
            profile = user.profile
            return user, profile
        return None, None
    finally:
        db.close()
```

### 3.2 공지사항 관리
```python
# 공지사항 생성
from app.models.notice import Notice, NoticeAttachment

def create_notice_with_attachments(title: str, content: str, author_id: int, attachments: list):
    db = SessionLocal()
    try:
        # 공지사항 생성
        notice = Notice(
            title=title,
            content=content,
            author_id=author_id,
            category="general",
            is_published=True,
            published_at=datetime.utcnow()
        )
        db.add(notice)
        db.flush()

        # 첨부파일 추가
        for attachment_data in attachments:
            attachment = NoticeAttachment(
                notice_id=notice.id,
                filename=attachment_data["filename"],
                original_filename=attachment_data["original_filename"],
                file_size=attachment_data["file_size"],
                mime_type=attachment_data["mime_type"],
                file_path=attachment_data["file_path"]
            )
            db.add(attachment)

        db.commit()
        return notice
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

# 공지사항 목록 조회 (페이지네이션)
def get_notices_paginated(page: int = 1, per_page: int = 10, category: str = None):
    db = SessionLocal()
    try:
        query = db.query(Notice).filter(Notice.is_published == True)

        if category:
            query = query.filter(Notice.category == category)

        # 고정 공지사항을 먼저, 그 다음 발행일 순으로 정렬
        query = query.order_by(Notice.is_pinned.desc(), Notice.published_at.desc())

        # 페이지네이션
        offset = (page - 1) * per_page
        notices = query.offset(offset).limit(per_page).all()
        total = query.count()

        return notices, total
    finally:
        db.close()
```

### 3.3 신청서 관리
```python
# 신청서 제출
from app.models.application import Application, ApplicationStatus, ApplicationAttachment

def submit_application(applicant_id: int, title: str, description: str, application_data: dict):
    db = SessionLocal()
    try:
        application = Application(
            applicant_id=applicant_id,
            title=title,
            description=description,
            application_type="business_proposal",
            status=ApplicationStatus.SUBMITTED,
            application_data=application_data,
            submitted_at=datetime.utcnow()
        )
        db.add(application)
        db.commit()
        return application
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

# 신청서 검토
def review_application(application_id: int, reviewer_id: int, status: ApplicationStatus, notes: str):
    db = SessionLocal()
    try:
        application = db.query(Application).filter(Application.id == application_id).first()
        if application:
            application.reviewer_id = reviewer_id
            application.status = status
            application.review_notes = notes
            application.reviewed_at = datetime.utcnow()
            db.commit()
            return application
        return None
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()
```

## 4. 고급 쿼리 패턴

### 4.1 복잡한 조인 쿼리
```python
# 사용자별 신청서 통계
def get_user_application_stats(user_id: int):
    db = SessionLocal()
    try:
        from sqlalchemy import func

        stats = db.query(
            func.count(Application.id).label('total_applications'),
            func.count(case([(Application.status == ApplicationStatus.APPROVED, 1)])).label('approved_count'),
            func.count(case([(Application.status == ApplicationStatus.REJECTED, 1)])).label('rejected_count'),
            func.avg(Application.budget_requested).label('avg_budget')
        ).filter(Application.applicant_id == user_id).first()

        return {
            'total_applications': stats.total_applications,
            'approved_count': stats.approved_count,
            'rejected_count': stats.rejected_count,
            'avg_budget': float(stats.avg_budget) if stats.avg_budget else 0
        }
    finally:
        db.close()

# 회사 관계 네트워크 조회
def get_company_network(company_id: int, depth: int = 2):
    db = SessionLocal()
    try:
        # 직접 연결된 회사들
        direct_relations = db.query(CompanyRelationship).filter(
            CompanyRelationship.company_id == company_id,
            CompanyRelationship.is_active == True
        ).all()

        # 2차 연결 회사들 (필요시 재귀 쿼리로 확장 가능)
        related_company_ids = [rel.related_company_id for rel in direct_relations if rel.related_company_id]

        if related_company_ids and depth > 1:
            indirect_relations = db.query(CompanyRelationship).filter(
                CompanyRelationship.company_id.in_(related_company_ids),
                CompanyRelationship.is_active == True
            ).all()
        else:
            indirect_relations = []

        return {
            'direct_relations': direct_relations,
            'indirect_relations': indirect_relations
        }
    finally:
        db.close()
```

### 4.2 전문 검색 쿼리
```python
# 공지사항 전문 검색
def search_notices(search_term: str, page: int = 1, per_page: int = 10):
    db = SessionLocal()
    try:
        from sqlalchemy import func, text

        # PostgreSQL의 전문 검색 기능 사용
        search_query = db.query(Notice).filter(
            Notice.is_published == True,
            func.to_tsvector('korean', Notice.title + ' ' + Notice.content).match(search_term)
        ).order_by(
            func.ts_rank(
                func.to_tsvector('korean', Notice.title + ' ' + Notice.content),
                func.plainto_tsquery('korean', search_term)
            ).desc()
        )

        offset = (page - 1) * per_page
        notices = search_query.offset(offset).limit(per_page).all()
        total = search_query.count()

        return notices, total
    finally:
        db.close()
```
```
