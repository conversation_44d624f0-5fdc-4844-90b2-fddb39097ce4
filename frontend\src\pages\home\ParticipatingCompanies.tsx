import React, { useState } from 'react';
import { participatingCompanies, getMajorCompanies, getStartups, getResearchInstitutions, type Company } from '@/data/companiesData';
import { ExternalLink, Building2, Lightbulb, Rocket, GraduationCap } from 'lucide-react';

interface ParticipatingCompaniesProps {
  className?: string;
}

const ParticipatingCompanies: React.FC<ParticipatingCompaniesProps> = ({ className = '' }) => {
  const [selectedCategory, setSelectedCategory] = useState<'all' | Company['category']>('all');

  const getFilteredCompanies = () => {
    switch (selectedCategory) {
      case 'major':
        return getMajorCompanies();
      case 'startup':
        return getStartups();
      case 'research':
        return getResearchInstitutions();
      default:
        return participatingCompanies;
    }
  };

  const getCategoryIcon = (category: Company['category']) => {
    switch (category) {
      case 'major':
        return <Building2 className="w-4 h-4" />;
      case 'startup':
        return <Rocket className="w-4 h-4" />;
      case 'research':
        return <GraduationCap className="w-4 h-4" />;
      default:
        return <Lightbulb className="w-4 h-4" />;
    }
  };

  const getCategoryLabel = (category: Company['category']) => {
    switch (category) {
      case 'major':
        return '대기업';
      case 'startup':
        return '스타트업';
      case 'research':
        return '연구기관';
      default:
        return '기타';
    }
  };

  const filteredCompanies = getFilteredCompanies();

  return (
    <section className={`py-16 px-4 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto">
        {/* 섹션 헤더 */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            참여 기업 및 기관
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ETRI 딥테크 밸리 생태계에 참여하는 다양한 기업과 연구기관들을 만나보세요
          </p>
        </div>

        {/* 카테고리 필터 */}
        <div className="flex justify-center mb-12">
          <div className="flex flex-wrap gap-2 p-1 bg-gray-100 rounded-xl">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                selectedCategory === 'all'
                  ? 'bg-etri-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white'
              }`}
            >
              전체 ({participatingCompanies.length})
            </button>
            <button
              onClick={() => setSelectedCategory('major')}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                selectedCategory === 'major'
                  ? 'bg-etri-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white'
              }`}
            >
              {getCategoryIcon('major')}
              대기업 ({getMajorCompanies().length})
            </button>
            <button
              onClick={() => setSelectedCategory('startup')}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                selectedCategory === 'startup'
                  ? 'bg-etri-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white'
              }`}
            >
              {getCategoryIcon('startup')}
              스타트업 ({getStartups().length})
            </button>
            <button
              onClick={() => setSelectedCategory('research')}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                selectedCategory === 'research'
                  ? 'bg-etri-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white'
              }`}
            >
              {getCategoryIcon('research')}
              연구기관 ({getResearchInstitutions().length})
            </button>
          </div>
        </div>

        {/* 기업 로고 그리드 */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-6">
          {filteredCompanies.map((company) => (
            <div
              key={company.id}
              className="group relative bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg hover:border-etri-blue-200 transition-all duration-300 cursor-pointer"
              onClick={() => company.website && window.open(company.website, '_blank')}
            >
              {/* 로고 이미지 */}
              <div className="flex items-center justify-center h-16 mb-4">
                <img
                  src={company.logo}
                  alt={`${company.name} 로고`}
                  className="max-w-full max-h-full object-contain grayscale group-hover:grayscale-0 transition-all duration-300"
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `<div class="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center text-gray-400 text-xs font-medium">${company.name}</div>`;
                    }
                  }}
                />
              </div>

              {/* 기업명 */}
              <div className="text-center">
                <h3 className="text-sm font-medium text-gray-900 group-hover:text-etri-blue-600 transition-colors line-clamp-2">
                  {company.name}
                </h3>
                <p className="text-xs text-gray-500 mt-1">
                  {getCategoryLabel(company.category)}
                </p>
              </div>

              {/* 외부 링크 아이콘 */}
              {company.website && (
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-etri-blue-600" />
                </div>
              )}

              {/* 호버 오버레이 */}
              <div className="absolute inset-0 bg-etri-blue-50 opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300" />
            </div>
          ))}
        </div>

        {/* 참여 문의 CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-etri-blue-50 to-etri-orange-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              딥테크 밸리에 참여하고 싶으신가요?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              혁신적인 기술과 아이디어를 가진 기업이라면 누구나 ETRI 딥테크 밸리 생태계에 참여할 수 있습니다.
            </p>
            <button className="inline-flex items-center px-8 py-3 bg-etri-blue-600 text-white font-semibold rounded-xl hover:bg-etri-blue-700 transition-colors duration-300 shadow-lg hover:shadow-xl">
              <span>참여 문의하기</span>
              <ExternalLink className="w-5 h-5 ml-2" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ParticipatingCompanies;
