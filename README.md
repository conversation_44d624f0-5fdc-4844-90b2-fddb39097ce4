# DeepTech Valley Platform

> 밸리 내 기업 소통, 공모, 선정을 지원하는 통합 오픈플랫폼

## 📋 프로젝트 개요

**DeepTech Valley Platform**은 딥테크 밸리 내 기업들의 소통과 협력을 지원하는 통합 플랫폼입니다. 기업 간 네트워킹, 사업 공모 및 선정 과정을 디지털화하여 효율적인 에코시스템을 구축합니다.

### 🎯 주요 목표
- 밸리 내 기업 간 소통 및 협력 촉진
- 사업 공모 및 선정 과정의 디지털화
- 에코시스템 시각화를 통한 관계 파악
- 기관용 고품질 관리 시스템 제공

## 🛠 기술 스택

### Frontend
- **React 18+** + TypeScript + Vite
- **ShadcnUI** + TailwindCSS
- **React Flow** (에코시스템 맵) + Chart.js (통계)
- **Framer Motion** (애니메이션)
- **React Query** + React Hook Form

### Backend
- **Python 3.11+** + FastAPI
- **SQLAlchemy** (ORM) + Alembic (마이그레이션)
- **JWT** 인증 + RBAC 권한 관리
- **pandas** (데이터 분석) + ReportLab (PDF 생성)
- **Celery** + Redis (백그라운드 작업)

## 🚀 주요 기능

### 1차 개발 (핵심 기능)
- **회원관리**: 개인/기업/기관/관리자 4단계 권한 시스템
- **사업소개**: Wiki 형태 콘텐츠 관리, 권한 기반 접근 제어
- **공지관리**: 파일 첨부 지원 게시판 시스템
- **수요관리**: 폼 기반 사업신청, PDF 자동 생성
- **기본 통계**: 참여 현황 대시보드

### 2차 개발 (고급 기능)
- **에코시스템 맵**: React Flow 기반 인터랙티브 기업 관계도
- **밸리관리**: 기업/기관 정보 관리 시스템
- **고급 통계**: 참여기업 분석 및 시각화
- **메인페이지**: 애니메이션 기반 인터랙티브 맵

### 3차 개발 (최적화 & 완성)
- **반응형 웹**: 모바일 최적화 및 크로스 브라우저 지원
- **성능 최적화**: 코드 분할, 캐싱, 번들 최적화
- **접근성**: WCAG 2.1 AA 수준 준수
- **보안 강화**: OWASP Top 10 대응
- **백업 시스템**: 자동 백업 및 복구 메커니즘

## 📁 프로젝트 구조

```
deeptech-valley-platform/
├── frontend/                 # React + TypeScript 프론트엔드
│   ├── src/
│   │   ├── components/       # 재사용 가능한 컴포넌트
│   │   ├── pages/           # 페이지 컴포넌트
│   │   ├── hooks/           # 커스텀 훅
│   │   ├── services/        # API 서비스
│   │   └── utils/           # 유틸리티 함수
│   └── package.json
├── backend/                  # Python + FastAPI 백엔드
│   ├── app/
│   │   ├── api/             # API 라우터
│   │   ├── models/          # SQLAlchemy 모델
│   │   ├── schemas/         # Pydantic 스키마
│   │   ├── services/        # 비즈니스 로직
│   │   └── core/            # 핵심 설정
│   └── requirements.txt
├── docs/                     # 프로젝트 문서
│   ├── prd_draft.md         # 제품 요구사항 정의서
│   ├── tech_spec.md         # 기술 명세서
│   ├── api_specification.md # API 명세서
│   ├── development_plan.md  # 개발 계획서
│   └── task_status_tracking.md # 구현 상태 추적
└── README.md
```

## 📅 개발 일정

### 1차 개발 (Month 1) - 핵심 기능
- **Week 1**: 프로젝트 설정, DB 모델, 인증 시스템
- **Week 2**: 사용자 관리 시스템
- **Week 3**: 공지관리, 수요관리, PDF 생성
- **Week 4**: 통계 시스템, 사업소개 모듈

### 2차 개발 (Month 2) - 고급 시각화
- **Week 5-6**: React Flow 에코시스템 맵, 밸리관리 API
- **Week 7-8**: 인터랙티브 기능, 통합 인터페이스

### 3차 개발 (Month 3) - 최적화 & 완성
- **Week 9-10**: 반응형 최적화, 성능 개선
- **Week 11-12**: 보안 강화, 테스트, 배포 준비

## 🚀 시작하기

### 개발 환경 요구사항
- Node.js 18+
- Python 3.11+
- Git

### 설치 및 실행

#### 프론트엔드
```bash
cd frontend
npm install
npm run dev
```

#### 백엔드
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

## 📊 개발 현황

- **총 작업 수**: 29개 (완료 5개 + 진행 예정 24개)
- **현재 진행률**: 21% (6/29 작업 완료)
- **완료된 작업**:
  - ✅ shrimp-rules.md 문서 업데이트 완료
  - ✅ 작업 관리 자동화 시스템 구현
  - ✅ 데이터베이스 설계 문서 작성 완료
  - ✅ SQLAlchemy 모델 구현 가이드 작성 완료
  - ✅ Shrimp 작업 참고 문서 가이드 작성 완료
  - ✅ **프로젝트 초기 설정 및 개발 환경 구축** (방금 완료!)
- **다음 작업**: 데이터베이스 모델 설계 및 마이그레이션 시스템 구축

## 📚 문서

- [제품 요구사항 정의서 (PRD)](docs/prd_draft.md)
- [기술 명세서](docs/tech_spec.md)
- [API 명세서](docs/api_specification.md)
- [UI 설계 명세서](docs/ui_design_spec.md)
- [개발 계획서](docs/development_plan.md)
- [작업 분해서](docs/task_breakdown.md)
- [구현 가이드](docs/implementation_guide.md)
- [구현 상태 추적](docs/task_status_tracking.md)
- [프로젝트 종합 요약](docs/project_summary.md)

## 🤝 기여하기

1. 이 저장소를 포크합니다
2. 새로운 기능 브랜치를 생성합니다 (`git checkout -b feature/amazing-feature`)
3. 변경사항을 커밋합니다 (`git commit -m 'Add some amazing feature'`)
4. 브랜치에 푸시합니다 (`git push origin feature/amazing-feature`)
5. Pull Request를 생성합니다

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다. 자세한 내용은 [LICENSE](LICENSE) 파일을 참조하세요.

## 📞 연락처

프로젝트에 대한 문의사항이 있으시면 이슈를 생성해 주세요.

---

**DeepTech Valley Platform** - 딥테크 생태계의 디지털 혁신을 이끌어갑니다. 🚀
