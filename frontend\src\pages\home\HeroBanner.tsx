import React from 'react';

interface HeroBannerProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

/**
 * ETRI 브랜드 색상을 활용한 그라데이션 Hero 배너 컴포넌트
 * 메인페이지 상단에 표시되는 주요 제목과 부제목을 포함
 */
const HeroBanner: React.FC<HeroBannerProps> = ({
  title = "딥테크 스케일업 밸리 육성",
  subtitle = "AI, IoT, 차세대 반도체 등 핵심 신기술 기반",
  className = ""
}) => {
  return (
    <section className={`w-full bg-gradient-to-r from-etri-blue-600 to-etri-orange-500 py-20 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 text-center text-white">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          {title}
        </h1>
        <p className="text-xl md:text-2xl opacity-90">
          {subtitle}
        </p>
      </div>
    </section>
  );
};

export default HeroBanner;
