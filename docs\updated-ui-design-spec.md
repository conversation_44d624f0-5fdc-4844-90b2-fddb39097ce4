# DeepTech Valley Platform UI 설계 명세서

## 1. 디자인 시스템

### 1.1 컬러 팔레트 (ETRI 기반 전문 디자인)

#### Primary Colors - ETRI 기반 블루/오렌지 계열
```css
/* ETRI Blue - 신뢰감과 전문성 */
--etri-blue-50: #eff6ff;
--etri-blue-100: #dbeafe;
--etri-blue-500: #1c5aa0;   /* ETRI 메인 블루 (G102 B165 기준) */
--etri-blue-600: #1e4a8c;
--etri-blue-700: #1a3f78;
--etri-blue-900: #0f2852;

/* ETRI Orange - 혁신과 활력 */
--etri-orange-50: #fff7ed;
--etri-orange-100: #ffedd5;
--etri-orange-500: #f55a2c;  /* ETRI 메인 오렌지 (R241 G90 B34 기준) */
--etri-orange-600: #ea580c;
--etri-orange-700: #c2410c;
--etri-orange-900: #7c2d12;

/* Neutral Colors - 세련된 그레이 계열 */
--gray-50: #f8fafc;
--gray-100: #f1f5f9;
--gray-200: #e2e8f0;
--gray-300: #cbd5e1;
--gray-400: #94a3b8;
--gray-500: #64748b;
--gray-600: #475569;
--gray-700: #334155;
--gray-800: #1e293b;
--gray-900: #0f172a;

/* Status Colors */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #06b6d4;
```

#### 컬러 사용 가이드
- **Primary Blue**: 헤더, 네비게이션, 주요 버튼, 로고
- **Primary Orange**: 액센트, CTA 버튼, 중요 알림, 강조 요소
- **Gray Scale**: 텍스트, 배경, 보조 UI 요소
- **Status Colors**: 알림, 상태 표시, 피드백

### 1.2 타이포그래피
```css
/* 폰트 패밀리 */
--font-sans: 'Pretendard', 'Noto Sans KR', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
--font-mono: 'JetBrains Mono', 'Fira Code', 'D2Coding', monospace;

/* 폰트 크기 */
--text-xs: 0.75rem;     /* 12px - 캡션, 메타데이터 */
--text-sm: 0.875rem;    /* 14px - 보조 텍스트, 라벨 */
--text-base: 1rem;      /* 16px - 기본 본문 */
--text-lg: 1.125rem;    /* 18px - 큰 본문, 리드 텍스트 */
--text-xl: 1.25rem;     /* 20px - 소제목 */
--text-2xl: 1.5rem;     /* 24px - 섹션 제목 */
--text-3xl: 1.875rem;   /* 30px - 페이지 제목 */
--text-4xl: 2.25rem;    /* 36px - 메인 제목 */

/* 폰트 굵기 */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;

/* 행간 */
--leading-tight: 1.25;
--leading-normal: 1.5;
--leading-relaxed: 1.625;
```

### 1.3 간격 시스템 (8px Grid)
```css
/* 8px 기반 스페이싱 시스템 */
--space-0: 0;           /* 0px */
--space-1: 0.25rem;     /* 4px */
--space-2: 0.5rem;      /* 8px */
--space-3: 0.75rem;     /* 12px */
--space-4: 1rem;        /* 16px */
--space-5: 1.25rem;     /* 20px */
--space-6: 1.5rem;      /* 24px */
--space-8: 2rem;        /* 32px */
--space-10: 2.5rem;     /* 40px */
--space-12: 3rem;       /* 48px */
--space-16: 4rem;       /* 64px */
--space-20: 5rem;       /* 80px */
--space-24: 6rem;       /* 96px */
```

## 2. 레이아웃 구조

### 2.1 전체 레이아웃 (기관용 표준)
```
┌─────────────────────────────────────────────────────┐
│  Header                                             │
│  ┌─────────┐  Navigation Menu    User Profile    │
│  │ ETRI    │  [사업소개] [공지사항] [밸리관리]     │
│  │ LOGO    │  [수요관리] [사업신청]         [👤] │
│  └─────────┘                                       │
├─────────────────────────────────────────────────────┤
│                                                     │
│  ┌─────────────┐  ┌─────────────────────────────┐  │
│  │             │  │                             │  │
│  │  Sidebar    │  │        Main Content         │  │
│  │             │  │                             │  │
│  │ • 메뉴1     │  │  ┌─────────────────────────┐ │  │
│  │ • 메뉴2     │  │  │     Content Area        │ │  │
│  │ • 메뉴3     │  │  │                         │ │  │
│  │             │  │  │                         │ │  │
│  │             │  │  └─────────────────────────┘ │  │
│  │             │  │                             │  │
│  └─────────────┘  └─────────────────────────────┘  │
│                                                     │
├─────────────────────────────────────────────────────┤
│  Footer                                             │
│  © 2025 ETRI. All rights reserved.                 │
└─────────────────────────────────────────────────────┘
```

### 2.2 반응형 브레이크포인트
```css
/* Mobile First 접근법 */
/* Mobile */
@media (min-width: 320px) { /* 320px ~ 767px */ }

/* Tablet */
@media (min-width: 768px) { /* 768px ~ 1023px */ }

/* Desktop */
@media (min-width: 1024px) { /* 1024px ~ 1439px */ }

/* Large Desktop */
@media (min-width: 1440px) { /* 1440px+ */ }

/* Ultra Wide */
@media (min-width: 1920px) { /* 1920px+ */ }
```

## 3. 주요 화면별 설계

- **설계는 참고만 해주세요 **

### 3.1 메인 페이지 (React Flow 에코시스템 맵)
```
┌───────────────────────────────────────────────────────────┐
│  ETRI 딥테크 오픈플랫폼                                    │
│  [사업소개] [공지사항] [밸리관리] [수요관리]          [👤] │
├───────────────────────────────────────────────────────────┤
│                                                           │
│           딥테크 스케일업 밸리 육성                        │
│       AI, IoT, 차세대 반도체 등 핵심 신기술 기반           │
│                                                           │
│  ┌─────────────────────────────────────────────────────┐  │
│  │         Interactive React Flow Ecosystem Map       │  │
│  │                                                     │  │
│  │      🏢KT ────── 🏛️ETRI ────── 🏢삼성              │  │
│  │       │           │             │                  │  │
│  │    🏢LG ─────── 👥개인 ─────── 🏢SK                │  │
│  │       │        연구자         │                    │  │
│  │    🏛️국가기관 ────────────── 🏢기타기업            │  │
│  │                                                     │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                           │
│  ┌─────────────────────────────────────────────────────┐  │
│  │                   주요 통계                          │  │
│  │  📊 1,200+     📈 500+      🔬 3,450+    🎯 800+   │  │
│  │  등록된 기업    참여 기관    연구 프로젝트  기술 특허   │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                           │
└───────────────────────────────────────────────────────────┘
```

### 3.2 로그인 페이지
```
┌─────────────────────────────────────────────┐
│                                             │
│  ┌─────────────────────────────────────────┐ │
│  │            ETRI 로고                    │ │
│  │        딥테크 오픈플랫폼                │ │
│  └─────────────────────────────────────────┘ │
│                                             │
│  ┌─────────────────────────────────────────┐ │
│  │              로그인                     │ │
│  │                                         │ │
│  │  사용자 구분                            │ │
│  │  ○ 개인  ○ 기업  ○ 기관               │ │
│  │                                         │ │
│  │  이메일 주소                            │ │
│  │  [<EMAIL>          📧] │ │
│  │                                         │ │
│  │  비밀번호                               │ │
│  │  [••••••••                        👁] │ │
│  │                                         │ │
│  │  ☐ 비밀번호 찾기    계정이 없으신가요?   │ │
│  │                                         │ │
│  │  [        로그인        ]              │ │
│  │                                         │ │
│  └─────────────────────────────────────────┘ │
│                                             │
└─────────────────────────────────────────────┘
```

### 3.3 사업신청 페이지
```
┌───────────────────────────────────────────────────────────┐
│  사업신청                                                 │
├───────────────────────────────────────────────────────────┤
│                                                           │
│  ┌─ 사업신청 ─────────────────────────────────────────┐   │
│  │                                                    │   │
│  │  📋 기업 정보                                       │   │
│  │  ┌──────────────────────────────────────────────┐  │   │
│  │  │ 사업명    [                              ] │  │   │
│  │  │ 담당자    [                              ] │  │   │
│  │  │ 소속기업  [                              ] │  │   │
│  │  │ 연락처    [010-XXXX-XXXX                ] │  │   │
│  │  │ 이메일    [<EMAIL>          ] │  │   │
│  │  │ 기업분야  [AI/ML                    ▼] │  │   │
│  │  └──────────────────────────────────────────────┘  │   │
│  │                                                    │   │
│  │  📝 사업 계획                                       │   │
│  │  ┌──────────────────────────────────────────────┐  │   │
│  │  │ 사업명    [                              ] │  │   │
│  │  │           [                              ] │  │   │
│  │  │                                            │  │   │
│  │  │ 기술 설명  [                              ] │  │   │
│  │  │           [                              ] │  │   │
│  │  │                                            │  │   │
│  │  │ 기대 효과  [                              ] │  │   │
│  │  │           [                              ] │  │   │
│  │  └──────────────────────────────────────────────┘  │   │
│  │                                                    │   │
│  │  📎 첨부 파일                                       │  │
│  │  ┌──────────────────────────────────────────────┐  │   │
│  │  │ 사업 계획서 [     파일 업로드     ] (2MB 미만) │  │   │
│  │  │ 재무 계획서 [     파일 업로드     ] (2MB 미만) │  │   │
│  │  │ 기타 자료   [     파일 업로드     ] (2MB 미만) │  │   │
│  │  └──────────────────────────────────────────────┘  │   │
│  │                                                    │   │
│  │  ┌──────────────────────────────────────────────┐  │   │
│  │  │ 첨부 파일 목록                               │  │   │
│  │  │ • 사업계획서_최종.pdf (1.8MB)                │  │   │
│  │  │ • 재무계획서.xlsx (0.5MB)                    │  │   │
│  │  └──────────────────────────────────────────────┘  │   │
│  │                                                    │   │
│  │  [임시저장]                          [제출하기]    │   │
│  │                                                    │   │
│  └────────────────────────────────────────────────────┘   │
│                                                           │
└───────────────────────────────────────────────────────────┘
```

### 3.4 수요 공모 통계 페이지
```
┌───────────────────────────────────────────────────────────┐
│  사업신청 > 수요 공모 통계 현황                            │
├───────────────────────────────────────────────────────────┤
│                                                           │
│  ┌─ 수요 공모 통계 현황 ─────────────────────────────────┐ │
│  │                                                      │ │
│  │  💰 총지원 건수     📊 승인률 현황                     │ │
│  │     500건             300건                         │ │
│  │   ▲ +15% (지난 대비)   ▲ +5% (지난 분기 대비)          │ │
│  │                                                      │ │
│  │  📈 지원 신청 현황     🏆 선정 현황                    │ │
│  │     150건             120개                         │ │
│  │   ▼ -5% (지난 대비)    ▲ +20% (vs 작년)             │ │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
│  ┌─ 월별 신청 트렌드 ────────────────────────────────────┐ │
│  │  최근 12개월 동안 신청 동향                           │ │
│  │                                                      │ │
│  │  신청 건수                                            │ │
│  │    260                                  ╭─╮           │ │
│  │    240                             ╭─╮  │ │           │ │
│  │    220                        ╭─╮  │ │  │ │      ╭─╮  │ │
│  │    200                   ╭─╮  │ │  │ │  │ │ ╭─╮  │ │  │ │
│  │    180              ╭─╮  │ │  │ │  │ │  │ │ │ │  │ │  │ │
│  │    160         ╭─╮  │ │  │ │  │ │  │ │  │ │ │ │  │ │  │ │
│  │    140    ╭─╮  │ │  │ │  │ │  │ │  │ │  │ │ │ │  │ │  │ │
│  │       1월  2월  3월  4월  5월  6월  7월  8월  9월  10월 11월 12월 │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
│  ┌─ 신청 현황 분포 ──────────────────────────────────────┐ │
│  │  연령 상태별 신청 현황                                │ │
│  │                                                      │ │
│  │  ●●●●●●●●●●●● 기업별정리연구원                      │ │
│  │  ●●●●●●●●● 한국소프트시스                            │ │
│  │  ●●●●●●● 더블유컴즈                                 │ │
│  │                                                      │ │
│  │  기업 분야별 신청                                     │ │
│  │  ┌────────────────────────────────────────────────┐  │ │
│  │  │ 인공지능 ████████████████████  80%            │  │ │
│  │  │ 바이오테크 ████████████  50%                  │  │ │
│  │  │ 양자 기술 ████████  35%                       │  │ │
│  │  │ 로봇 공학 ████  20%                           │  │ │
│  │  │ 신소재 ██  10%                               │  │ │
│  │  │ 기타 ██  8%                                  │  │ │
│  │  └────────────────────────────────────────────────┘  │ │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
└───────────────────────────────────────────────────────────┘
```

### 3.5 공지사항 페이지
```
┌───────────────────────────────────────────────────────────┐
│  공지사항                                                 │
├───────────────────────────────────────────────────────────┤
│                                                           │
│  ┌─ 주요 공고 ───────────────────────────────────────────┐ │
│  │                                                      │ │
│  │  [공지] AI 기반 데이터 분석 개발 파트너 모집           │ │
│  │  ENTRI 기반의 데이터 솔루션을 활용한 개발 파트너를 신청 │ │
│  │  마감: 2024-07-25    조회수: 1,260                    │ │
│  │                                                      │ │
│  │  [공지] AI 기반 데이터 분석 개발 파트너 모집           │ │
│  │  ENTRI 기반의 데이터 솔루션을 활용한 개발 파트너를 신청 │ │
│  │  마감: 2024-07-25    조회수: 1,260                    │ │
│  │                                                      │ │
│  │  [공지] AI 기반 데이터 분석 개발 파트너 모집           │ │
│  │  ENTRI 기반의 데이터 솔루션을 활용한 개발 파트너를 신청 │ │
│  │  마감: 2024-07-25    조회수: 1,260                    │ │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
│  ┌─ 사업공고 ────────────────────────────────────────────┐ │
│  │                  [+ 새 공고 작성]                      │ │
│  │                                                      │ │
│  │  제목, 내용 검색    선택 검색      📊 필터 설정         │ │
│  │                                                      │ │
│  │  ┌─┬─────────────────────────┬────────┬──────┬──────┐ │ │
│  │  │순│         제목           │  분류  │ 작성일│ 조회수│ │ │
│  │  ├─┼─────────────────────────┼────────┼──────┼──────┤ │ │
│  │  │17│양자 시스템 일정표적 학습공지  │ 시스템 │2024-06-12│880 │ │ │
│  │  │16│메타버스 컨텐츠 개발 워터 파트너 구축│컨텐츠 │2024-06-15│920 │ │ │
│  │  │15│차세대 반도체 AI IoT 디바이스 개발│ 하드웨어│2024-06-18│760 │ │ │
│  │  │14│클라우드 인이 기반 디지털 엠베이커 │클라우드│2024-06-20│990 │ │ │
│  │  │13│AI 틀러닝설 상화 특화인 컴퓨터 안정시│ 컴퓨터│2024-06-22│1050│ │ │
│  │  │12│전통 데이터 기술 심층히 행경 메이커│ 데이터│2024-06-25│650 │ │ │
│  │  │11│클라우드 기업 반온 융플 개발 프로젝트│클라우드│2024-06-28│720 │ │ │
│  │  │10│정보보안이해올 테피 연세 │ 보안 │2024-06-30│1600 │ │ │
│  │  │ 9│자동 스기 기술 증강 하라 개발 보급│ 연구소│2024-07-03│810 │ │ │
│  │  │ 8│2024년 딥테크 동허 쉐어 지원 프로젝트│기술분야│2024-07-05│1300│ │ │
│  │  └─┴─────────────────────────┴────────┴──────┴──────┘ │ │
│  │                                                      │ │
│  │              ← 이전   1  2  3  다음 →                 │ │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
└───────────────────────────────────────────────────────────┘
```

### 3.6 기술 소스코드 브라우징 페이지
```
┌───────────────────────────────────────────────────────────┐
│  사업소개 > 기술소개 > 기술 소스코드                       │
├───────────────────────────────────────────────────────────┤
│                                                           │
│  ┌─ 기술소개 ─┬─ 기술 소스코드 ──────────────────────────┐ │
│  │ • 사업소개 │                                        │ │
│  │ ▼ 기술소개 │  양자 딥러닝 울브시의 근도를 이용한 핵심 기술의 │ │
│  │   • 기술 특성 #1│  소스코드를 텍시에 이해예상.      │ │
│  │   • 기술 특성 #2│                                  │ │
│  │   • 기술 특성 #3│  코드 전체 컨텍            필터   │ │
│  │             │                                        │ │
│  │             │  QuantumAIProcessors    TypeScript    │ │
│  │             │  양자 합당스와 의 몇출 핵심 에이스 응곤  │ │
│  │             │  ┌──────────────────────────────────┐ │ │
│  │             │ 1│import numpy as np               │ │ │
│  │             │ 2│from tensorflow import keras     │ │ │
│  │             │ 3│import qiskit                    │ │ │
│  │             │ 4│                                 │ │ │
│  │             │ 5│def create_model(input_shape):    │ │ │
│  │             │ 6│    model = keras.Sequential([   │ │ │
│  │             │ 7│        layers.Input(shape=input_shape),│ │ │
│  │             │ 8│        layers.Conv2D(32, kernel_size=(3, 3), activation='relu'),│ │ │
│  │             │ 9│        layers.MaxPooling2D(pool_size=(2, 2)),│ │ │
│  │             │10│        layers.Conv2D(64, kernel_size=(3, 3), activation='relu'),│ │ │
│  │             │11│        layers.Flatten(),        │ │ │
│  │             │12│        layers.Dropout(0.5),     │ │ │
│  │             │13│        layers.Dense(128, activation='relu'),│ │ │
│  │             │14│        layers.Dense(10, activation='softmax')│ │ │
│  │             │15│    ])                           │ │ │
│  │             │...│return model                    │ │ │
│  │             │32│                                 │ │ │
│  │             │  └──────────────────────────────────┘ │ │
│  │             │                                        │ │
│  └─────────────┴────────────────────────────────────────┘ │
│                                                           │
│  ┌─ 소스코드 모듈 목록 ──────────────────────────────────┐ │
│  │                                                      │ │
│  │  [카드] Secure Blockchain Protocol    Solidity       │ │
│  │  보안 기업 기술을 훨씬 높고 센터 프로토콜입니다.         │ │
│  │  데이터 무렼성의 분슘내과 다결정과 보호되어있습니다.      │ │
│  │  파일: 3 개                작성일: 2024-03-10        │ │
│  │                            [소스 보기]              │ │
│  │                                                      │ │
│  │  [카드] Secure Blockchain Protocol    Solidity       │ │
│  │  [카드] Secure Blockchain Protocol    Solidity       │ │
│  │  [카드] Secure Blockchain Protocol    Solidity       │ │
│  │  [카드] Secure Blockchain Protocol    Solidity       │ │
│  │  [카드] Secure Blockchain Protocol    Solidity       │ │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
└───────────────────────────────────────────────────────────┘
```

### 3.7 밸리현황 - 혁신 기술 연구소 페이지
```
┌───────────────────────────────────────────────────────────┐
│  밸리현황 > 참여기업 > 혁신 기술 연구소                    │
├───────────────────────────────────────────────────────────┤
│                                                           │
│  ┌─ 밸리현황 ────────────────────────────────────────────┐ │
│  │ • 밸리소개                                            │ │
│  │ ▼ 참여기업                                            │ │
│  │   • 참여기관                                          │ │
│  │   • 참여연구소                                        │ │
│  └───────────────────────────────────────────────────────┘ │
│                                                           │
│  ┌─ 혁신 기술 연구소 ─────────────────────────────────────┐ │
│  │                                                      │ │
│  │  ⚡ 혁신 기술 연구소                                   │ │
│  │     딥테크 오픈 솔루션 전문기업                        │ │
│  │                                                      │ │
│  │  회사 소개                                            │ │
│  │  혁신 기술 연구는 일정지능, 빅데이터, 클라우드 컴퓨팅   │ │
│  │  기술을 전달하는 디지털 플랫폼을 구축하는 전문 기업입니다. │ │
│  │  대학계 우수 연구진과 전문팀이 연구진이 다양한 사업을   │ │
│  │  개별적으로 수행하고 있습니다.                          │ │
│  │                                                      │ │
│  │  비전 및 목표                                         │ │
│  │  기술 혁신을 통해 세상을 변화선 생태계를 구축하고, 글로벌 │ │
│  │  기술 랩저에서 저자의 실전환과 프덕션을 창출하는 것이    │ │
│  │  기업입니다.                                          │ │
│  │                                                      │ │
│  │  주요 사업 영역                                       │ │
│  │  🔹 인공지능 솔루션 개발                              │ │
│  │  🔹 빅데이터 분석 제품 구축                           │ │
│  │  🔹 클라우드 기반 서비스                              │ │
│  │  🔹 사세대 연구 개발                                  │ │
│  │                                                      │ │
│  │  ┌────────────────┬────────────────┬────────────────┐ │ │
│  │  │   블랙기지 가이드 │   공동 연구 프로젝트 │   협력 기회   │ │ │
│  │  │   블랙내 기  기업  │   진행 연구의 규모 수│  확장 가능성  │ │ │
│  │  │   가이드 흠록      │                   │             │ │ │
│  │  │                   │       A+          │    30+      │ │ │
│  │  │       A+          │   공동 연구 프로젝트 │   공되 기업  │ │ │
│  │  │   블랙내 기  기 명 │     수             │     수      │ │ │
│  │  │      명           │                   │             │ │ │
│  │  │                   │       125+        │             │ │ │
│  │  │                   │   우리 특허 확수   │             │ │ │
│  │  └────────────────┴────────────────┴────────────────┘ │ │
│  │                                                      │ │
│  │  최근 활동                                 협력 기회   │ │
│  │                                                      │ │
│  │  🔸 세계로 AI 실이 '왕자' 컴프값동 솔찰                │ │
│  │     더러                                🎯 개별기   │ │
│  │  🔸 빅데이터 분석 솔루단 '실품값' 컴프값                │ │
│  │     예년                                             │ │
│  │  🔸 사세대 온로 국의 기출 찜지외 게획                  │ │
│  │     우리                                 자세히 보기 > │ │ │
│  │                                                      │ │
│  │  🔸 지능형 자율주행 시스템 개발                       │ │
│  │     우리                                 협동 지원 분야 │ │
│  │                                          자세히 보기 > │ │ │
│  │                                                      │ │
│  └──────────────────────────────────────────────────────┘ │
│                                                           │
└───────────────────────────────────────────────────────────┘
```

## 4. 컴포넌트 설계

### 4.1 버튼 시스템
```typescript
// ETRI 브랜드 기반 버튼 변형
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'etri-blue' | 'etri-orange' | 'outline' | 'ghost' | 'destructive';
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

// 사용 예시
<Button variant="etri-blue" size="md" icon={<FileIcon />}>
  사업 신청
</Button>

<Button variant="etri-orange" size="lg">
  공모 참여
</Button>

<Button variant="outline" size="sm">
  더보기
</Button>
```

### 4.2 폼 컴포넌트 시스템
```typescript
// 기관용 폼 필드
<FormField>
  <FormLabel required>회사명</FormLabel>
  <FormInput 
    placeholder="정확한 회사명을 입력하세요"
    validationMessage="필수 입력 항목입니다"
  />
  <FormDescription>
    사업자등록증에 기재된 정확한 회사명을 입력해주세요
  </FormDescription>
</FormField>

// 다중 선택 필드
<FormField>
  <FormLabel>기업 규모</FormLabel>
  <FormRadioGroup>
    <FormRadioItem value="startup">스타트업</FormRadioItem>
    <FormRadioItem value="small">중소기업</FormRadioItem>
    <FormRadioItem value="medium">중견기업</FormRadioItem>
    <FormRadioItem value="large">대기업</FormRadioItem>
  </FormRadioGroup>
</FormField>

// 파일 업로드 컴포넌트
<FileUpload
  accept=".pdf,.doc,.docx"
  maxSize={2 * 1024 * 1024} // 2MB
  multiple={true}
  onUpload={handleFileUpload}
  progress={uploadProgress}
/>
```

### 4.3 데이터 표시 컴포넌트
```typescript
// 통계 카드
<StatCard>
  <StatIcon>📊</StatIcon>
  <StatNumber>1,200+</StatNumber>
  <StatLabel>등록 기업</StatLabel>
  <StatTrend trend="up" value="15%">
    지난 분기 대비
  </StatTrend>
</StatCard>

// 테이블 컴포넌트
<DataTable
  columns={[
    { key: 'name', header: '회사명', sortable: true },
    { key: 'type', header: '기업 유형', filterable: true },
    { key: 'status', header: '상태', render: StatusBadge }
  ]}
  data={companies}
  pagination={true}
  searchable={true}
  filters={['기업 유형', '지역', '사업 분야']}
/>

// 뱃지 컴포넌트
<Badge variant="success">승인</Badge>
<Badge variant="warning">검토중</Badge>
<Badge variant="error">반려</Badge>
<Badge variant="info">대기</Badge>
```

### 4.4 네비게이션 컴포넌트
```typescript
// 메인 네비게이션
<Navigation>
  <NavItem href="/business" icon={<BuildingIcon />}>
    사업소개
  </NavItem>
  <NavItem href="/notices" icon={<BellIcon />}>
    공지사항
  </NavItem>
  <NavItem href="/valley" icon={<MapIcon />}>
    밸리관리
  </NavItem>
  <NavItem href="/demands" icon={<ClipboardIcon />}>
    수요관리
  </NavItem>
</Navigation>

// 사이드바 네비게이션
<Sidebar>
  <SidebarSection title="사업 관리">
    <SidebarItem href="/business/intro">사업소개</SidebarItem>
    <SidebarItem href="/business/tech">기술소개</SidebarItem>
    <SidebarItem href="/business/guide">기술가이드</SidebarItem>
  </SidebarSection>
</Sidebar>

// 브레드크럼
<Breadcrumb>
  <BreadcrumbItem href="/">홈</BreadcrumbItem>
  <BreadcrumbItem href="/business">사업소개</BreadcrumbItem>
  <BreadcrumbItem current>기술소개</BreadcrumbItem>
</Breadcrumb>
```

## 5. 인터랙션 및 애니메이션

### 5.1 마이크로 인터랙션
```css
/* 버튼 호버 효과 */
.btn-etri-blue:hover {
  background: linear-gradient(135deg, var(--etri-blue-600), var(--etri-blue-700));
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(28, 90, 160, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 카드 호버 효과 */
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--etri-blue-200);
  transition: all 0.3s ease;
}

/* 포커스 링 */
.focus-ring:focus {
  outline: 2px solid var(--etri-blue-500);
  outline-offset: 2px;
  border-radius: 6px;
}
```

### 5.2 페이지 전환 애니메이션
```typescript
// Framer Motion 페이지 전환
const pageVariants = {
  initial: { opacity: 0, x: -20 },
  in: { opacity: 1, x: 0 },
  out: { opacity: 0, x: 20 }
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4
};

<motion.div
  initial="initial"
  animate="in"
  exit="out"
  variants={pageVariants}
  transition={pageTransition}
>
  {children}
</motion.div>
```

### 5.3 로딩 상태
```typescript
// 스켈레톤 로딩
<div className="space-y-4">
  <Skeleton className="h-4 w-3/4" />
  <Skeleton className="h-4 w-1/2" />
  <Skeleton className="h-32 w-full" />
</div>

// 프로그레스 바
<Progress value={uploadProgress} className="w-full" />

// 스피너
<Spinner size="md" color="etri-blue" />
```

## 6. 접근성 가이드라인

### 6.1 키보드 네비게이션
```typescript
// 키보드 접근성 개선
const KeyboardNavigation = {
  Tab: '다음 요소로 이동',
  'Shift + Tab': '이전 요소로 이동',
  Enter: '버튼/링크 활성화',
  Space: '체크박스/버튼 토글',
  'Arrow Keys': '라디오 버튼/메뉴 탐색',
  Escape: '모달/드롭다운 닫기'
};

// 스킵 링크
<SkipToContent href="#main-content">
  본문으로 바로가기
</SkipToContent>
```

### 6.2 ARIA 속성
```html
<!-- 폼 필드 -->
<label for="company-name">회사명 *</label>
<input 
  id="company-name"
  aria-required="true"
  aria-describedby="company-name-help"
  aria-invalid="false"
/>
<div id="company-name-help">
  사업자등록증에 기재된 정확한 회사명을 입력하세요
</div>

<!-- 버튼 -->
<button aria-label="파일 업로드" aria-describedby="upload-help">
  <UploadIcon />
</button>

<!-- 테이블 -->
<table role="table" aria-label="참여 기업 목록">
  <caption>총 150개 기업이 등록되어 있습니다</caption>
  <!-- ... -->
</table>
```

### 6.3 색상 대비 및 텍스트
```css
/* WCAG AA 준수 색상 대비 */
.text-primary {
  color: var(--etri-blue-700); /* 대비비 4.5:1 이상 */
}

.text-secondary {
  color: var(--gray-600); /* 대비비 4.5:1 이상 */
}

/* 색상에만 의존하지 않는 상태 표시 */
.status-approved::before {
  content: '✓';
  margin-right: 0.5rem;
}

.status-rejected::before {
  content: '✗';
  margin-right: 0.5rem;
}
```

## 7. 반응형 디자인

### 7.1 모바일 우선 설계
```css
/* Mobile First CSS */
.container {
  padding: 1rem;
  margin: 0 auto;
}

/* Tablet */
@media (min-width: 768px) {
  .container {
    padding: 2rem;
    max-width: 1200px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .container {
    padding: 3rem;
  }
}
```

### 7.2 모바일 네비게이션
```typescript
// 모바일 햄버거 메뉴
<MobileNavigation>
  <MobileNavToggle aria-label="메뉴 열기/닫기" />
  <MobileNavMenu>
    <MobileNavItem href="/business">사업소개</MobileNavItem>
    <MobileNavItem href="/notices">공지사항</MobileNavItem>
    <MobileNavItem href="/valley">밸리관리</MobileNavItem>
    <MobileNavItem href="/demands">수요관리</MobileNavItem>
  </MobileNavMenu>
</MobileNavigation>

// 하단 탭 네비게이션 (모바일)
<BottomTabNavigation>
  <BottomTab href="/" icon={<HomeIcon />}>홈</BottomTab>
  <BottomTab href="/notices" icon={<BellIcon />}>공지</BottomTab>
  <BottomTab href="/applications" icon={<ClipboardIcon />}>신청</BottomTab>
  <BottomTab href="/profile" icon={<UserIcon />}>마이페이지</BottomTab>
</BottomTabNavigation>
```

## 8. 다크모드 지원

### 8.1 다크모드 컬러 시스템
```css
[data-theme="dark"] {
  /* ETRI 다크모드 컬러 */
  --etri-blue-500: #4a90e2;
  --etri-orange-500: #ff7043;
  
  /* 배경 및 텍스트 */
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  
  /* 보더 및 링 */
  --border: #334155;
  --ring: #4a90e2;
}
```

### 8.2 테마 토글
```typescript
const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      aria-label="테마 변경"
    >
      {theme === 'light' ? <MoonIcon /> : <SunIcon />}
    </Button>
  );
};
```

## 9. 성능 최적화

### 9.1 이미지 최적화
```typescript
// Next.js Image 컴포넌트 활용
<Image
  src="/ecosystem-map.png"
  alt="딥테크 밸리 에코시스템 맵"
  width={800}
  height={600}
  priority={true}
  loading="eager"
  placeholder="blur"
/>

// 지연 로딩
<LazyImage
  src="/company-logo.png"
  alt="회사 로고"
  className="w-12 h-12"
  threshold={0.1}
/>
```

### 9.2 코드 분할
```typescript
// 페이지별 코드 분할
const EcosystemMapPage = lazy(() => import('@/pages/EcosystemMap'));
const ApplicationFormPage = lazy(() => import('@/pages/ApplicationForm'));
const StatisticsPage = lazy(() => import('@/pages/Statistics'));

// 컴포넌트 지연 로딩
<Suspense fallback={<PageSkeleton />}>
  <EcosystemMapPage />
</Suspense>
```

## 10. 구현 우선순위

### 10.1 1차 구현 (핵심 컴포넌트)
1. **기본 레이아웃**: Header, Sidebar, Footer
2. **폼 시스템**: Input, Select, Button, FileUpload
3. **네비게이션**: MainNav, Breadcrumb, Pagination
4. **데이터 표시**: Table, Card, Badge, Statistics

### 10.2 2차 구현 (고급 기능)
1. **인터랙티브 요소**: Modal, Dropdown, Tooltip
2. **차트 및 시각화**: React Flow 에코시스템 맵, Chart.js 통계
3. **애니메이션**: 페이지 전환, 마이크로 인터랙션
4. **접근성 개선**: ARIA 속성, 키보드 네비게이션

### 10.3 3차 구현 (최적화)
1. **성능 최적화**: 코드 분할, 이미지 최적화
2. **반응형 완성**: 모바일 네비게이션, 터치 인터페이스
3. **다크모드**: 테마 시스템, 토글 기능
4. **PWA 기능**: 오프라인 지원, 푸시 알림

이 UI 설계 명세서는 ETRI의 기존 디자인 언어를 존중하면서도 2025년 트렌드를 반영한 현대적이고 접근성이 뛰어난 사용자 인터페이스 구현을 위한 완전한 가이드입니다.
│  