# PRD Template (AI 개발 중심)

## 🎯 목적
Shrimp MCP의 `process_thought` + `plan_task` 결과를 AI가 개발할 때 참고할 수 있는 실용적인 PRD로 변환하는 템플릿입니다.

## 📋 PRD 구조 (AI 개발 중심)

### 헤더 정보
```markdown
# [프로젝트명] PRD

**생성일**: YYYY-MM-DD
**기술 스택**: [React, Node.js, PostgreSQL 등]
**Shrimp 분석 기반**: process_thought + plan_task 결과
**개발 기간**: [X주]
```

## 🎯 1. 프로젝트 핵심 정보

### 프로젝트 목표 (개발 관점)
```markdown
## 1. 프로젝트 핵심 정보

### 1.1 무엇을 만드는가?
**제품 설명**: [한 문장으로 명확히]
예: "대학생들이 중고 전공서적을 안전하게 거래할 수 있는 웹 플랫폼"

**핵심 가치**: [사용자가 얻는 주요 이익]
예: "학교 인증으로 신뢰할 수 있는 거래, 전공별 맞춤 검색"

### 1.2 주요 사용자
- **Primary**: [주요 사용자 그룹]
- **Secondary**: [부차적 사용자 그룹]
- **Admin**: [관리자 유형]

### 1.3 핵심 사용 시나리오 (3-5개)
1. **사용자 등록**: 학교 이메일로 회원가입 → 학생 인증
2. **책 등록**: 교재 정보 입력 → 사진 업로드 → 가격 설정
3. **책 검색**: 학교/전공 필터 → 검색 → 책 상세 정보 확인
4. **거래 진행**: 판매자 연락 → 만남 약속 → 거래 완료
5. **후기 작성**: 거래 후 상호 평가
```

## ⚡ 2. 핵심 기능 명세 (Epic 레벨)

### 기능 우선순위
```markdown
## 2. 핵심 기능 명세

### 2.1 MVP 필수 기능 (Phase 1)

#### 사용자 관리
- **회원가입/로그인**: 학교 이메일 인증, JWT 기반
- **프로필 관리**: 기본 정보, 학교/전공 정보
- **권한 관리**: 일반 사용자, 관리자

#### 도서 관리
- **도서 등록**: 제목, 저자, ISBN, 가격, 상태, 사진
- **도서 조회**: 검색, 필터링 (학교, 전공, 가격대)
- **도서 수정/삭제**: 본인 등록 도서만

#### 거래 기능
- **관심 표시**: 찜하기, 문의하기
- **연락 기능**: 간단한 메시지 교환
- **거래 상태**: 판매중, 예약중, 거래완료

### 2.2 고도화 기능 (Phase 2)
- **실시간 채팅**: Socket.io 기반
- **위치 기반 서비스**: 학교 내 만남 장소 추천
- **가격 제안**: 가격 협상 기능
- **후기 시스템**: 거래 후 평점/리뷰
- **알림 기능**: 관심 도서 가격 변동, 새 등록
```

### 기능별 상세 명세 (개발용)
```markdown
### 2.3 기능별 개발 명세

#### 사용자 인증
**API 엔드포인트**:
- `POST /auth/register` - 회원가입
- `POST /auth/login` - 로그인  
- `POST /auth/verify-email` - 이메일 인증

**데이터 모델**:
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  university: string;
  major: string;
  isVerified: boolean;
  createdAt: Date;
}
```

**프론트엔드 컴포넌트**:
- `RegisterForm` - 회원가입 폼
- `LoginForm` - 로그인 폼
- `EmailVerification` - 이메일 인증

#### 도서 관리
**API 엔드포인트**:
- `GET /books` - 도서 목록 조회
- `POST /books` - 도서 등록
- `GET /books/:id` - 도서 상세 조회
- `PUT /books/:id` - 도서 정보 수정
- `DELETE /books/:id` - 도서 삭제

**데이터 모델**:
```typescript
interface Book {
  id: string;
  title: string;
  author: string;
  isbn?: string;
  price: number;
  condition: 'new' | 'good' | 'fair' | 'poor';
  description: string;
  imageUrls: string[];
  sellerId: string;
  university: string;
  major: string;
  status: 'available' | 'reserved' | 'sold';
  createdAt: Date;
}
```

**프론트엔드 컴포넌트**:
- `BookList` - 도서 목록
- `BookCard` - 도서 카드
- `BookDetail` - 도서 상세
- `BookForm` - 도서 등록/수정 폼
- `SearchFilter` - 검색 및 필터
```

## 🛠 3. 기술 요구사항

### 기술 스택 및 제약사항
```markdown
## 3. 기술 요구사항

### 3.1 기술 스택
**프론트엔드**:
- React 18 + TypeScript
- 상태관리: React Query + Zustand
- 스타일: Tailwind CSS
- 라우팅: React Router v6

**백엔드**:
- Node.js + Express + TypeScript
- 데이터베이스: PostgreSQL + Prisma
- 인증: JWT + bcrypt
- 파일 업로드: Multer + AWS S3

**개발 도구**:
- 빌드: Vite
- 테스트: Jest + React Testing Library
- 린터: ESLint + Prettier

### 3.2 성능 요구사항
- **페이지 로딩**: 3초 이내
- **API 응답**: 500ms 이내
- **이미지 업로드**: 5MB 제한
- **동시 사용자**: 500명 지원

### 3.3 보안 요구사항
- HTTPS 필수
- JWT 토큰 인증
- 입력 데이터 검증
- 파일 업로드 보안 (이미지만 허용)
```

## 📊 4. 데이터 구조 및 API

### 주요 데이터 모델
```markdown
## 4. 데이터 구조 및 API

### 4.1 데이터베이스 스키마

#### 핵심 테이블
```sql
-- 사용자
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  university VARCHAR(100) NOT NULL,
  major VARCHAR(100) NOT NULL,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 도서
CREATE TABLE books (
  id UUID PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  author VARCHAR(255) NOT NULL,
  isbn VARCHAR(20),
  price INTEGER NOT NULL,
  condition VARCHAR(20) NOT NULL,
  description TEXT,
  image_urls TEXT[],
  seller_id UUID REFERENCES users(id),
  university VARCHAR(100) NOT NULL,
  major VARCHAR(100) NOT NULL,
  status VARCHAR(20) DEFAULT 'available',
  created_at TIMESTAMP DEFAULT NOW()
);

-- 관심 도서
CREATE TABLE wishlists (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  book_id UUID REFERENCES books(id),
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, book_id)
);
```

### 4.2 주요 API 엔드포인트

#### 인증 API
- `POST /api/auth/register` - 회원가입
- `POST /api/auth/login` - 로그인
- `POST /api/auth/logout` - 로그아웃
- `GET /api/auth/me` - 현재 사용자 정보

#### 도서 API
- `GET /api/books?university=&major=&search=` - 도서 목록
- `POST /api/books` - 도서 등록
- `GET /api/books/:id` - 도서 상세
- `PUT /api/books/:id` - 도서 수정
- `DELETE /api/books/:id` - 도서 삭제

#### 사용자 API
- `GET /api/users/me` - 내 정보
- `PUT /api/users/me` - 내 정보 수정
- `GET /api/users/me/books` - 내가 등록한 도서
- `GET /api/users/me/wishlist` - 관심 도서
```

## 🎨 5. UI/UX 가이드라인

### 디자인 시스템
```markdown
## 5. UI/UX 가이드라인

### 5.1 디자인 시스템

**컬러 팔레트**:
```css
:root {
  --primary: #3B82F6;      /* 메인 블루 */
  --secondary: #64748B;    /* 그레이 */
  --success: #10B981;      /* 성공 그린 */
  --warning: #F59E0B;      /* 경고 옐로우 */
  --error: #EF4444;        /* 에러 레드 */
  --background: #F8FAFC;   /* 배경 */
  --surface: #FFFFFF;      /* 카드 배경 */
}
```

**타이포그래피**:
- 제목: text-2xl font-bold (24px, 700)
- 부제목: text-lg font-medium (18px, 500)
- 본문: text-base (16px, 400)
- 캡션: text-sm text-gray-600 (14px)

### 5.2 주요 컴포넌트

#### 공통 컴포넌트
- `Button` - 기본/Primary/Secondary 버튼
- `Input` - 텍스트 입력 필드
- `Card` - 콘텐츠 카드
- `Modal` - 모달 다이얼로그
- `Toast` - 알림 메시지

#### 페이지별 레이아웃
- **헤더**: 로고, 검색바, 사용자 메뉴
- **메인**: 도서 목록 그리드 (3-4열)
- **사이드바**: 검색 필터 (학교, 전공, 가격대)
- **푸터**: 간단한 링크들

### 5.3 사용자 플로우

#### 주요 사용자 플로우
1. **회원가입**: 이메일 입력 → 인증 메일 → 정보 입력 → 완료
2. **도서 등록**: 로그인 → 등록 버튼 → 정보 입력 → 사진 업로드 → 완료
3. **도서 검색**: 메인 페이지 → 필터 설정 → 검색 → 결과 확인
4. **거래 시작**: 도서 클릭 → 상세 보기 → 관심 표시 → 판매자 연락
```

## ✅ 6. 완료 기준 및 검증

### 기능별 완료 기준
```markdown
## 6. 완료 기준 및 검증

### 6.1 MVP 완료 기준

#### 사용자 인증
- [ ] 이메일 회원가입/로그인 동작
- [ ] 학교 이메일 인증 기능
- [ ] JWT 기반 세션 관리
- [ ] 로그아웃 기능

#### 도서 관리
- [ ] 도서 등록 (텍스트 + 이미지)
- [ ] 도서 목록 조회 (검색/필터)
- [ ] 도서 상세 페이지
- [ ] 내가 등록한 도서 관리

#### 거래 기능
- [ ] 관심 도서 추가/제거
- [ ] 판매자 연락처 확인
- [ ] 거래 상태 변경

### 6.2 품질 기준
- [ ] 모든 핵심 기능 동작
- [ ] 모바일 반응형 디자인
- [ ] 로딩 시간 3초 이내
- [ ] 기본적인 에러 처리
- [ ] 코드 테스트 커버리지 70% 이상

### 6.3 배포 준비
- [ ] 프로덕션 환경 설정
- [ ] 데이터베이스 마이그레이션
- [ ] 환경변수 설정
- [ ] 기본 모니터링 설정
```

## 💡 AI 개발 가이드

### 개발 시 참고사항
```markdown
## 개발 가이드

### 개발 우선순위
1. **인증 시스템** (다른 기능의 전제조건)
2. **도서 CRUD** (핵심 기능)
3. **검색/필터** (사용자 경험 핵심)
4. **관심 도서** (사용자 참여 유도)
5. **거래 기능** (비즈니스 목표 달성)

### 주의사항
- 이메일 인증은 실제 메일 발송 대신 콘솔 로그로 대체 가능
- 이미지 업로드는 로컬 저장 후 URL 반환으로 시작
- 실시간 채팅은 MVP에서 제외, 간단한 연락처 확인만
- 결제 기능은 포함하지 않음 (직거래 가정)

### 확장 고려사항
- 추후 실시간 채팅 추가를 위한 사용자 간 관계 테이블 준비
- 관리자 기능을 위한 권한 시스템 확장성
- 모바일 앱 연동을 위한 API 설계
```

이 PRD를 참고하여 AI가 효율적으로 개발할 수 있도록 구체적이고 실용적인 정보를 제공합니다.