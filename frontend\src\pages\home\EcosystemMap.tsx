import React, { useState } from 'react';
import { Building2, GraduationCap, Zap, Users, Lightbulb } from 'lucide-react';

// 개별 참여자 정보
interface Participant {
  id: string;
  name: string;
  description: string;
  sector?: string;
  detailUrl: string;
}

// 에코시스템 노드 (참고 이미지 스타일)
interface EcosystemNode {
  id: string;
  name: string;
  type: 'etri' | 'companies' | 'institutions' | 'experts' | 'startups';
  description: string;
  position: 'top' | 'right' | 'bottom' | 'left' | 'center';
  participants: Participant[];
}

interface EcosystemMapProps {
  className?: string;
  onNodeHover?: (nodeData: any) => void;
}

// 샘플 데이터 (참고 이미지 스타일로 재구성)
const ecosystemNodes: EcosystemNode[] = [
  {
    id: 'etri',
    name: 'ETRI',
    type: 'etri',
    description: '한국전자통신연구원',
    position: 'center',
    participants: []
  },
  {
    id: 'companies',
    name: '참여기업',
    type: 'companies',
    description: '딥테크 혁신 기업들',
    position: 'top',
    participants: [
      {
        id: 'samsung',
        name: '삼성전자',
        description: '글로벌 전자기업',
        sector: '전자/반도체',
        detailUrl: '/companies/samsung'
      },
      {
        id: 'lg',
        name: 'LG전자',
        description: '생활가전 및 전자기업',
        sector: '전자/가전',
        detailUrl: '/companies/lg'
      },
      {
        id: 'sk',
        name: 'SK텔레콤',
        description: '통신 및 ICT 서비스',
        sector: '통신/ICT',
        detailUrl: '/companies/sk'
      }
    ]
  },
  {
    id: 'institutions',
    name: '연구기관',
    type: 'institutions',
    description: '정부출연 연구기관',
    position: 'right',
    participants: [
      {
        id: 'kaist',
        name: 'KAIST',
        description: '한국과학기술원',
        sector: '과학기술',
        detailUrl: '/institutions/kaist'
      },
      {
        id: 'kist',
        name: 'KIST',
        description: '한국과학기술연구원',
        sector: '과학기술',
        detailUrl: '/institutions/kist'
      }
    ]
  },
  {
    id: 'startups',
    name: '스타트업',
    type: 'startups',
    description: '딥테크 스타트업',
    position: 'bottom',
    participants: [
      {
        id: 'startup1',
        name: '딥테크 스타트업 A',
        description: 'AI 기반 솔루션',
        sector: 'AI/ML',
        detailUrl: '/startups/startup1'
      },
      {
        id: 'startup2',
        name: '딥테크 스타트업 B',
        description: '바이오 기술',
        sector: '바이오',
        detailUrl: '/startups/startup2'
      }
    ]
  },
  {
    id: 'experts',
    name: '전문가',
    type: 'experts',
    description: '기술 전문가 그룹',
    position: 'left',
    participants: [
      {
        id: 'expert1',
        name: '김전문가',
        description: 'AI 기술 전문가',
        sector: 'AI/ML',
        detailUrl: '/experts/expert1'
      },
      {
        id: 'expert2',
        name: '이전문가',
        description: '바이오 기술 전문가',
        sector: '바이오',
        detailUrl: '/experts/expert2'
      }
    ]
  }
];

// 노드 타입별 스타일 설정
const getNodeConfig = (type: EcosystemNode['type']) => {
  switch (type) {
    case 'etri':
      return {
        icon: Zap,
        bgColor: 'bg-gradient-to-br from-etri-blue-500 to-etri-blue-600',
        iconBgColor: 'bg-white',
        iconColor: 'text-etri-blue-600',
        textColor: 'text-white',
        borderColor: 'border-etri-blue-300',
        shadowColor: 'shadow-xl shadow-etri-blue-500/30'
      };
    case 'companies':
      return {
        icon: Building2,
        bgColor: 'bg-white',
        iconBgColor: 'bg-blue-50',
        iconColor: 'text-blue-600',
        textColor: 'text-gray-900',
        borderColor: 'border-blue-200',
        shadowColor: 'shadow-lg shadow-blue-500/20'
      };
    case 'institutions':
      return {
        icon: GraduationCap,
        bgColor: 'bg-white',
        iconBgColor: 'bg-green-50',
        iconColor: 'text-green-600',
        textColor: 'text-gray-900',
        borderColor: 'border-green-200',
        shadowColor: 'shadow-lg shadow-green-500/20'
      };
    case 'startups':
      return {
        icon: Lightbulb,
        bgColor: 'bg-white',
        iconBgColor: 'bg-purple-50',
        iconColor: 'text-purple-600',
        textColor: 'text-gray-900',
        borderColor: 'border-purple-200',
        shadowColor: 'shadow-lg shadow-purple-500/20'
      };
    case 'experts':
      return {
        icon: Users,
        bgColor: 'bg-white',
        iconBgColor: 'bg-orange-50',
        iconColor: 'text-orange-600',
        textColor: 'text-gray-900',
        borderColor: 'border-orange-200',
        shadowColor: 'shadow-lg shadow-orange-500/20'
      };
    default:
      return {
        icon: Building2,
        bgColor: 'bg-white',
        iconBgColor: 'bg-gray-50',
        iconColor: 'text-gray-600',
        textColor: 'text-gray-900',
        borderColor: 'border-gray-200',
        shadowColor: 'shadow-lg shadow-gray-500/20'
      };
  }
};

/**
 * 참고 이미지 스타일의 에코시스템 맵
 * 중앙에 ETRI, 상하좌우에 참여기업들을 배치
 */
const EcosystemMap: React.FC<EcosystemMapProps> = ({
  className = '',
  onNodeHover
}) => {
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState<string | null>(null);

  // 위치별 노드 배치
  const getPositionStyle = (position: EcosystemNode['position']) => {
    switch (position) {
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
      case 'top':
        return 'top-20 left-1/2 transform -translate-x-1/2';
      case 'right':
        return 'top-1/2 right-20 transform -translate-y-1/2';
      case 'bottom':
        return 'bottom-20 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'top-1/2 left-20 transform -translate-y-1/2';
      default:
        return '';
    }
  };

  return (
    <div className={`relative w-full h-[600px] bg-gray-50 rounded-2xl overflow-visible ${className}`}>
      {/* 제목 */}
      <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 text-center z-20">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          딥테크 밸리 에코시스템
        </h2>
        <p className="text-gray-600 flex items-center justify-center space-x-2">
          <span className="w-2 h-2 bg-etri-blue-500 rounded-full"></span>
          <span>ETRI를 중심으로 한 혁신 생태계</span>
        </p>
      </div>

      {/* 연결선들 */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none z-10">
        {/* ETRI에서 각 노드로의 연결선 */}
        {ecosystemNodes.filter(node => node.id !== 'etri').map((node) => {
          const isHovered = hoveredNode === node.id || hoveredNode === 'etri';
          
          // 중앙에서 각 방향으로의 연결선 (점선)
          let pathData = '';
          switch (node.position) {
            case 'top':
              pathData = 'M 50% 50% L 50% 15%';
              break;
            case 'right':
              pathData = 'M 50% 50% L 85% 50%';
              break;
            case 'bottom':
              pathData = 'M 50% 50% L 50% 85%';
              break;
            case 'left':
              pathData = 'M 50% 50% L 15% 50%';
              break;
          }

          return (
            <path
              key={`connection-${node.id}`}
              d={pathData}
              stroke={isHovered ? '#1d4ed8' : '#cbd5e1'}
              strokeWidth={isHovered ? '3' : '2'}
              strokeDasharray="8,4"
              className="transition-all duration-300"
              opacity={isHovered ? 0.8 : 0.4}
            />
          );
        })}
      </svg>

      {/* 노드들 */}
      {ecosystemNodes.map((node) => {
        const config = getNodeConfig(node.type);
        const IconComponent = config.icon;
        const isETRI = node.id === 'etri';
        const isHovered = hoveredNode === node.id;

        return (
          <div
            key={node.id}
            className={`absolute ${getPositionStyle(node.position)} z-20`}
            onMouseEnter={() => {
              setHoveredNode(node.id);
              if (node.participants.length > 0) {
                setShowParticipants(node.id);
                onNodeHover?.(node);
              }
            }}
            onMouseLeave={() => {
              setHoveredNode(null);
              setShowParticipants(null);
              onNodeHover?.(null);
            }}
          >
            {/* 노드 카드 */}
            <div
              className={`
                ${isETRI ? 'w-56 h-36' : 'w-44 h-28'}
                ${config.bgColor}
                ${config.borderColor}
                ${config.shadowColor}
                border-2 rounded-2xl p-4 cursor-pointer
                transition-all duration-300 hover:scale-105 hover:shadow-xl
                flex items-center space-x-4
              `}
            >
              {/* 아이콘 */}
              <div className={`
                ${isETRI ? 'w-16 h-16' : 'w-12 h-12'}
                ${config.iconBgColor}
                rounded-2xl flex items-center justify-center
                shadow-md
              `}>
                <IconComponent className={`${isETRI ? 'w-9 h-9' : 'w-6 h-6'} ${config.iconColor}`} />
              </div>

              {/* 텍스트 */}
              <div className="flex-1">
                <div className={`${isETRI ? 'text-xl' : 'text-lg'} font-bold ${config.textColor} leading-tight mb-1`}>
                  {node.name}
                </div>
                <div className={`${isETRI ? 'text-sm' : 'text-xs'} ${config.textColor} opacity-75`}>
                  {node.description}
                </div>
                {node.participants.length > 0 && (
                  <div className={`${isETRI ? 'text-sm' : 'text-xs'} ${config.textColor} opacity-90 mt-2 font-medium`}>
                    {node.participants.length}개 참여기관
                  </div>
                )}
              </div>
            </div>

            {/* 호버 시 참여자 목록 */}
            {showParticipants === node.id && node.participants.length > 0 && (
              <div className={`absolute z-40 ${
                node.position === 'top' ? 'top-full mt-4' :
                node.position === 'bottom' ? 'bottom-full mb-4' :
                node.position === 'left' ? 'left-full ml-4' :
                node.position === 'right' ? 'right-full mr-4' : 'top-full mt-4'
              } ${
                node.position === 'left' || node.position === 'right' ? '' : 'left-1/2 transform -translate-x-1/2'
              }`}>
                <div className="bg-white rounded-xl shadow-2xl border border-gray-200 p-5 min-w-72 max-w-80">
                  <div className="text-base font-bold text-gray-900 mb-4 border-b border-gray-100 pb-3">
                    {node.name}
                  </div>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {node.participants.map((participant) => (
                      <div
                        key={participant.id}
                        className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
                      >
                        <div className="w-3 h-3 bg-etri-blue-500 rounded-full mt-1.5 flex-shrink-0 group-hover:bg-etri-blue-600 transition-colors"></div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-semibold text-gray-900 truncate">
                            {participant.name}
                          </div>
                          <div className="text-xs text-gray-600 mt-1 leading-relaxed">
                            {participant.description}
                          </div>
                          {participant.sector && (
                            <div className="text-xs text-etri-blue-600 mt-2 font-medium bg-etri-blue-50 px-2 py-1 rounded-md inline-block">
                              {participant.sector}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 화살표 */}
                  <div className={`absolute ${
                    node.position === 'top' ? 'bottom-full left-1/2 transform -translate-x-1/2 border-8 border-transparent border-b-white' :
                    node.position === 'bottom' ? 'top-full left-1/2 transform -translate-x-1/2 border-8 border-transparent border-t-white' :
                    node.position === 'left' ? 'right-full top-1/2 transform -translate-y-1/2 border-8 border-transparent border-l-white' :
                    node.position === 'right' ? 'left-full top-1/2 transform -translate-y-1/2 border-8 border-transparent border-r-white' :
                    'bottom-full left-1/2 transform -translate-x-1/2 border-8 border-transparent border-b-white'
                  }`}></div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default EcosystemMap;
