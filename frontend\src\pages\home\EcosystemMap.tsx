import React, { useState } from 'react';
import { Building2, GraduationCap, Zap, Users, Lightbulb } from 'lucide-react';

// 개별 참여자 정보
interface Participant {
  id: string;
  name: string;
  description: string;
  sector?: string;
  detailUrl: string;
}

// 에코시스템 노드 (참고 이미지 스타일 - 중앙 허브 + 주변 노드)
interface EcosystemNode {
  id: string;
  name: string;
  type: 'center' | 'node';
  description: string;
  x: number; // 절대 좌표
  y: number; // 절대 좌표
  participants: Participant[];
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

interface EcosystemMapProps {
  className?: string;
  onNodeHover?: (nodeData: any) => void;
}

// 참고 이미지 스타일 데이터 (중앙 허브 + 주변 노드들)
const ecosystemNodes: EcosystemNode[] = [
  // 중앙 허브 (ETRI)
  {
    id: 'center',
    name: 'ETRI',
    type: 'center',
    description: '한국전자통신연구원',
    x: 400, // 중앙
    y: 300, // 중앙
    participants: [],
    icon: Zap,
    color: '#3b82f6' // 파란색
  },
  // 상단 노드 (Supplier)
  {
    id: 'supplier',
    name: 'Supplier',
    type: 'node',
    description: 'Payment terms set by buyer',
    x: 200,
    y: 100,
    participants: [
      {
        id: 'supplier1',
        name: '공급업체 A',
        description: '핵심 부품 공급',
        sector: '제조',
        detailUrl: '/suppliers/supplier1'
      },
      {
        id: 'supplier2',
        name: '공급업체 B',
        description: '소프트웨어 솔루션',
        sector: 'IT',
        detailUrl: '/suppliers/supplier2'
      }
    ],
    icon: Building2,
    color: '#10b981' // 초록색
  },
  // 우측 노드 (Buyer)
  {
    id: 'buyer',
    name: 'Buyer',
    type: 'node',
    description: 'Receivable amount paid',
    x: 600,
    y: 100,
    participants: [
      {
        id: 'buyer1',
        name: '구매업체 A',
        description: '대기업 고객',
        sector: '제조',
        detailUrl: '/buyers/buyer1'
      },
      {
        id: 'buyer2',
        name: '구매업체 B',
        description: '정부기관',
        sector: '공공',
        detailUrl: '/buyers/buyer2'
      }
    ],
    icon: Users,
    color: '#8b5cf6' // 보라색
  },
  // 하단 노드 (Banks)
  {
    id: 'banks',
    name: 'Banks',
    type: 'node',
    description: 'Funding partners provide liquidity',
    x: 400,
    y: 500,
    participants: [
      {
        id: 'bank1',
        name: '은행 A',
        description: '투자 파트너',
        sector: '금융',
        detailUrl: '/banks/bank1'
      },
      {
        id: 'bank2',
        name: '은행 B',
        description: '펀딩 지원',
        sector: '금융',
        detailUrl: '/banks/bank2'
      }
    ],
    icon: GraduationCap,
    color: '#f59e0b' // 주황색
  }
];

// 연결선 생성 함수 (참고 이미지 스타일의 점선)
const createConnectionPath = (fromNode: EcosystemNode, toNode: EcosystemNode) => {
  const dx = toNode.x - fromNode.x;
  const dy = toNode.y - fromNode.y;

  // 곡선 연결선 (참고 이미지 스타일)
  const midX = fromNode.x + dx * 0.5;
  const midY = fromNode.y + dy * 0.5;

  return `M ${fromNode.x} ${fromNode.y} Q ${midX} ${midY} ${toNode.x} ${toNode.y}`;
};

/**
 * 참고 이미지 스타일의 에코시스템 맵
 * 중앙 허브와 주변 노드들을 연결선으로 연결
 */
const EcosystemMap: React.FC<EcosystemMapProps> = ({
  className = '',
  onNodeHover
}) => {
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState<string | null>(null);

  // 중앙 노드 찾기
  const centerNode = ecosystemNodes.find(node => node.type === 'center');
  const peripheralNodes = ecosystemNodes.filter(node => node.type === 'node');

  return (
    <div className={`relative w-full h-[600px] bg-white rounded-2xl overflow-visible ${className}`}>
      {/* 제목 */}
      <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 text-center z-20">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Launch modern payment experiences, instantly
        </h2>
        <div className="flex items-center justify-center space-x-2">
          <div className="w-3 h-3 bg-gray-800 rounded-full flex items-center justify-center">
            <div className="w-1 h-1 bg-white rounded-full"></div>
          </div>
          <span className="text-gray-600 text-sm">See how it works</span>
        </div>
      </div>

      {/* SVG 연결선 (참고 이미지 스타일의 점선) */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none z-10">
        {centerNode && peripheralNodes.map((node) => {
          const isHovered = hoveredNode === node.id;
          const pathData = createConnectionPath(centerNode, node);

          return (
            <path
              key={`connection-${node.id}`}
              d={pathData}
              stroke={isHovered ? node.color : '#d1d5db'}
              strokeWidth={isHovered ? '3' : '2'}
              strokeDasharray="6,6"
              className="transition-all duration-300"
              opacity={isHovered ? 0.8 : 0.4}
              fill="none"
            />
          );
        })}
      </svg>

      {/* 노드들 (참고 이미지 스타일) */}
      {ecosystemNodes.map((node) => {
        const IconComponent = node.icon;
        const isCenter = node.type === 'center';
        const isHovered = hoveredNode === node.id;

        return (
          <div
            key={node.id}
            className="absolute z-20"
            style={{
              left: `${node.x}px`,
              top: `${node.y}px`,
              transform: 'translate(-50%, -50%)'
            }}
            onMouseEnter={() => {
              setHoveredNode(node.id);
              if (node.participants.length > 0) {
                setShowParticipants(node.id);
                onNodeHover?.(node);
              }
            }}
            onMouseLeave={() => {
              setHoveredNode(null);
              setShowParticipants(null);
              onNodeHover?.(null);
            }}
          >
            {/* 노드 카드 (참고 이미지 스타일) */}
            <div
              className={`
                ${isCenter ? 'w-32 h-32' : 'w-24 h-24'}
                bg-white border-2 border-gray-200 rounded-2xl
                cursor-pointer transition-all duration-300
                hover:scale-110 hover:shadow-xl hover:border-gray-300
                flex flex-col items-center justify-center space-y-2
                ${isHovered ? 'shadow-xl scale-110' : 'shadow-lg'}
              `}
              style={{
                background: isCenter
                  ? 'linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%)'
                  : 'white'
              }}
            >
              {/* 아이콘 */}
              <div
                className={`
                  ${isCenter ? 'w-12 h-12' : 'w-8 h-8'}
                  rounded-xl flex items-center justify-center
                `}
                style={{
                  backgroundColor: isCenter ? 'rgba(255,255,255,0.2)' : node.color + '20'
                }}
              >
                <IconComponent
                  className={`${isCenter ? 'w-7 h-7' : 'w-5 h-5'}`}
                  style={{ color: isCenter ? 'white' : node.color }}
                />
              </div>

              {/* 텍스트 */}
              <div className="text-center">
                <h3 className={`${isCenter ? 'text-sm' : 'text-xs'} font-bold`}
                    style={{ color: isCenter ? 'white' : '#374151' }}>
                  {node.name}
                </h3>
                {!isCenter && (
                  <p className="text-xs text-gray-500 mt-1 leading-tight">
                    {node.description}
                  </p>
                )}
              </div>
            </div>

            {/* 호버 시 참여자 목록 표시 (참고 이미지 스타일) */}
            {showParticipants === node.id && node.participants.length > 0 && (
              <div className="absolute left-full top-0 ml-8 w-80 bg-white rounded-xl shadow-xl border border-gray-200 p-4 z-30">
                {/* 연결선 */}
                <div
                  className="absolute right-full top-1/2 w-8 h-0.5 bg-gray-300"
                  style={{ transform: 'translateY(-50%)' }}
                ></div>

                <h4 className="font-semibold text-gray-900 mb-3 flex items-center space-x-2">
                  <IconComponent className="w-4 h-4" style={{ color: node.color }} />
                  <span>{node.name} 참여기관</span>
                </h4>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {node.participants.map((participant) => (
                    <div
                      key={participant.id}
                      className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                      onClick={() => window.open(participant.detailUrl, '_blank')}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 text-sm">{participant.name}</h5>
                          <p className="text-xs text-gray-600 mt-1">{participant.description}</p>
                        </div>
                        {participant.sector && (
                          <span
                            className="text-xs px-2 py-1 rounded-full ml-2 text-white"
                            style={{ backgroundColor: node.color }}
                          >
                            {participant.sector}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};



export default EcosystemMap;
