import React, { useState } from 'react';
import { Building2, GraduationCap, Zap, Users, Lightbulb } from 'lucide-react';

// 개별 참여자 정보
interface Participant {
  id: string;
  name: string;
  description: string;
  sector?: string;
  detailUrl: string; // 상세페이지 URL
}

// 에코시스템 카테고리 노드
interface EcosystemNode {
  id: string;
  name: string;
  type: 'etri' | 'companies' | 'institutions' | 'experts' | 'startups';
  description: string;
  position: { x: number; y: number };
  participants: Participant[]; // 해당 카테고리의 참여자 목록
}

// 에코시스템 노드 데이터
const ecosystemNodes: EcosystemNode[] = [
  // ETRI 중앙 허브
  {
    id: 'etri',
    name: 'ETRI',
    type: 'etri',
    description: '한국전자통신연구원 - 딥테크 밸리의 중심 허브',
    position: { x: 50, y: 50 },
    participants: [] // ETRI는 참여자 목록이 없음
  },

  // 참여 기업 카테고리
  {
    id: 'companies',
    name: '참여 기업',
    type: 'companies',
    description: '딥테크 밸리에 참여하는 기업들',
    position: { x: 20, y: 25 }, // 왼쪽 위 - 더 넓게 분산
    participants: [
      {
        id: 'samsung',
        name: '삼성전자',
        description: '글로벌 반도체 및 전자기기 선도기업',
        sector: '반도체/전자',
        detailUrl: '/companies/samsung'
      },
      {
        id: 'kt',
        name: 'KT',
        description: '5G, AI, IoT 기술 혁신 선도',
        sector: '통신/ICT',
        detailUrl: '/companies/kt'
      },
      {
        id: 'lg',
        name: 'LG전자',
        description: 'AI 가전 및 스마트홈 솔루션',
        sector: '전자/가전',
        detailUrl: '/companies/lg'
      },
      {
        id: 'sk',
        name: 'SK텔레콤',
        description: 'AI, 클라우드, 메타버스 기술',
        sector: '통신/AI',
        detailUrl: '/companies/sk'
      },
      {
        id: 'naver',
        name: '네이버',
        description: 'AI 플랫폼 및 클라우드 서비스',
        sector: 'IT/플랫폼',
        detailUrl: '/companies/naver'
      },
      {
        id: 'kakao',
        name: '카카오',
        description: '모빌리티 및 핀테크 혁신',
        sector: 'IT/플랫폼',
        detailUrl: '/companies/kakao'
      }
    ]
  },

  // 참여 기관 카테고리
  {
    id: 'institutions',
    name: '참여 기관',
    type: 'institutions',
    description: '연구기관 및 대학교',
    position: { x: 80, y: 25 }, // 오른쪽 위 - 더 넓게 분산
    participants: [
      {
        id: 'kaist',
        name: 'KAIST',
        description: '한국과학기술원 - AI, 로봇공학 연구',
        sector: '과학기술대학',
        detailUrl: '/institutions/kaist'
      },
      {
        id: 'snu',
        name: '서울대학교',
        description: '공과대학 - 차세대 반도체, 바이오 연구',
        sector: '종합대학',
        detailUrl: '/institutions/snu'
      },
      {
        id: 'kist',
        name: 'KIST',
        description: '한국과학기술연구원 - 융합기술 연구',
        sector: '과학기술연구원',
        detailUrl: '/institutions/kist'
      },
      {
        id: 'postech',
        name: 'POSTECH',
        description: '포항공과대학교 - 신소재, 에너지 연구',
        sector: '과학기술대학',
        detailUrl: '/institutions/postech'
      },
      {
        id: 'unist',
        name: 'UNIST',
        description: '울산과학기술원 - 바이오메디컬 연구',
        sector: '과학기술대학',
        detailUrl: '/institutions/unist'
      }
    ]
  },

  // 참여 스타트업 카테고리
  {
    id: 'startups',
    name: '참여 스타트업',
    type: 'startups',
    description: '혁신적인 딥테크 스타트업들',
    position: { x: 20, y: 75 }, // 왼쪽 아래 - 더 넓게 분산
    participants: [
      {
        id: 'ai-startup-1',
        name: '딥마인드코리아',
        description: 'AI 기반 의료진단 솔루션 개발',
        sector: '의료AI',
        detailUrl: '/startups/deepmind-korea'
      },
      {
        id: 'quantum-startup-1',
        name: '퀀텀테크',
        description: '양자컴퓨팅 하드웨어 및 소프트웨어',
        sector: '양자기술',
        detailUrl: '/startups/quantum-tech'
      },
      {
        id: 'bio-startup-1',
        name: '바이오젠',
        description: '유전자 치료제 및 바이오마커 개발',
        sector: '바이오테크',
        detailUrl: '/startups/biogen'
      },
      {
        id: 'robotics-startup-1',
        name: '로보틱스랩',
        description: '산업용 자율로봇 및 AI 제어시스템',
        sector: '로봇공학',
        detailUrl: '/startups/robotics-lab'
      },
      {
        id: 'energy-startup-1',
        name: '그린에너지',
        description: '차세대 배터리 및 에너지 저장 시스템',
        sector: '에너지',
        detailUrl: '/startups/green-energy'
      }
    ]
  },

  // 참여 전문가 카테고리
  {
    id: 'experts',
    name: '참여 전문가',
    type: 'experts',
    description: '딥테크 분야 전문가 및 연구자',
    position: { x: 80, y: 75 }, // 오른쪽 아래 - 더 넓게 분산
    participants: [
      {
        id: 'ai-expert-1',
        name: '김AI 박사',
        description: '인공지능 및 머신러닝 전문가',
        sector: '인공지능',
        detailUrl: '/experts/ai-expert-1'
      },
      {
        id: 'bio-expert-1',
        name: '이바이오 교수',
        description: '바이오테크놀로지 및 유전공학 전문가',
        sector: '바이오테크',
        detailUrl: '/experts/bio-expert-1'
      },
      {
        id: 'semiconductor-expert-1',
        name: '박반도체 연구원',
        description: '차세대 반도체 설계 전문가',
        sector: '반도체',
        detailUrl: '/experts/semiconductor-expert-1'
      },
      {
        id: 'quantum-expert-1',
        name: '최양자 박사',
        description: '양자컴퓨팅 및 양자통신 전문가',
        sector: '양자기술',
        detailUrl: '/experts/quantum-expert-1'
      },
      {
        id: 'robotics-expert-1',
        name: '정로봇 교수',
        description: '로봇공학 및 자율주행 전문가',
        sector: '로봇공학',
        detailUrl: '/experts/robotics-expert-1'
      },
      {
        id: 'energy-expert-1',
        name: '한에너지 박사',
        description: '신재생에너지 및 배터리 기술 전문가',
        sector: '에너지',
        detailUrl: '/experts/energy-expert-1'
      }
    ]
  }
];

// 노드 타입별 아이콘 및 스타일 정의 - 더 선명하고 크게
const getNodeConfig = (type: EcosystemNode['type']) => {
  switch (type) {
    case 'etri':
      return {
        icon: Zap,
        bgColor: 'bg-gradient-to-br from-etri-blue-600/95 via-etri-blue-700/95 to-etri-orange-600/95',
        textColor: 'text-white',
        borderColor: 'border-white/40',
        shadowColor: 'shadow-2xl shadow-etri-blue-600/60',
        hoverScale: 'hover:scale-110',
        size: 'w-48 h-32 text-lg'
      };
    case 'companies':
      return {
        icon: Building2,
        bgColor: 'bg-gradient-to-br from-blue-600/90 to-blue-700/90',
        textColor: 'text-white',
        borderColor: 'border-blue-400/60',
        shadowColor: 'shadow-xl shadow-blue-600/50',
        hoverScale: 'hover:scale-110',
        size: 'w-44 h-28 text-base'
      };
    case 'institutions':
      return {
        icon: GraduationCap,
        bgColor: 'bg-gradient-to-br from-green-600/90 to-green-700/90',
        textColor: 'text-white',
        borderColor: 'border-green-400/60',
        shadowColor: 'shadow-xl shadow-green-600/50',
        hoverScale: 'hover:scale-110',
        size: 'w-44 h-28 text-base'
      };
    case 'startups':
      return {
        icon: Lightbulb,
        bgColor: 'bg-gradient-to-br from-orange-600/90 to-orange-700/90',
        textColor: 'text-white',
        borderColor: 'border-orange-400/60',
        shadowColor: 'shadow-xl shadow-orange-600/50',
        hoverScale: 'hover:scale-110',
        size: 'w-44 h-28 text-base'
      };
    case 'experts':
      return {
        icon: Users,
        bgColor: 'bg-gradient-to-br from-purple-600/90 to-purple-700/90',
        textColor: 'text-white',
        borderColor: 'border-purple-400/60',
        shadowColor: 'shadow-xl shadow-purple-600/50',
        hoverScale: 'hover:scale-110',
        size: 'w-44 h-28 text-base'
      };
    default:
      return {
        icon: Building2,
        bgColor: 'bg-gradient-to-br from-gray-600 to-gray-700',
        textColor: 'text-white',
        borderColor: 'border-gray-400/50',
        shadowColor: 'shadow-xl shadow-gray-600/40',
        hoverScale: 'hover:scale-110',
        size: 'w-40 h-24 text-sm'
      };
  }
};

interface EcosystemMapProps {
  className?: string;
  onNodeHover?: (nodeData: any) => void;
}

/**
 * 호버 인터랙션과 애니메이션이 있는 딥테크 밸리 에코시스템 맵
 * ETRI를 중심으로 참여 기업, 기관, 전문가 카테고리를 시각화
 */
const EcosystemMap: React.FC<EcosystemMapProps> = ({
  className = '',
  onNodeHover
}) => {
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [animationStarted, setAnimationStarted] = useState(false);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 컴포넌트 마운트 시 애니메이션 시작
  React.useEffect(() => {
    const timer = setTimeout(() => setAnimationStarted(true), 300);
    return () => clearTimeout(timer);
  }, []);



  return (
    <div
      ref={containerRef}
      className={`w-full h-[700px] bg-transparent rounded-lg relative ${className}`}
      style={{ overflow: 'visible' }}
    >
      {/* 커스텀 스크롤바 스타일 */}
      <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }
      `}</style>
      {/* 미니멀 배경 패턴 */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(148, 163, 184, 0.3) 1px, transparent 0)',
          backgroundSize: '40px 40px'
        }} />
      </div>

      {/* 연결선들 (SVG) */}
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none z-0"
        viewBox="0 0 700 700"
        preserveAspectRatio="xMidYMid meet"
      >
        <defs>
          <linearGradient id="cleanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#2563eb" stopOpacity="0.8" />
            <stop offset="50%" stopColor="#0891b2" stopOpacity="0.7" />
            <stop offset="100%" stopColor="#7c3aed" stopOpacity="0.8" />
          </linearGradient>
          <linearGradient id="etriGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#1d4ed8" stopOpacity="0.9" />
            <stop offset="100%" stopColor="#ea580c" stopOpacity="0.9" />
          </linearGradient>
        </defs>

        {/* ETRI에서 다른 노드들로의 연결선 */}
        {ecosystemNodes.filter(node => node.id !== 'etri').map((node) => {
          const etri = ecosystemNodes.find(n => n.id === 'etri')!;

          // SVG viewBox 크기 (700x700)
          const svgWidth = 700;
          const svgHeight = 700;

          // 백분율을 픽셀로 변환
          const etriCenterX = (etri.position.x / 100) * svgWidth;
          const etriCenterY = (etri.position.y / 100) * svgHeight;
          const nodeCenterX = (node.position.x / 100) * svgWidth;
          const nodeCenterY = (node.position.y / 100) * svgHeight;

          // 방향 벡터
          const dx = nodeCenterX - etriCenterX;
          const dy = nodeCenterY - etriCenterY;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance === 0) return null; // 같은 위치면 스킵

          // 연결선이 노드 중심까지 완전히 도달하도록 반지름 0

          // 노드 중심에서 중심까지 연결
          const startX = etriCenterX;
          const startY = etriCenterY;
          const endX = nodeCenterX;
          const endY = nodeCenterY;

          // 깔끔한 곡선 연결 (Quadratic Bezier)
          const midX = (startX + endX) / 2;
          const midY = (startY + endY) / 2;

          // 곡선의 강도를 거리에 비례하여 조정
          const curvature = distance * 0.08;

          // 노드 위치에 따라 곡선 방향 결정
          const isTopNode = endY < etriCenterY;
          const isLeftNode = endX < etriCenterX;

          // 자연스러운 곡선을 위한 제어점 계산
          let controlX, controlY;

          if (isTopNode) {
            // 위쪽 노드들은 위로 볼록한 곡선
            controlX = midX + (isLeftNode ? -curvature * 0.5 : curvature * 0.5);
            controlY = midY - curvature;
          } else {
            // 아래쪽 노드들은 아래로 볼록한 곡선
            controlX = midX + (isLeftNode ? -curvature * 0.5 : curvature * 0.5);
            controlY = midY + curvature;
          }

          // 단순한 Quadratic Bezier 곡선
          const pathData = `M ${startX} ${startY} Q ${controlX} ${controlY} ${endX} ${endY}`;

          return (
            <path
              key={`connection-${node.id}`}
              d={pathData}
              stroke="url(#cleanGradient)"
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              style={{
                transition: `opacity 0.3s ease-out`,
              }}
              className={`${
                hoveredNode === 'etri' || hoveredNode === node.id
                  ? 'opacity-90'
                  : 'opacity-60'
              }`}
            />
          );
        })}
      </svg>

      {/* 노드들 */}
      {ecosystemNodes.map((node, index) => {
        const config = getNodeConfig(node.type);
        const IconComponent = config.icon;
        const isHovered = hoveredNode === node.id;

        const isETRI = node.id === 'etri';

        // 애니메이션을 위한 중앙 위치 (ETRI 위치)
        const etriPosition = ecosystemNodes.find(n => n.id === 'etri')!.position;





        return (
          <div
            key={node.id}
            className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer ${config.size} ${config.hoverScale}`}
            style={{
              // 백분율로 위치 설정 (원래대로)
              left: animationStarted || isETRI
                ? `${node.position.x}%`
                : `${etriPosition.x}%`,
              top: animationStarted || isETRI
                ? `${node.position.y}%`
                : `${etriPosition.y}%`,
              transform: `translate(-50%, -50%) scale(${animationStarted || isETRI ? 1 : 0.3})`,
              transition: isETRI
                ? 'all 0.3s ease-out'
                : `all 1s ease-out ${index * 0.2 + 0.5}s`,
              opacity: animationStarted || isETRI ? 1 : 0,
            }}
            onMouseEnter={() => {
              setHoveredNode(node.id);
              if (onNodeHover && node.participants.length > 0) {
                onNodeHover(node);
              }
            }}
            onMouseLeave={() => {
              setHoveredNode(null);
              if (onNodeHover) {
                onNodeHover(null);
              }
            }}
          >
            <div
              className={`w-full h-full ${isETRI ? 'rounded-2xl' : 'rounded-xl'} ${config.bgColor} border-2 ${config.borderColor} flex items-center justify-center ${isETRI ? 'px-4 py-3' : 'px-3 py-2'} transition-all duration-500 ${config.hoverScale} relative overflow-hidden group ${config.shadowColor}`}
            >
              {/* 호버 효과 오버레이 */}
              <div className={`absolute inset-0 opacity-0 group-hover:opacity-20 transition-all duration-300 ${isETRI ? 'rounded-2xl' : 'rounded-xl'} bg-white`}></div>
              <div className="relative z-10 flex items-center justify-center space-x-3 w-full h-full">
                {/* 아이콘 */}
                <div className={`${isETRI ? 'p-3' : 'p-2'} rounded-full bg-white/20 backdrop-blur-sm transition-all duration-300 group-hover:scale-110 group-hover:bg-white/30 flex-shrink-0 border border-white/30`}>
                  <IconComponent className={`${isETRI ? 'w-8 h-8' : 'w-6 h-6'} ${config.textColor} drop-shadow-sm`} />
                </div>

                {/* 텍스트 영역 */}
                <div className="flex-1 flex flex-col justify-center min-w-0 overflow-hidden">
                  <div className={`font-bold ${isETRI ? 'text-lg' : 'text-base'} ${config.textColor} leading-tight ${node.participants.length > 0 ? 'mb-1' : ''} transition-colors duration-300 drop-shadow-lg`}>
                    <div className="truncate">{node.name}</div>
                  </div>
                  {node.participants.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <div className="w-1.5 h-1.5 bg-white/90 rounded-full animate-pulse flex-shrink-0"></div>
                      <div className={`text-sm ${config.textColor} font-medium truncate opacity-90`}>
                        {node.participants.length}개
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>



            {/* ETRI 노드의 경우 간단한 설명 툴팁 */}
            {isHovered && node.id === 'etri' && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 bg-white rounded-lg shadow-lg border p-3 min-w-48 z-10 animate-in fade-in-0 zoom-in-95 duration-200">
                <div className="font-semibold text-gray-900">{node.name}</div>
                <div className="text-xs text-gray-500 mt-2">{node.description}</div>

                {/* 툴팁 화살표 */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-white"></div>
              </div>
            )}
          </div>
        );
      })}


    </div>
  );
};

export default EcosystemMap;
