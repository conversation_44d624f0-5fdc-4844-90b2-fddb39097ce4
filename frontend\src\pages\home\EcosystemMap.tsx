import React, { useState } from 'react';
import { Building2, GraduationCap, Zap, Users } from 'lucide-react';

// 타입 정의
interface Participant {
  id: string;
  name: string;
  description: string;
  sector?: string;
  detailUrl: string;
}

interface EcosystemNode {
  id: string;
  name: string;
  type: 'center' | 'node';
  description: string;
  x: number;
  y: number;
  participants: Participant[];
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

interface EcosystemMapProps {
  className?: string;
  onNodeHover?: (node: EcosystemNode | null) => void;
}

// 딥테크 밸리 에코시스템 - 중앙 ETRI + 주변 참여기관들
const ecosystemNodes: EcosystemNode[] = [
  // 중앙 허브 (ETRI)
  {
    id: 'center',
    name: 'ETRI 딥테크 밸리',
    type: 'center',
    description: '한국전자통신연구원 딥테크 밸리 플랫폼',
    x: 400,
    y: 300,
    participants: [],
    icon: Zap,
    color: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)'
  },
  // 상단 좌측 (연구기관)
  {
    id: 'research',
    name: '연구기관',
    type: 'node',
    description: '첨단 기술 연구개발',
    x: 150,
    y: 150,
    participants: [
      {
        id: 'kaist',
        name: 'KAIST',
        description: '인공지능 및 로봇 기술',
        sector: '연구',
        detailUrl: '/research/kaist'
      },
      {
        id: 'postech',
        name: 'POSTECH',
        description: '나노소재 및 바이오 기술',
        sector: '연구',
        detailUrl: '/research/postech'
      },
      {
        id: 'kist',
        name: 'KIST',
        description: '융합기술 연구',
        sector: '연구',
        detailUrl: '/research/kist'
      }
    ],
    icon: GraduationCap,
    color: '#10b981'
  },
  // 상단 우측 (기업)
  {
    id: 'companies',
    name: '참여기업',
    type: 'node',
    description: '기술사업화 및 상용화',
    x: 650,
    y: 150,
    participants: [
      {
        id: 'samsung',
        name: '삼성전자',
        description: '반도체 및 디스플레이 기술',
        sector: '대기업',
        detailUrl: '/companies/samsung'
      },
      {
        id: 'lg',
        name: 'LG전자',
        description: 'AI 가전 및 자율주행',
        sector: '대기업',
        detailUrl: '/companies/lg'
      },
      {
        id: 'naver',
        name: '네이버',
        description: 'AI 플랫폼 기술',
        sector: 'IT',
        detailUrl: '/companies/naver'
      }
    ],
    icon: Building2,
    color: '#3b82f6'
  },
  // 하단 중앙 (투자기관)
  {
    id: 'investors',
    name: '투자기관',
    type: 'node',
    description: '딥테크 스타트업 투자 지원',
    x: 400,
    y: 480,
    participants: [
      {
        id: 'kvic',
        name: '한국벤처투자',
        description: '딥테크 전문 투자',
        sector: '투자',
        detailUrl: '/investors/kvic'
      },
      {
        id: 'kdb',
        name: 'KDB산업은행',
        description: '정책금융 지원',
        sector: '금융',
        detailUrl: '/investors/kdb'
      },
      {
        id: 'tips',
        name: 'TIPS 프로그램',
        description: '민관 협력 투자',
        sector: '정책',
        detailUrl: '/investors/tips'
      }
    ],
    icon: Users,
    color: '#6b7280'
  }
];

// 연결선 생성 함수 (참고 이미지 스타일의 부드러운 곡선)
const createConnectionPath = (fromNode: EcosystemNode, toNode: EcosystemNode) => {
  const dx = toNode.x - fromNode.x;
  const dy = toNode.y - fromNode.y;
  
  // 참고 이미지처럼 부드러운 베지어 곡선
  const controlOffset = 50;
  const controlX1 = fromNode.x + (dx > 0 ? controlOffset : -controlOffset);
  const controlY1 = fromNode.y;
  const controlX2 = toNode.x + (dx > 0 ? -controlOffset : controlOffset);
  const controlY2 = toNode.y;
  
  return `M ${fromNode.x} ${fromNode.y} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${toNode.x} ${toNode.y}`;
};

/**
 * 참고 이미지 스타일의 에코시스템 맵
 * 중앙 허브와 주변 노드들을 연결선으로 연결
 */
const EcosystemMap: React.FC<EcosystemMapProps> = ({
  className = '',
  onNodeHover
}) => {
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState<string | null>(null);

  // 중앙 노드 찾기
  const centerNode = ecosystemNodes.find(node => node.type === 'center');
  const peripheralNodes = ecosystemNodes.filter(node => node.type === 'node');

  return (
    <div className={`relative w-full h-[600px] bg-white rounded-2xl overflow-visible ${className}`}>
      {/* 제목 */}
      <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 text-center z-20">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Launch modern payment experiences, instantly
        </h2>
        <div className="flex items-center justify-center space-x-2">
          <div className="w-3 h-3 bg-gray-800 rounded-full flex items-center justify-center">
            <div className="w-1 h-1 bg-white rounded-full"></div>
          </div>
          <span className="text-gray-600 text-sm">See how it works</span>
        </div>
      </div>

      {/* SVG 연결선 (참고 이미지 스타일의 부드러운 곡선) */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none z-10">
        {centerNode && peripheralNodes.map((node) => {
          const isHovered = hoveredNode === node.id;
          const pathData = createConnectionPath(centerNode, node);
          
          return (
            <path
              key={`connection-${node.id}`}
              d={pathData}
              stroke={isHovered ? node.color : '#d1d5db'}
              strokeWidth={isHovered ? '3' : '2'}
              className="transition-all duration-300"
              opacity={isHovered ? 0.9 : 0.6}
              fill="none"
            />
          );
        })}
      </svg>

      {/* 노드들 (참고 이미지 정확히 따라하기) */}
      {ecosystemNodes.map((node) => {
        const IconComponent = node.icon;
        const isCenter = node.type === 'center';
        const isHovered = hoveredNode === node.id;

        if (isCenter) {
          // 중앙 노드 - 큰 직사각형 카드 (참고 이미지 스타일)
          return (
            <div
              key={node.id}
              className="absolute z-20"
              style={{
                left: `${node.x}px`,
                top: `${node.y}px`,
                transform: 'translate(-50%, -50%)'
              }}
              onMouseEnter={() => {
                setHoveredNode(node.id);
                onNodeHover?.(node);
              }}
              onMouseLeave={() => {
                setHoveredNode(null);
                onNodeHover?.(null);
              }}
            >
              {/* 중앙 카드 - 참고 이미지와 동일한 스타일 */}
              <div
                className={`
                  w-64 h-32 rounded-2xl cursor-pointer 
                  transition-all duration-300 hover:scale-105 hover:shadow-xl
                  flex flex-col items-center justify-center space-y-3
                  ${isHovered ? 'shadow-xl scale-105' : 'shadow-lg'}
                `}
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)'
                }}
              >
                {/* 중앙 아이콘 */}
                <div className="w-12 h-12 bg-black/20 rounded-xl flex items-center justify-center">
                  <IconComponent className="w-7 h-7 text-black" />
                </div>
                
                {/* 중앙 텍스트 */}
                <div className="text-center">
                  <h3 className="text-white font-bold text-lg">
                    {node.name}
                  </h3>
                </div>
              </div>
            </div>
          );
        } else {
          // 주변 노드들 - 작은 원형 아이콘 + 텍스트 (참고 이미지 스타일)
          return (
            <div
              key={node.id}
              className="absolute z-20"
              style={{
                left: `${node.x}px`,
                top: `${node.y}px`,
                transform: 'translate(-50%, -50%)'
              }}
              onMouseEnter={() => {
                setHoveredNode(node.id);
                if (node.participants.length > 0) {
                  setShowParticipants(node.id);
                  onNodeHover?.(node);
                }
              }}
              onMouseLeave={() => {
                setHoveredNode(null);
                setShowParticipants(null);
                onNodeHover?.(null);
              }}
            >
              {/* 주변 노드 - 참고 이미지와 동일한 스타일 */}
              <div className="flex flex-col items-center space-y-3">
                {/* 원형 아이콘 */}
                <div 
                  className={`
                    w-12 h-12 rounded-full flex items-center justify-center
                    cursor-pointer transition-all duration-300 hover:scale-110
                    ${isHovered ? 'scale-110 shadow-lg' : 'shadow-md'}
                  `}
                  style={{ backgroundColor: node.color }}
                >
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                
                {/* 텍스트 */}
                <div className="text-center">
                  <h3 className="font-bold text-gray-900 text-sm mb-1">
                    {node.name}
                  </h3>
                  <p className="text-xs text-gray-600 leading-tight max-w-32">
                    {node.description}
                  </p>
                </div>
              </div>

              {/* 호버 시 참여자 목록 (참고 이미지 스타일) */}
              {showParticipants === node.id && node.participants.length > 0 && (
                <div className="absolute left-full top-1/2 ml-8 w-80 bg-white rounded-xl shadow-xl border border-gray-200 p-4 z-30 transform -translate-y-1/2">
                  {/* 연결선 */}
                  <div 
                    className="absolute right-full top-1/2 w-8 h-0.5"
                    style={{ 
                      backgroundColor: node.color,
                      transform: 'translateY(-50%)'
                    }}
                  ></div>
                  
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center space-x-2">
                    <IconComponent className="w-4 h-4" style={{ color: node.color }} />
                    <span>{node.name} 참여기관</span>
                  </h4>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {node.participants.map((participant) => (
                      <div
                        key={participant.id}
                        className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                        onClick={() => window.open(participant.detailUrl, '_blank')}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h5 className="font-medium text-gray-900 text-sm">{participant.name}</h5>
                            <p className="text-xs text-gray-600 mt-1">{participant.description}</p>
                          </div>
                          {participant.sector && (
                            <span
                              className="text-xs px-2 py-1 rounded-full ml-2 text-white"
                              style={{ backgroundColor: node.color }}
                            >
                              {participant.sector}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        }
      })}
    </div>
  );
};

export default EcosystemMap;
