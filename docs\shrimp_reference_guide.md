# Shrimp 작업 시 참고 문서 가이드

## 📋 개요

이 문서는 Shrimp AI Agent가 DeepTech Valley Platform 개발 작업을 수행할 때 어떤 문서들을 참고해야 하는지 정리한 가이드입니다.

## 🎯 작업별 필수 참고 문서

### 🔧 **모든 작업 공통 참고 문서**

#### 1️⃣ **shrimp-rules.md** (최우선 참고)
- **목적**: 개발 표준 및 코딩 규칙
- **내용**: TypeScript, React, FastAPI 표준, 작업 관리 자동화
- **참고 시점**: 모든 코드 작성 전 필수 확인

#### 2️⃣ **updated-ui-design-spec.md** (UI 작업 시 필수)
- **목적**: 최신 UI/UX 디자인 명세서
- **내용**: ETRI 기반 컬러 팔레트, 타이포그래피, 레이아웃, 컴포넌트 설계
- **참고 시점**: 모든 프론트엔드 작업 시 필수

#### 3️⃣ **ui-implementation-guide.md** (UI 구현 시 필수)
- **목적**: 실제 구현된 컴포넌트 패턴 및 재사용 가이드
- **내용**: 헤더, 메가메뉴, 사이드바, 테이블 등 구현 패턴, 새 페이지 템플릿
- **참고 시점**: UI 컴포넌트 개발 및 새 페이지 생성 시 필수

#### 4️⃣ **tailwind-config-guide.md** (새 페이지 개발 시 필수)
- **목적**: ETRI 색상 시스템 및 Tailwind CSS 설정 가이드
- **내용**: ETRI 브랜드 색상 설정, 마이그레이션 방법, 성능 최적화
- **참고 시점**: 새 페이지 개발 및 스타일 시스템 변경 시 필수

#### 5️⃣ **tech_spec.md**
- **목적**: 기술 아키텍처 및 스택 정의
- **내용**: React + TypeScript + ShadcnUI + FastAPI 구조
- **참고 시점**: 기술적 결정이 필요한 모든 작업

### 🏗️ **1차 개발 (핵심 기능) 작업별 참고 문서**

#### 📦 **프로젝트 초기 설정**
```
필수 참고:
✅ shrimp-rules.md (개발 표준)
✅ tech_spec.md (기술 스택)
✅ updated-ui-design-spec.md (디자인 시스템)
✅ development_plan.md (개발 계획)

선택 참고:
📄 project_summary.md (프로젝트 개요)
```

#### 🗄️ **데이터베이스 모델 설계**
```
필수 참고:
✅ database_design.md (DB 설계서)
✅ sqlalchemy_models.md (모델 구현 가이드)
✅ shrimp-rules.md (코딩 표준)

주의사항:
⚠️ 데이터베이스 종류는 아직 미정 상태
⚠️ PostgreSQL 기준으로 작성되었으나 변경 가능
```

#### 🔐 **인증 시스템 구현**
```
필수 참고:
✅ shrimp-rules.md (JWT 구현 표준)
✅ api_specification.md (인증 API 명세)
✅ updated-ui-design-spec.md (로그인 UI 설계)
✅ tech_spec.md (보안 요구사항)

선택 참고:
📄 database_design.md (사용자 테이블 구조)
```

#### 👥 **사용자 관리 시스템**
```
필수 참고:
✅ updated-ui-design-spec.md (사용자 관리 UI)
✅ ui-implementation-guide.md (사용자 관리 페이지 구현 패턴)
✅ api_specification.md (사용자 API 명세)
✅ database_design.md (사용자 테이블)
✅ shrimp-rules.md (권한 관리 표준)

선택 참고:
📄 tailwind-config-guide.md (ETRI 색상 시스템 설정)
📄 sqlalchemy_models.md (User 모델)
```

#### 📢 **공지관리 시스템**
```
필수 참고:
✅ updated-ui-design-spec.md (공지사항 UI 설계)
✅ ui-implementation-guide.md (공지사항 페이지 구현 패턴)
✅ api_specification.md (공지사항 API)
✅ database_design.md (공지사항 테이블)
✅ shrimp-rules.md (파일 업로드 표준)

선택 참고:
📄 tailwind-config-guide.md (ETRI 색상 시스템 설정)
📄 sqlalchemy_models.md (Notice 모델)
```

#### 📝 **수요관리 시스템**
```
필수 참고:
✅ updated-ui-design-spec.md (신청서 폼 UI)
✅ ui-implementation-guide.md (폼 컴포넌트 구현 패턴)
✅ api_specification.md (신청서 API)
✅ database_design.md (신청서 테이블)
✅ shrimp-rules.md (파일 업로드, PDF 생성)

선택 참고:
📄 tailwind-config-guide.md (ETRI 색상 시스템 설정)
📄 sqlalchemy_models.md (Application 모델)
📄 prd_draft.md (비즈니스 요구사항)
```

### 🚀 **2차 개발 (고급 기능) 작업별 참고 문서**

#### 🗺️ **D3.js 에코시스템 맵**
```
필수 참고:
✅ updated-ui-design-spec.md (에코시스템 맵 UI 설계)
✅ shrimp-rules.md (D3.js 구현 패턴)
✅ tech_spec.md (시각화 요구사항)

선택 참고:
📄 database_design.md (회사 관계 테이블)
📄 api_specification.md (밸리관리 API)
```

#### 🏢 **밸리관리 시스템**
```
필수 참고:
✅ updated-ui-design-spec.md (밸리관리 UI)
✅ ui-implementation-guide.md (밸리관리 페이지 구현 패턴)
✅ tailwind-config-guide.md (ETRI 색상 시스템 설정)
✅ database_design.md (회사/기관 테이블)
✅ api_specification.md (밸리관리 API)
✅ shrimp-rules.md (데이터 시각화 표준)

선택 참고:
📄 sqlalchemy_models.md (Company, Institution 모델)
```

#### 📊 **통계 시스템**
```
필수 참고:
✅ updated-ui-design-spec.md (대시보드 UI)
✅ ui-implementation-guide.md (대시보드 컴포넌트 구현 패턴)
✅ shrimp-rules.md (Chart.js 구현 표준)
✅ api_specification.md (통계 API)

선택 참고:
📄 tailwind-config-guide.md (ETRI 색상 시스템 설정)
📄 database_design.md (통계 쿼리 참고)
📄 tech_spec.md (성능 요구사항)
```

#### 📄 **PDF 문서 생성**
```
필수 참고:
✅ shrimp-rules.md (ReportLab 구현 가이드)
✅ api_specification.md (문서 생성 API)

선택 참고:
📄 database_design.md (신청서 데이터 구조)
```

### ⚡ **3차 개발 (최적화) 작업별 참고 문서**

#### 📱 **반응형 최적화**
```
필수 참고:
✅ updated-ui-design-spec.md (반응형 브레이크포인트)
✅ ui-implementation-guide.md (반응형 패턴 구현 가이드)
✅ tailwind-config-guide.md (반응형 Tailwind 설정)
✅ shrimp-rules.md (성능 최적화 가이드)

선택 참고:
📄 tech_spec.md (성능 요구사항)
```

#### ⚡ **성능 최적화**
```
필수 참고:
✅ shrimp-rules.md (성능 최적화 패턴)
✅ tech_spec.md (성능 기준)

선택 참고:
📄 database_design.md (인덱스 전략)
```

#### ♿ **접근성 개선**
```
필수 참고:
✅ shrimp-rules.md (접근성 구현 가이드)
✅ updated-ui-design-spec.md (접근성 고려사항)
✅ ui-implementation-guide.md (접근성 패턴 구현 가이드)

선택 참고:
📄 tailwind-config-guide.md (접근성 관련 Tailwind 설정)
📄 tech_spec.md (접근성 표준)
```

#### 🔒 **보안 강화**
```
필수 참고:
✅ shrimp-rules.md (보안 표준)
✅ tech_spec.md (보안 요구사항)
✅ api_specification.md (인증/권한)

선택 참고:
📄 database_design.md (데이터 보안)
```

#### 🧪 **테스트 구현**
```
필수 참고:
✅ shrimp-rules.md (테스트 전략)
✅ tech_spec.md (테스트 요구사항)

선택 참고:
📄 api_specification.md (API 테스트)
```

#### 🚀 **배포 준비**
```
필수 참고:
✅ shrimp-rules.md (배포 가이드)
✅ tech_spec.md (배포 환경)

선택 참고:
📄 database_design.md (백업/복구)
```

## 🔄 **문서 우선순위 및 충돌 해결**

### 📊 **우선순위 순서**
1. **shrimp-rules.md** (최고 우선순위 - 개발 표준)
2. **updated-ui-design-spec.md** (UI 작업 시 최우선 - 디자인 명세)
3. **ui-implementation-guide.md** (UI 구현 시 필수 - 구현 패턴)
4. **tailwind-config-guide.md** (새 페이지 개발 시 필수 - 기술 설정)
5. **tech_spec.md** (기술적 결정 시 참고)
6. **api_specification.md** (API 작업 시 참고)
7. **database_design.md** (DB 작업 시 참고)
8. 기타 문서들

### ⚠️ **문서 간 충돌 시 해결 방법**
1. **shrimp-rules.md 우선**: 개발 표준이 다른 문서와 충돌 시
2. **최신 문서 우선**: updated-ui-design-spec.md가 기존 ui_design_spec.md보다 우선
3. **구체적 문서 우선**: 상세한 명세가 일반적 가이드보다 우선
4. **UI 가이드 계층**: updated-ui-design-spec.md (디자인) > ui-implementation-guide.md (구현) > tailwind-config-guide.md (설정)
5. **사용자 확인**: 중요한 충돌 시 사용자에게 확인 요청

## 📝 **문서 업데이트 알림**

### 🔔 **주요 변경사항**
- **updated-ui-design-spec.md**: 최신 UI 디자인 명세서 (기존 ui_design_spec.md 대체)
- **ui-implementation-guide.md**: 새로 추가된 UI 구현 가이드 (실제 구현 패턴 및 재사용 템플릿)
- **tailwind-config-guide.md**: 새로 추가된 Tailwind CSS 설정 가이드 (ETRI 색상 시스템)
- **database_design.md**: 새로 추가된 DB 설계서 (PostgreSQL 기준, 변경 가능)
- **sqlalchemy_models.md**: 새로 추가된 모델 구현 가이드

### ⚠️ **주의사항**
- **데이터베이스 미정**: PostgreSQL 기준 문서이나 변경 가능성 있음
- **UI 디자인 업데이트**: updated-ui-design-spec.md가 최신 버전
- **UI 구현 가이드**: ui-implementation-guide.md는 실제 구현된 패턴 기반
- **Tailwind 설정**: tailwind-config-guide.md는 ETRI 색상 시스템 필수 적용
- **개발 시작 금지**: 아직 개발 시작하지 말고 계획 단계 유지

## 🎯 **작업 시작 전 체크리스트**

### ✅ **작업 시작 전 필수 확인사항**
- [ ] 해당 작업의 필수 참고 문서 모두 확인
- [ ] shrimp-rules.md의 관련 섹션 숙지
- [ ] updated-ui-design-spec.md의 해당 UI 설계 확인
- [ ] **ui-implementation-guide.md** 구현 패턴 확인 (UI 작업 시)
- [ ] **tailwind-config-guide.md** 설정 방법 확인 (새 페이지 개발 시)
- [ ] 문서 간 충돌사항 없는지 확인
- [ ] 데이터베이스 미정 상태 고려
- [ ] 작업 완료 후 문서 업데이트 계획 수립

### 🚫 **작업 완료 전 필수 검증 (절대 준수)**

#### ❌ **작업 완료 금지 조건**
다음 중 하나라도 해당하면 **절대 작업 완료 불가**:
- [ ] TypeScript 컴파일 오류 존재
- [ ] ESLint 오류 존재
- [ ] 런타임 에러 발생 (콘솔 에러)
- [ ] 빌드 실패 (`npm run build` 실패)
- [ ] 개발 서버 실행 실패
- [ ] 핵심 기능 동작 실패
- [ ] 필수 문서 업데이트 누락
- [ ] 보안 취약점 발견
- [ ] **터미널 명령어에서 상대경로 사용**
- [ ] **UI 일관성 검증 실패** (프론트엔드 작업 시)

#### ✅ **작업 완료 승인 조건**
모든 항목이 체크되어야 작업 완료 가능:
- [ ] **코드 오류 제로**: TypeScript, ESLint, 런타임 오류 모두 해결
- [ ] **빌드 성공**: `npm run build` 명령어 성공 실행
- [ ] **서버 정상 실행**: 개발 서버 오류 없이 실행
- [ ] **기능 정상 동작**: 구현된 모든 기능 정상 작동 확인
- [ ] **문서 업데이트**: README, API 문서 등 관련 문서 업데이트
- [ ] **표준 준수**: shrimp-rules.md 코딩 표준 준수
- [ ] **UI 일관성 검증**: 프론트엔드 작업 시 UI 가이드 준수 확인
- [ ] **다음 작업 준비**: 다음 작업 진행에 필요한 모든 준비 완료

#### 🔍 **검증 절차**
1. **코드 품질 검사**
   ```bash
   # TypeScript 컴파일 검사
   npx tsc --noEmit

   # ESLint 검사
   npx eslint src --ext .ts,.tsx

   # 빌드 테스트
   npm run build
   ```

2. **서버 실행 테스트**
   ```bash
   # 개발 서버 실행 확인
   npm run dev
   # 브라우저에서 http://localhost:5173 접속 확인
   ```

3. **기능 동작 확인**
   - 구현된 모든 기능 수동 테스트
   - 콘솔 에러 없음 확인
   - UI 정상 렌더링 확인

4. **UI 일관성 검증** (프론트엔드 작업 시)
   - ETRI 색상 시스템 적용 확인 (`etri-blue-*` 클래스 사용)
   - 구현 가이드 패턴 준수 확인 (ui-implementation-guide.md 참조)
   - 재사용 컴포넌트 활용 확인
   - 폰트 크기 및 간격 시스템 준수 확인 (`text-base` 이상, `mb-6` 등)
   - 호버 효과 및 애니메이션 일관성 확인
   - 반응형 디자인 적용 확인

5. **문서 업데이트 확인**
   - README.md 진행률 업데이트
   - 관련 API 문서 업데이트
   - 변경사항 기록

**⚠️ 중요: 위 모든 조건을 만족해야만 `verify_task`로 작업 완료 처리 가능**

## 🖥️ **터미널 사용 규칙 (절대 준수)**

### 📍 **절대경로 사용 원칙**
모든 터미널 명령어 실행 시 **반드시 절대경로만 사용**:

```typescript
// ✅ 올바른 사용법
launch_process({
  command: "npm install",
  cwd: "d:\\gayun\\2025\\deeptech-valley-platform\\frontend",  // 절대경로
  wait: true,
  max_wait_seconds: 120
});

// ❌ 잘못된 사용법 (절대 금지)
launch_process({
  command: "npm install",
  cwd: "./frontend",        // ❌ 상대경로 금지
  cwd: "../backend",        // ❌ 상대경로 금지
  cwd: "frontend",          // ❌ 상대경로 금지
});
```

### 🚫 **터미널 사용 금지사항**
- ❌ **상대경로 사용**: `./`, `../`, `frontend/`, `backend/` 등
- ❌ **현재 디렉토리 기준**: 작업 디렉토리 가정 금지
- ❌ **경로 구분자 혼용**: Windows에서 `/` 사용 등
- ❌ **존재하지 않는 경로**: 경로 검증 없이 사용

### ✅ **터미널 사용 필수 규칙**
- ✅ **항상 절대경로**: 모든 `cwd` 매개변수에 절대경로 사용
- ✅ **경로 검증**: 명령어 실행 전 디렉토리 존재 확인
- ✅ **운영체제 고려**: Windows/Unix 경로 구분자 올바르게 사용
- ✅ **오류 처리**: 터미널 명령어 실행 오류 시 즉시 수정

### 📋 **터미널 경로 체크리스트**
작업 완료 전 필수 확인:
- [ ] 모든 `launch-process` 호출에서 절대경로 사용
- [ ] `cwd` 매개변수에 상대경로 없음 (`./`, `../` 등)
- [ ] 경로 구분자 올바르게 사용 (Windows: `\\`, Unix: `/`)
- [ ] 존재하지 않는 경로로 명령어 실행 시도 없음
- [ ] 터미널 명령어 실행 오류 없음

### ⚠️ **위반 시 처리방법**
1. **즉시 중단**: 상대경로 발견 시 작업 즉시 중단
2. **경로 수정**: 모든 상대경로를 절대경로로 변경
3. **재실행**: 수정된 절대경로로 명령어 재실행
4. **검증**: 모든 터미널 명령어 정상 실행 확인

이 가이드를 통해 Shrimp AI Agent가 오류 없는 고품질 개발을 수행할 수 있습니다! 🚀
