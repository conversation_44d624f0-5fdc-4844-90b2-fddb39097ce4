import React from 'react';
import { TechSidebar } from '@/components/intro/TechSidebar';

interface IntroLayoutProps {
  children: React.ReactNode;
}

/**
 * IntroLayout: 기술/사업소개 페이지 공통 레이아웃
 * - 좌측: TechSidebar
 * - 우측: 본문(children)
 */
const IntroLayout: React.FC<IntroLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex bg-gradient-to-b from-etri-blue-50 to-white">
      {/* 좌측: 사이드바 (고정 너비) */}
      <TechSidebar />
      {/* 우측: 본문 (가변) */}
      <main className="flex-1 min-w-0">{children}</main>
    </div>
  );
};

export default IntroLayout; 