import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';

// 사이드바 메뉴 아이템 타입
export interface SidebarMenuItem {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  subItems?: SidebarSubItem[];
}

// 서브 메뉴 아이템 타입
export interface SidebarSubItem {
  id: string;
  name: string;
  path: string;
}

// 사이드바 컴포넌트 Props
export interface SidebarProps {
  title: string;
  menuItems: SidebarMenuItem[];
  className?: string;
  width?: 'w-64' | 'w-72' | 'w-80' | 'w-96';
}

export const Sidebar: React.FC<SidebarProps> = ({
  title,
  menuItems,
  className = '',
  width = 'w-80'
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // 현재 열려있는 메뉴 ID
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  // 서브메뉴 표시 상태 (메뉴별)
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});
  // 서브메뉴 DOM 렌더링 상태 (애니메이션용)
  const [visibleSubMenus, setVisibleSubMenus] = useState<Record<string, boolean>>({});
  
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // 현재 경로에서 선택된 메뉴와 서브메뉴 찾기
  const findSelectedItems = () => {
    for (const menuItem of menuItems) {
      // 메인 메뉴 경로 체크
      if (location.pathname === menuItem.path || location.pathname.startsWith(menuItem.path + '/')) {
        // 서브메뉴가 있는 경우 서브메뉴에서 찾기
        if (menuItem.subItems) {
          for (const subItem of menuItem.subItems) {
            if (location.pathname === subItem.path || location.pathname.startsWith(subItem.path + '/')) {
              return { selectedMenuId: menuItem.id, selectedSubItemId: subItem.id };
            }
          }
          // 서브메뉴에서 찾지 못했지만 메인 경로와 일치하는 경우
          return { selectedMenuId: menuItem.id, selectedSubItemId: null };
        }
        return { selectedMenuId: menuItem.id, selectedSubItemId: null };
      }
    }
    return { selectedMenuId: null, selectedSubItemId: null };
  };

  const { selectedMenuId, selectedSubItemId } = findSelectedItems();

  // 경로 변경 시 메뉴 상태 동기화
  useEffect(() => {
    if (selectedMenuId) {
      setOpenMenuId(selectedMenuId);
      
      // 서브메뉴가 있고 서브아이템이 선택된 경우 자동으로 펼치기
      const selectedMenu = menuItems.find(item => item.id === selectedMenuId);
      if (selectedMenu?.subItems && selectedSubItemId) {
        setExpandedMenus(prev => ({ ...prev, [selectedMenuId]: true }));
      }
    }
  }, [location.pathname, selectedMenuId, selectedSubItemId, menuItems]);

  // 서브메뉴 펼침 상태 변경 시 DOM 렌더링 상태 동기화
  useEffect(() => {
    Object.keys(expandedMenus).forEach(menuId => {
      if (expandedMenus[menuId]) {
        setVisibleSubMenus(prev => ({ ...prev, [menuId]: true }));
      }
    });
  }, [expandedMenus]);

  // 메인 메뉴 클릭 핸들러
  const handleMenuClick = (menuItem: SidebarMenuItem) => {
    if (openMenuId !== menuItem.id) {
      // 다른 메뉴 선택 시 해당 경로로 이동
      navigate(menuItem.path);
      setOpenMenuId(menuItem.id);
    } else if (menuItem.subItems) {
      // 같은 메뉴 클릭 시 서브메뉴 토글 (서브메뉴가 있는 경우만)
      if (selectedSubItemId) {
        // 서브아이템이 선택된 상태에서 메인 메뉴 클릭 시 메인으로 이동
        navigate(menuItem.path);
      } else {
        // 서브메뉴 토글
        setExpandedMenus(prev => ({ ...prev, [menuItem.id]: !prev[menuItem.id] }));
      }
    }
  };

  // 화살표 클릭 핸들러 (서브메뉴 토글)
  const handleArrowClick = (e: React.MouseEvent, menuId: string) => {
    e.stopPropagation();
    setExpandedMenus(prev => ({ ...prev, [menuId]: !prev[menuId] }));
    setOpenMenuId(menuId);
  };

  // 서브메뉴 클릭 핸들러
  const handleSubItemClick = (subItem: SidebarSubItem, menuId: string) => {
    navigate(subItem.path);
    setOpenMenuId(menuId);
    if (!expandedMenus[menuId]) {
      setExpandedMenus(prev => ({ ...prev, [menuId]: true }));
    }
  };

  // 서브메뉴 애니메이션 종료 핸들러
  const handleSubMenuTransitionEnd = (menuId: string) => {
    if (!expandedMenus[menuId]) {
      setVisibleSubMenus(prev => ({ ...prev, [menuId]: false }));
    }
  };

  // 서브메뉴 최대 높이 계산
  const getSubMenuMaxHeight = (menuId: string) => {
    const menuItem = menuItems.find(item => item.id === menuId);
    if (!menuItem?.subItems) return 0;
    return menuItem.subItems.length * 36 + 16; // 36px per item + margin
  };

  return (
    <div className={`${width} flex-shrink-0 ${className}`}>
      <div className="p-6">
        {/* 사이드바 제목 */}
        <div className="mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-2">{title}</h2>
          <div className="w-8 h-0.5 bg-gray-300"></div>
        </div>

        {/* 메뉴 목록 */}
        <nav className="space-y-2">
          {menuItems.map((menuItem) => {
            const isSelected = selectedMenuId === menuItem.id;
            const isExpanded = expandedMenus[menuItem.id];
            const isSubMenuVisible = visibleSubMenus[menuItem.id];
            const IconComponent = menuItem.icon;

            return (
              <div key={menuItem.id}>
                {/* 메인 메뉴 버튼 */}
                <button
                  onClick={() => handleMenuClick(menuItem)}
                  className={`group flex items-center px-4 py-2.5 text-base rounded-lg w-full text-left transition-all duration-200 relative ${
                    isSelected
                      ? 'text-gray-900 bg-gray-100 border-l-3 border-etri-blue-600 font-medium'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <IconComponent className="w-5 h-5 mr-3 transition-colors duration-200" />
                  <span className="flex-1">{menuItem.name}</span>
                  {menuItem.subItems && (
                    <ChevronRight
                      onClick={(e) => handleArrowClick(e, menuItem.id)}
                      className={`w-4 h-4 transition-all duration-200 cursor-pointer ${
                        isExpanded ? 'rotate-90' : ''
                      } ${isSelected ? 'text-gray-600' : 'text-gray-400 group-hover:text-gray-600'}`}
                    />
                  )}
                </button>

                {/* 서브메뉴 */}
                {menuItem.subItems && (
                  <div
                    ref={(el) => (subMenuRefs.current[menuItem.id] = el)}
                    className="overflow-hidden transition-all duration-500 ease-in-out"
                    style={{
                      maxHeight: isExpanded ? getSubMenuMaxHeight(menuItem.id) : 0,
                      opacity: isExpanded ? 1 : 0,
                    }}
                    onTransitionEnd={() => handleSubMenuTransitionEnd(menuItem.id)}
                  >
                    {isSubMenuVisible && (
                      <div className="mt-3 space-y-1 pl-4">
                        <div className="w-6 h-px bg-gray-300 mb-3"></div>
                        {menuItem.subItems.map((subItem, index) => (
                          <button
                            key={subItem.id}
                            onClick={() => handleSubItemClick(subItem, menuItem.id)}
                            className={`group flex items-center px-3 py-2 text-sm rounded-md w-full text-left transition-all duration-200 relative ${
                              selectedSubItemId === subItem.id
                                ? 'text-gray-900 bg-gray-100 border-l-2 border-etri-blue-600 font-medium'
                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                            }`}
                            style={{
                              animationDelay: `${index * 50}ms`,
                              animation: isExpanded ? 'slideInLeft 0.3s ease-out forwards' : 'none'
                            }}
                          >
                            <div className={`w-1.5 h-1.5 rounded-full mr-3 transition-all duration-200 ${
                              selectedSubItemId === subItem.id
                                ? 'bg-etri-blue-600'
                                : 'bg-gray-400 group-hover:bg-gray-600'
                            }`}></div>
                            <span className={selectedSubItemId === subItem.id ? 'font-medium' : ''}>
                              {subItem.name}
                            </span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </nav>
      </div>
    </div>
  );
};
