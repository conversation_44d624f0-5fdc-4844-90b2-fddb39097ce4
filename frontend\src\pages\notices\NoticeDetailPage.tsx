import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { mockNotices } from '@/data/mockNotices';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Eye, Calendar, User, Download, FileText, Pin } from 'lucide-react';
import { format } from 'date-fns';
import { ko } from 'date-fns/locale';
import { cn } from '@/lib/utils';

const categoryConfig = {
  general: { label: '일반', color: 'bg-gray-100 text-gray-800' },
  important: { label: '중요', color: 'bg-blue-100 text-blue-800' },
  urgent: { label: '긴급', color: 'bg-red-100 text-red-800' },
  event: { label: '행사', color: 'bg-green-100 text-green-800' },
};

const statusConfig = {
  published: { label: '게시됨', color: 'bg-green-100 text-green-800' },
  draft: { label: '임시저장', color: 'bg-yellow-100 text-yellow-800' },
  archived: { label: '보관됨', color: 'bg-gray-100 text-gray-800' },
};

export const NoticeDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // 더미 데이터에서 공지사항 찾기
  const notice = mockNotices.find(n => n.id === id);

  if (!notice) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            공지사항을 찾을 수 없습니다
          </h2>
          <p className="text-gray-600 mb-6">
            요청하신 공지사항이 존재하지 않거나 삭제되었습니다.
          </p>
          <Button onClick={() => navigate('/notices')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            공지사항 목록으로
          </Button>
        </div>
      </div>
    );
  }

  const categoryInfo = categoryConfig[notice.category];
  const statusInfo = statusConfig[notice.status];

  const handleFileDownload = (fileId: string, fileName: string) => {
    // 실제 구현에서는 파일 다운로드 API 호출
    console.log(`Downloading file: ${fileName} (${fileId})`);
    alert(`파일 다운로드: ${fileName}\n(실제 구현에서는 파일이 다운로드됩니다)`);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 뒤로가기 버튼 */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/notices')}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          공지사항 목록으로
        </Button>
      </div>

      {/* 공지사항 상세 내용 */}
      <Card className={cn(
        notice.isPinned && "border-etri-blue bg-blue-50/30"
      )}>
        <CardHeader className="pb-6">
          {/* 배지들 */}
          <div className="flex flex-wrap items-center gap-2 mb-4">
            {notice.isPinned && (
              <div className="flex items-center gap-1 text-etri-blue">
                <Pin className="h-4 w-4" />
                <span className="text-sm font-medium">고정됨</span>
              </div>
            )}
            <Badge className={categoryInfo.color}>
              {categoryInfo.label}
            </Badge>
            <Badge className={statusInfo.color}>
              {statusInfo.label}
            </Badge>
            {notice.isImportant && (
              <Badge className="bg-red-100 text-red-800">
                중요
              </Badge>
            )}
          </div>

          {/* 제목 */}
          <CardTitle className={cn(
            "text-2xl md:text-3xl leading-tight",
            notice.isPinned && "text-etri-blue"
          )}>
            {notice.title}
          </CardTitle>

          {/* 메타 정보 */}
          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 pt-4 border-t">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>{notice.author.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>
                {format(notice.createdAt, 'yyyy년 MM월 dd일 HH:mm', { locale: ko })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <span>조회 {notice.viewCount.toLocaleString()}회</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 요약 */}
          {notice.summary && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">📋 요약</h3>
              <p className="text-gray-700">{notice.summary}</p>
            </div>
          )}

          {/* 본문 내용 */}
          <div 
            className="prose prose-gray max-w-none"
            dangerouslySetInnerHTML={{ __html: notice.content }}
          />

          {/* 첨부 파일 */}
          {notice.files.length > 0 && (
            <div className="border-t pt-6">
              <h3 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                첨부 파일 ({notice.files.length}개)
              </h3>
              <div className="space-y-3">
                {notice.files.map((file) => (
                  <div 
                    key={file.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <FileText className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-gray-900 truncate">
                          {file.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatFileSize(file.size)} • {format(file.uploadedAt, 'yyyy.MM.dd', { locale: ko })}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFileDownload(file.id, file.name)}
                      className="flex-shrink-0"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      다운로드
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 태그 */}
          {notice.tags.length > 0 && (
            <div className="border-t pt-6">
              <h3 className="font-medium text-gray-900 mb-3">🏷️ 태그</h3>
              <div className="flex flex-wrap gap-2">
                {notice.tags.map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="text-sm"
                  >
                    #{tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 관리자 액션 버튼 (관리자용) */}
      <div className="flex justify-end gap-3 mt-6">
        <Button variant="outline">
          편집
        </Button>
        <Button variant="outline">
          삭제
        </Button>
      </div>
    </div>
  );
};
