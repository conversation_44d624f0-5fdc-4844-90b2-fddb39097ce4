# API Specification Template

## 🎯 목적
백엔드 API 설계 및 구현을 위한 **범용 가이드라인 템플릿**입니다. 이 템플릿을 참고하여 **프로젝트별 기술 스택에 맞는 실제 API 명세서**를 생성할 수 있습니다.

## ⚙️ 사용 방법
```markdown
이 템플릿은 직접 사용하지 않고, 다음과 같이 활용합니다:

1. **프로젝트 기술 스택 확인**: Node.js + Express + PostgreSQL + JWT
2. **AI에게 커스터마이징 요청**: 
   "api-specification-template.md를 참고해서 
   Node.js + Express + PostgreSQL + JWT 프로젝트에 맞는 
   실제 api-specification.md를 생성해줘"
3. **프로젝트별 맞춤 API 명세서 완성**

## 템플릿 구조:
✅ **고정 가이드라인**: RESTful 원칙, HTTP 상태 코드, URL 규칙
🔧 **선택 옵션들**: 인증 방식, 데이터베이스, 에러 처리 방식
```

## 🌐 API 설계 기본 원칙 (고정)

### RESTful API 기본 규칙
```markdown
## 1. URL 설계 원칙

### 기본 구조
```
[Base URL]/api/v1/[Resource]/[ID]/[Sub-Resource]
```

### URL 네이밍 규칙
- **소문자 사용**: `/users` (O), `/Users` (X)
- **복수형 사용**: `/users` (O), `/user` (X)
- **하이픈 사용**: `/user-profiles` (O), `/user_profiles` (X)
- **동사 금지**: `/getUsers` (X), `/users` (O)

### HTTP 메서드 사용
- **GET**: 데이터 조회 (멱등성)
- **POST**: 데이터 생성
- **PUT**: 전체 데이터 수정 (멱등성)
- **PATCH**: 부분 데이터 수정
- **DELETE**: 데이터 삭제 (멱등성)

### 예시
```
GET    /api/v1/users           # 사용자 목록 조회
GET    /api/v1/users/123       # 특정 사용자 조회
POST   /api/v1/users           # 사용자 생성
PUT    /api/v1/users/123       # 사용자 전체 정보 수정
PATCH  /api/v1/users/123       # 사용자 부분 정보 수정
DELETE /api/v1/users/123       # 사용자 삭제
```
```

### HTTP 상태 코드 표준
```markdown
## 2. HTTP 상태 코드

### 성공 응답 (2xx)
- **200 OK**: 성공적인 GET, PUT, PATCH
- **201 Created**: 성공적인 POST (새 리소스 생성)
- **204 No Content**: 성공적인 DELETE

### 클라이언트 에러 (4xx)
- **400 Bad Request**: 잘못된 요청 데이터
- **401 Unauthorized**: 인증 필요
- **403 Forbidden**: 권한 없음
- **404 Not Found**: 리소스 없음
- **409 Conflict**: 데이터 충돌 (중복 등)
- **422 Unprocessable Entity**: 유효성 검사 실패

### 서버 에러 (5xx)
- **500 Internal Server Error**: 서버 내부 오류
- **502 Bad Gateway**: 업스트림 서버 오류
- **503 Service Unavailable**: 서비스 일시 중단
```

## 📋 표준 응답 형식

### 성공 응답 구조
```json
// 단일 리소스 응답
{
  "success": true,
  "data": {
    "id": "123",
    "name": "홍길동",
    "email": "<EMAIL>",
    "createdAt": "2024-01-15T10:30:00Z"
  },
  "message": "사용자 정보를 성공적으로 조회했습니다"
}

// 목록 응답 (페이지네이션 포함)
{
  "success": true,
  "data": [
    {
      "id": "123",
      "name": "홍길동",
      "email": "<EMAIL>"
    },
    {
      "id": "124", 
      "name": "김철수",
      "email": "<EMAIL>"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "totalPages": 15,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "사용자 목록을 성공적으로 조회했습니다"
}

// 생성 성공 응답
{
  "success": true,
  "data": {
    "id": "125",
    "name": "이영희",
    "email": "<EMAIL>",
    "createdAt": "2024-01-15T11:00:00Z"
  },
  "message": "사용자가 성공적으로 생성되었습니다"
}
```

### 에러 응답 구조
```json
// 일반 에러 응답
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "사용자를 찾을 수 없습니다",
    "details": {
      "userId": "999",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  }
}

// 유효성 검사 에러
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "입력 데이터가 유효하지 않습니다",
    "details": {
      "fields": [
        {
          "field": "email",
          "message": "올바른 이메일 형식이 아닙니다"
        },
        {
          "field": "password",
          "message": "비밀번호는 최소 8자 이상이어야 합니다"
        }
      ]
    }
  }
}

// 인증 에러
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "인증에 실패했습니다",
    "details": {
      "reason": "토큰이 만료되었습니다"
    }
  }
}
```

## 🔐 인증 및 보안 (프로젝트별 선택)

### 인증 방식 옵션

#### Option 1: JWT 기반 인증 (Node.js + Express)
```typescript
// JWT 토큰 구조 예시
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

// 헤더 형식
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 로그인 API
POST /api/v1/auth/login
{
  "email": "<EMAIL>", 
  "password": "password123"
}
// 응답: accessToken + refreshToken
```

#### Option 2: OAuth 2.0 (소셜 로그인)
```typescript
// Google OAuth 예시
GET /api/v1/auth/google
// → Google 로그인 페이지로 리다이렉트

GET /api/v1/auth/google/callback?code=...
// → 토큰 발급 및 사용자 정보 저장
```

#### Option 3: Session 기반 (전통적 방식)
```typescript
// 쿠키 기반 세션
Set-Cookie: sessionId=abc123; HttpOnly; Secure; SameSite=strict

// 로그인 API
POST /api/v1/auth/login
// 응답: 세션 생성, 쿠키 설정
```

**🔧 프로젝트별 선택 가이드:**
- **JWT**: SPA, 모바일 앱, 마이크로서비스
- **OAuth**: 소셜 로그인 필요한 서비스
- **Session**: 전통적인 웹 애플리케이션
```markdown
## 3. 인증 시스템

### 인증 플로우
1. **로그인**: POST /api/v1/auth/login
2. **토큰 발급**: accessToken + refreshToken 반환
3. **API 요청**: Authorization 헤더에 Bearer 토큰 포함
4. **토큰 갱신**: POST /api/v1/auth/refresh

### 헤더 형식
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

### 인증 API 예시
```typescript
// 로그인
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 응답
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 3600,
    "user": {
      "id": "123",
      "email": "<EMAIL>",
      "name": "홍길동"
    }
  }
}

// 토큰 갱신
POST /api/v1/auth/refresh
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

// 로그아웃
POST /api/v1/auth/logout
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 권한 관리
```typescript
// 권한 레벨
enum Role {
  USER = 'user',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

// 권한 체크 미들웨어 사용 예시
GET /api/v1/admin/users     // admin 권한 필요
GET /api/v1/users/me        // 로그인한 사용자만
```
```

## 📝 데이터 검증 및 변환 (기술 스택별)

### 검증 라이브러리 옵션

#### Option 1: Node.js + Express 
```typescript
// Joi 사용 예시
import Joi from 'joi';

const createUserSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).pattern(/^(?=.*[A-Za-z])(?=.*\d)/).required(),
  age: Joi.number().integer().min(1).max(120).optional()
});

// Express 미들웨어
app.post('/users', validate(createUserSchema), createUser);
```

#### Option 2: FastAPI (Python)
```python
from pydantic import BaseModel, EmailStr, validator

class CreateUserRequest(BaseModel):
    name: str
    email: EmailStr
    password: str
    age: Optional[int] = None
    
    @validator('name')
    def name_length(cls, v):
        if len(v) < 2 or len(v) > 50:
            raise ValueError('이름은 2-50자여야 합니다')
        return v
    
    @validator('password')
    def password_strength(cls, v):
        if len(v) < 8:
            raise ValueError('비밀번호는 8자 이상이어야 합니다')
        return v
```

#### Option 3: NestJS (TypeScript)
```typescript
import { IsEmail, IsString, MinLength, MaxLength, IsOptional } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;

  @IsOptional()
  @IsNumber()
  age?: number;
}
```

**🔧 기술 스택별 추천:**
- **Node.js + Express**: Joi, Yup
- **NestJS**: class-validator
- **FastAPI**: Pydantic (내장)
- **Django**: Django Forms, DRF Serializers
```typescript
// 사용자 생성 요청 검증
interface CreateUserRequest {
  name: string;          // 필수, 2-50자
  email: string;         // 필수, 이메일 형식
  password: string;      // 필수, 8자 이상, 영문+숫자
  age?: number;          // 선택, 1-120
  role?: 'user' | 'admin'; // 선택, 기본값: 'user'
}

// 검증 규칙 예시
const createUserSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 2,
    maxLength: 50,
    trim: true
  },
  email: {
    required: true,
    type: 'email',
    lowercase: true
  },
  password: {
    required: true,
    type: 'string',
    minLength: 8,
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]/
  },
  age: {
    required: false,
    type: 'number',
    min: 1,
    max: 120
  }
};
```

### 출력 데이터 변환
```typescript
// 민감한 정보 제외
interface UserResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // password 필드 제외
  // refreshToken 필드 제외
}

// 날짜 형식 표준화 (ISO 8601)
const formatDate = (date: Date): string => {
  return date.toISOString(); // "2024-01-15T10:30:00.000Z"
};
```

## 🔍 페이지네이션 및 필터링

### 페이지네이션 구현
```typescript
// 쿼리 파라미터
interface PaginationQuery {
  page?: number;     // 기본값: 1
  limit?: number;    // 기본값: 10, 최대: 100
  sort?: string;     // 예: "createdAt:desc"
}

// URL 예시
GET /api/v1/users?page=2&limit=20&sort=createdAt:desc

// 응답에 페이지네이션 정보 포함
{
  "data": [...],
  "pagination": {
    "page": 2,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": true
  }
}
```

### 필터링 및 검색
```typescript
// 검색 쿼리 파라미터
interface SearchQuery extends PaginationQuery {
  search?: string;        // 통합 검색
  name?: string;          // 이름 필터
  email?: string;         // 이메일 필터
  role?: string;          // 역할 필터
  isActive?: boolean;     // 활성 상태 필터
  createdAfter?: string;  // 생성일 이후
  createdBefore?: string; // 생성일 이전
}

// URL 예시
GET /api/v1/users?search=홍길동&role=user&isActive=true&page=1&limit=10

// 다중 값 필터 (쉼표로 구분)
GET /api/v1/users?role=user,admin&sort=createdAt:desc
```

## 📊 CRUD API 패턴

### 표준 CRUD 엔드포인트
```typescript
// 사용자 CRUD API 예시

// 1. 목록 조회 (페이지네이션 + 필터링)
GET /api/v1/users?page=1&limit=10&search=홍길동

// 2. 상세 조회
GET /api/v1/users/123

// 3. 생성
POST /api/v1/users
{
  "name": "홍길동",
  "email": "<EMAIL>",
  "password": "password123"
}

// 4. 전체 수정
PUT /api/v1/users/123
{
  "name": "홍길동",
  "email": "<EMAIL>",
  "isActive": true
}

// 5. 부분 수정
PATCH /api/v1/users/123
{
  "name": "홍길순"  // 이름만 수정
}

// 6. 삭제
DELETE /api/v1/users/123

// 7. 일괄 삭제 (선택적)
DELETE /api/v1/users
{
  "ids": ["123", "124", "125"]
}
```

### 관계형 리소스 API
```typescript
// 사용자의 게시글 관리
GET    /api/v1/users/123/posts           // 사용자의 게시글 목록
POST   /api/v1/users/123/posts           // 사용자가 게시글 작성
GET    /api/v1/users/123/posts/456       // 특정 게시글 조회
PUT    /api/v1/users/123/posts/456       // 게시글 수정
DELETE /api/v1/users/123/posts/456       // 게시글 삭제

// 또는 독립적인 리소스로 관리
GET    /api/v1/posts?userId=123          // 필터로 사용자 게시글 조회
POST   /api/v1/posts                     // 게시글 작성 (userId 포함)
```

## 🔄 파일 업로드 API (저장소별 옵션)

### 파일 저장 방식 옵션

#### Option 1: AWS S3 + Multer (Node.js)
```typescript
import multer from 'multer';
import AWS from 'aws-sdk';

// S3 설정
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY,
  secretAccessKey: process.env.AWS_SECRET_KEY,
  region: process.env.AWS_REGION
});

// 파일 업로드
POST /api/v1/upload/image
Content-Type: multipart/form-data

// 응답
{
  "success": true,
  "data": {
    "fileId": "file_123",
    "url": "https://mybucket.s3.amazonaws.com/images/123456789_image.jpg",
    "thumbnailUrl": "https://mybucket.s3.amazonaws.com/thumbnails/123456789_image.jpg"
  }
}
```

#### Option 2: 로컬 저장 + Express
```typescript
import multer from 'multer';
import path from 'path';

const storage = multer.diskStorage({
  destination: './uploads/',
  filename: (req, file, cb) => {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

// 응답
{
  "success": true,
  "data": {
    "fileId": "file_123",
    "url": "/uploads/1705123456789_image.jpg",
    "localPath": "/uploads/1705123456789_image.jpg"
  }
}
```

#### Option 3: Cloudinary
```typescript
import cloudinary from 'cloudinary';

// 응답
{
  "success": true,
  "data": {
    "fileId": "cloudinary_public_id",
    "url": "https://res.cloudinary.com/demo/image/upload/v1705123456/sample.jpg",
    "thumbnailUrl": "https://res.cloudinary.com/demo/image/upload/c_thumb,w_200/v1705123456/sample.jpg"
  }
}
```

**🔧 저장소별 선택 가이드:**
- **AWS S3**: 확장성, 글로벌 CDN, 대용량 파일
- **로컬 저장**: 개발/테스트, 간단한 프로젝트
- **Cloudinary**: 이미지 최적화, 변환 기능 필요
```typescript
// 파일 업로드
POST /api/v1/upload/image
Content-Type: multipart/form-data

// 요청 body (FormData)
{
  file: [File],
  category?: 'profile' | 'product' | 'document'
}

// 응답
{
  "success": true,
  "data": {
    "fileId": "file_123",
    "originalName": "profile.jpg",
    "fileName": "123456789_profile.jpg",
    "fileSize": 1024000,
    "mimeType": "image/jpeg",
    "url": "https://cdn.example.com/images/123456789_profile.jpg",
    "thumbnailUrl": "https://cdn.example.com/thumbnails/123456789_profile.jpg"
  }
}
```

### 다중 파일 업로드
```typescript
// 다중 파일 업로드
POST /api/v1/upload/images
Content-Type: multipart/form-data

// 요청 body
{
  files: [File, File, File],  // 최대 5개
  category?: string
}

// 응답
{
  "success": true,
  "data": {
    "files": [
      {
        "fileId": "file_123",
        "originalName": "image1.jpg",
        "url": "https://cdn.example.com/images/image1.jpg"
      },
      {
        "fileId": "file_124", 
        "originalName": "image2.jpg",
        "url": "https://cdn.example.com/images/image2.jpg"
      }
    ],
    "totalSize": 2048000,
    "uploadedCount": 2
  }
}
```

## ⚠️ 에러 처리 패턴 (프로젝트별 커스터마이징)

### 에러 코드 체계 (비즈니스 로직별)
```typescript
// 기본 에러 코드 구조 (프로젝트별 확장)
enum BaseErrorCode {
  // 공통 에러 (모든 프로젝트)
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED', 
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  
  // 프로젝트별 추가 예시
  // 전자상거래: PRODUCT_OUT_OF_STOCK, ORDER_ALREADY_SHIPPED
  // 중고거래: ITEM_ALREADY_SOLD, SELLER_INACTIVE  
  // 교육: COURSE_NOT_ENROLLED, ASSIGNMENT_OVERDUE
}

// 프로젝트별 에러 코드 확장 예시 (중고책 거래 플랫폼)
enum BookTradeErrorCode {
  BOOK_ALREADY_SOLD = 'BOOK_ALREADY_SOLD',
  SELLER_INACTIVE = 'SELLER_INACTIVE', 
  UNIVERSITY_NOT_VERIFIED = 'UNIVERSITY_NOT_VERIFIED',
  BOOK_CONDITION_MISMATCH = 'BOOK_CONDITION_MISMATCH'
}
```

### 데이터베이스별 에러 처리

#### PostgreSQL + Prisma
```typescript
// Prisma 에러 처리
try {
  const user = await prisma.user.create(userData);
} catch (error) {
  if (error.code === 'P2002') {
    // Unique constraint 위반
    return { error: 'USER_ALREADY_EXISTS' };
  }
  if (error.code === 'P2025') {
    // Record not found
    return { error: 'USER_NOT_FOUND' };
  }
}
```

#### MongoDB + Mongoose
```typescript
// Mongoose 에러 처리
try {
  const user = await User.create(userData);
} catch (error) {
  if (error.code === 11000) {
    // Duplicate key 에러
    return { error: 'USER_ALREADY_EXISTS' };
  }
  if (error.name === 'ValidationError') {
    return { error: 'VALIDATION_FAILED' };
  }
}
```

**🔧 프로젝트별 에러 정의 가이드:**
1. **비즈니스 도메인 분석** (사용자, 상품, 주문 등)
2. **도메인별 주요 에러 상황 식별**
3. **에러 코드 네이밍 규칙 적용** ([DOMAIN]_[REASON])
4. **사용자 친화적 메시지 작성**
```typescript
// 에러 코드 네이밍 규칙: [DOMAIN]_[ACTION]_[REASON]
enum ErrorCode {
  // 사용자 관련
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS',
  USER_INACTIVE = 'USER_INACTIVE',
  
  // 인증 관련
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  AUTH_TOKEN_EXPIRED = 'AUTH_TOKEN_EXPIRED',
  AUTH_TOKEN_INVALID = 'AUTH_TOKEN_INVALID',
  
  // 권한 관련
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RESOURCE_ACCESS_DENIED = 'RESOURCE_ACCESS_DENIED',
  
  // 데이터 관련
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  DATA_CONFLICT = 'DATA_CONFLICT',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  
  // 시스템 관련
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR = 'DATABASE_ERROR'
}
```

### 에러 응답 예시
```json
// 400 Bad Request - 유효성 검사 실패
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "입력 데이터가 유효하지 않습니다",
    "details": {
      "fields": [
        {
          "field": "email",
          "value": "invalid-email",
          "message": "올바른 이메일 형식이 아닙니다"
        }
      ]
    }
  }
}

// 401 Unauthorized - 토큰 만료
{
  "success": false,
  "error": {
    "code": "AUTH_TOKEN_EXPIRED",
    "message": "인증 토큰이 만료되었습니다",
    "details": {
      "expiredAt": "2024-01-15T10:30:00Z",
      "refreshRequired": true
    }
  }
}

// 404 Not Found - 리소스 없음
{
  "success": false,
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "사용자를 찾을 수 없습니다",
    "details": {
      "userId": "999"
    }
  }
}

// 409 Conflict - 데이터 충돌
{
  "success": false,
  "error": {
    "code": "USER_ALREADY_EXISTS",
    "message": "이미 존재하는 이메일입니다",
    "details": {
      "email": "<EMAIL>",
      "conflictField": "email"
    }
  }
}
```

## 📊 API 문서화

### OpenAPI 3.0 주석 예시
```typescript
/**
 * @swagger
 * /api/v1/users:
 *   get:
 *     summary: 사용자 목록 조회
 *     description: 페이지네이션과 필터링을 지원하는 사용자 목록 조회 API
 *     tags: [Users]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 페이지 번호
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: 페이지당 항목 수
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 이름 또는 이메일 검색
 *     responses:
 *       200:
 *         description: 성공적으로 조회됨
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       400:
 *         description: 잘못된 요청
 *       401:
 *         description: 인증 필요
 */
```

## 🧪 API 테스트 가이드

### 테스트해야 할 항목들
```markdown
## API 테스트 체크리스트

### 기능 테스트
- [ ] **정상적인 요청/응답** 동작 확인
- [ ] **필수 필드 누락** 시 400 에러
- [ ] **잘못된 데이터 타입** 입력 시 400 에러
- [ ] **존재하지 않는 리소스** 요청 시 404 에러
- [ ] **권한 없는 접근** 시 403 에러

### 인증/권한 테스트
- [ ] **토큰 없이 요청** 시 401 에러
- [ ] **만료된 토큰** 으로 요청 시 401 에러
- [ ] **잘못된 토큰** 으로 요청 시 401 에러
- [ ] **권한 없는 사용자** 접근 시 403 에러

### 데이터 검증 테스트
- [ ] **이메일 형식** 검증
- [ ] **비밀번호 강도** 검증
- [ ] **필드 길이 제한** 검증
- [ ] **중복 데이터** 생성 시 409 에러

### 페이지네이션 테스트
- [ ] **기본 페이지네이션** 동작
- [ ] **잘못된 페이지 번호** 처리
- [ ] **페이지 크기 제한** 확인
- [ ] **정렬 옵션** 동작 확인

### 성능 테스트
- [ ] **응답 시간** 500ms 이내
- [ ] **동시 요청** 처리 능력
- [ ] **대용량 데이터** 처리
- [ ] **파일 업로드** 크기 제한
```

## 💡 AI 개발 가이드 (프로젝트별 커스터마이징)

### 프로젝트별 API 명세서 생성 방법
```markdown
## 1. 기술 스택 확인
- **백엔드**: Node.js + Express / NestJS / FastAPI / Django
- **데이터베이스**: PostgreSQL / MongoDB / MySQL
- **인증**: JWT / OAuth / Session
- **파일 저장**: AWS S3 / 로컬 / Cloudinary
- **캐싱**: Redis / 메모리 / 없음

## 2. 비즈니스 도메인 분석  
- **주요 엔티티**: User, Product, Order 등
- **비즈니스 규칙**: 재고 관리, 결제 처리, 권한 체계 등
- **외부 연동**: 결제 시스템, 메일 발송, SMS 등

## 3. API 명세서 커스터마이징 요청
"api-specification-template.md를 참고해서 
[프로젝트명 + 기술스택]에 맞는 실제 api-specification.md를 생성해줘"

예시:
"중고책 거래 플랫폼 + Node.js + Express + PostgreSQL + JWT에 맞는 
실제 api-specification.md를 생성해줘"
```

### 기술 스택별 선택 가이드
```markdown
## 인증 방식 선택
- **JWT**: SPA, 모바일 앱, 마이크로서비스 → stateless, 확장성
- **Session**: 전통적 웹앱 → 서버 상태 관리, 보안성
- **OAuth**: 소셜 로그인 → 사용자 편의성, 빠른 가입

## 데이터베이스 선택
- **PostgreSQL**: 복잡한 관계, 트랜잭션 중요 → ACID 보장
- **MongoDB**: 스키마 유연성, 빠른 개발 → NoSQL 장점
- **MySQL**: 간단한 웹앱, 레거시 → 널리 지원됨

## 파일 저장 방식
- **AWS S3**: 확장성, 글로벌 서비스 → 트래픽 많은 서비스
- **로컬**: 개발/테스트, 간단한 프로젝트 → 비용 절약
- **Cloudinary**: 이미지 중심, 자동 최적화 → 이미지 변환 필요
```

### 프로젝트별 주의사항
```markdown
## 개발 순서 (프로젝트 특성 반영)
1. **기술 스택에 맞는 검증 라이브러리** 선택
2. **비즈니스 도메인별 에러 코드** 정의  
3. **인증 방식에 맞는 미들웨어** 구현
4. **데이터베이스 특성에 맞는 쿼리** 최적화
5. **파일 저장소에 맞는 업로드 로직** 구현

## 비즈니스 로직 고려사항
- **전자상거래**: 재고 관리, 결제 처리, 배송 상태
- **SNS/커뮤니티**: 팔로우 관계, 콘텐츠 관리, 알림
- **교육 플랫폼**: 수강 관리, 진도 추적, 과제 제출
- **중고거래**: 거래 상태, 신뢰도 관리, 위치 기반

## 성능 고려사항 (규모별)
- **소규모**: 단순 CRUD, 기본 인덱스만
- **중규모**: 캐싱 도입, 쿼리 최적화, 페이지네이션
- **대규모**: 데이터베이스 샤딩, CDN, 마이크로서비스
```

### 최종 결과물 예시
```markdown
프로젝트별 커스터마이징 후 생성되는 실제 문서:

📝 api-specification.md (중고책 거래 플랫폼용)
- Node.js + Express + PostgreSQL + JWT 전용
- 책 등록/검색/거래 API
- 대학교 인증 API
- 중고거래 특화 에러 코드
- Multer + AWS S3 파일 업로드

📝 api-specification.md (전자상거래용)  
- NestJS + TypeORM + Redis 전용
- 상품/주문/결제 API
- 재고 관리 로직
- 전자상거래 특화 에러 코드
- 결제 시스템 연동
```

이 템플릿을 참고하여 **프로젝트 특성과 기술 스택에 최적화된** API 명세서를 생성할 수 있습니다.
```markdown
1. **데이터 모델 정의** (TypeScript 인터페이스)
2. **URL 구조 설계** (RESTful 원칙 준수)
3. **요청/응답 스키마 정의** (입출력 데이터 구조)
4. **에러 코드 정의** (비즈니스 로직별)
5. **미들웨어 구현** (인증, 권한, 검증)
6. **컨트롤러 구현** (비즈니스 로직)
7. **테스트 코드 작성** (단위 + 통합 테스트)
8. **API 문서 생성** (Swagger/OpenAPI)
```

### 주의사항
- **일관된 응답 형식** 유지 (success, data, error 구조)
- **적절한 HTTP 상태 코드** 사용
- **민감한 정보 제외** (password, token 등)
- **입력 데이터 검증** 필수
- **에러 메시지 명확성** (사용자가 이해할 수 있게)
- **API 버전 관리** (/api/v1/)

### 성능 최적화
- **불필요한 데이터 제외** (SELECT 최적화)
- **적절한 인덱스** 활용
- **페이지네이션** 필수 구현
- **캐싱 전략** (Redis 활용)
- **N+1 쿼리 문제** 방지

이 템플릿을 따라 일관되고 확장 가능한 RESTful API를 구현할 수 있습니다.