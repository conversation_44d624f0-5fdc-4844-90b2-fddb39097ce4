export interface Activity {
  id: string;
  title: string;
  date: string;
  category: string;
  description?: string;
  link?: string;
}

export const mockActivities: Activity[] = [
  {
    id: '1',
    title: '2024년 딥테크 밸리 혁신 기업 모집',
    date: '2024.12.15',
    category: '공모사업',
    description: 'ETRI 딥테크 밸리 생태계 참여를 위한 혁신 기업 모집을 시작합니다.',
    link: '/notices/1'
  },
  {
    id: '2',
    title: 'AI 반도체 기술 세미나 개최',
    date: '2024.12.10',
    category: '세미나',
    description: '차세대 AI 반도체 기술 동향과 ETRI의 최신 연구 성과를 공유합니다.',
    link: '/events/2'
  },
  {
    id: '3',
    title: '스타트업 멘토링 프로그램 런칭',
    date: '2024.12.08',
    category: '프로그램',
    description: '딥테크 스타트업을 위한 전문가 멘토링 프로그램이 시작됩니다.',
    link: '/programs/3'
  },
  {
    id: '4',
    title: '6G 통신 기술 연구 성과 발표',
    date: '2024.12.05',
    category: '연구성과',
    description: 'ETRI의 6G 통신 기술 연구 성과와 상용화 계획을 발표했습니다.',
    link: '/research/4'
  },
  {
    id: '5',
    title: '딥테크 투자 설명회',
    date: '2024.12.01',
    category: '투자',
    description: '딥테크 분야 투자 기회와 ETRI 밸리 생태계를 소개하는 설명회입니다.',
    link: '/investment/5'
  },
  {
    id: '6',
    title: '양자컴퓨팅 기술 워크샵',
    date: '2024.11.28',
    category: '워크샵',
    description: '양자컴퓨팅 기술의 현재와 미래에 대한 전문가 워크샵을 진행합니다.',
    link: '/workshops/6'
  }
];

export const getCategoryColor = (category: string): string => {
  const categoryColors: Record<string, string> = {
    '공모사업': 'bg-etri-blue-100 text-etri-blue-700 border-etri-blue-200',
    '세미나': 'bg-etri-orange-100 text-etri-orange-700 border-etri-orange-200',
    '프로그램': 'bg-green-100 text-green-700 border-green-200',
    '연구성과': 'bg-purple-100 text-purple-700 border-purple-200',
    '투자': 'bg-yellow-100 text-yellow-700 border-yellow-200',
    '워크샵': 'bg-pink-100 text-pink-700 border-pink-200',
    'default': 'bg-gray-100 text-gray-700 border-gray-200'
  };
  
  return categoryColors[category] || categoryColors['default'];
};
