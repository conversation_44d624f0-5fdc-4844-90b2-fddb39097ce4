import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import HomePage from './pages/HomePage';
import { NoticeListPage } from './pages/notices/NoticeListPage';
import { NoticeDetailPage } from './pages/notices/NoticeDetailPage';
import { NoticeManagePage } from './pages/admin/NoticeManagePage';
import { NoticeForm } from './components/notices/NoticeForm';
import './App.css';
import type { JSX } from 'react';
import { LoginPage, SignupPage } from './pages/auth';
import { TechListPage, TechDetailPage } from './pages/intro';
import AboutPage from './pages/intro/AboutPage';

// ETRI 스타일 네비게이션 컴포넌트 (고정 헤더 + 메가메뉴)
const Navigation: React.FC = () => {
  const [activeMenu, setActiveMenu] = React.useState<string | null>(null);
  const [menuPosition, setMenuPosition] = React.useState<{ left: number; top: number } | null>(null);
  const location = useLocation();
  // 각 메뉴별 ref 저장
  const menuRefs = React.useRef<{ [key: string]: HTMLDivElement | null }>({});

  const menuItems = [
    {
      name: '공지사항',
      href: '/notices',
      submenu: [
        { name: '공지사항', href: '/notices', description: '최신 공지사항을 확인하세요', icon: 'megaphone' },
        { name: 'Q&A', href: '/qna', description: '자주 묻는 질문과 답변', icon: 'question' },
        { name: 'FAQ', href: '/faq', description: '자주 묻는 질문 모음', icon: 'help' }
      ]
    },
    {
      name: '사업소개',
      href: '/intro/about',
      submenu: [
        { name: '사업 개요', href: '/intro/about', description: '딥테크 밸리 사업 소개', icon: 'document' },
        { name: '기술소개', href: '/intro/tech', description: '플랫폼의 주요 기술을 소개합니다', icon: 'document' }
      ]
    },
    {
      name: '밸리관리',
      href: '/valley',
      submenu: [
        { name: '밸리 현황', href: '/valley/status', description: '딥테크 밸리 현황', icon: 'location' },
        { name: '입주 기업', href: '/valley/companies', description: '입주 기업 관리', icon: 'users' },
        { name: '시설 관리', href: '/valley/facilities', description: '시설 및 인프라 관리', icon: 'cog' }
      ]
    },
    {
      name: '수요관리',
      href: '/demands',
      submenu: [
        { name: '수요 조사', href: '/demands/survey', description: '기업 수요 조사', icon: 'clipboard' },
        { name: '매칭', href: '/demands/matching', description: '수요-공급 매칭', icon: 'link' },
        { name: '분석', href: '/demands/analysis', description: '수요 분석 리포트', icon: 'analytics' }
      ]
    }
  ];

  const getIcon = (iconName: string) => {
    const icons: { [key: string]: JSX.Element } = {
      megaphone: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" /></svg>,
      question: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
      help: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
      document: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>,
      building: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" /></svg>,
      chart: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>,
      location: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>,
      users: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" /></svg>,
      cog: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>,
      clipboard: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" /></svg>,
      link: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>,
      analytics: <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
    };
    return icons[iconName] || icons.document;
  };

  // 메뉴 hover 시 해당 메뉴 아래에 메가메뉴 위치 계산
  const handleMenuEnter = (name: string) => {
    setActiveMenu(name);
    const ref = menuRefs.current[name];
    if (ref) {
      const rect = ref.getBoundingClientRect();
      setMenuPosition({
        left: rect.left,
        top: rect.bottom
      });
    }
  };
  // 메뉴 닫기 디바운스를 위한 타이머
  const closeTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // 전체 래퍼에서 마우스가 벗어나면 메뉴 닫기 (디바운스 적용)
  const handleWrapperLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setActiveMenu(null);
      setMenuPosition(null);
    }, 150); // 150ms 지연
  };

  // 마우스가 다시 들어오면 닫기 취소
  const handleWrapperEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  // 컴포넌트 언마운트 시 타이머 정리
  React.useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  return (
    // 헤더+메가메뉴 전체를 하나의 래퍼로 감싸고, 마우스 이벤트로 제어
    <div
      onMouseLeave={handleWrapperLeave}
      onMouseEnter={handleWrapperEnter}
      className="relative z-50"
    >
      <nav className="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md shadow-md border-b border-white/20 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* ETRI 로고 */}
            <div className="flex items-center space-x-3">
              <a href="/" className="flex items-center space-x-3">
                <img
                  src="/1.-wordmark.png"
                  alt="ETRI"
                  className="h-8 w-auto"
                />
                <span className="text-lg font-bold text-gray-600">딥테크 오픈플랫폼</span>
              </a>
            </div>

            {/* 중앙 메인 네비게이션 */}
            <div className="hidden md:flex space-x-8">
              {menuItems.map((item) => {
                // 메뉴 활성화 조건 보강
                const isActive =
                  (item.href === '/intro/about' && (
                    location.pathname === '/intro/about' ||
                    location.pathname.startsWith('/intro/tech')
                  ))
                  || (item.href === '/intro/tech' && location.pathname.startsWith('/intro/tech'))
                  || (item.href === '/notices' && location.pathname.startsWith('/notices'))
                  || (item.href === '/valley' && location.pathname.startsWith('/valley'))
                  || (item.href === '/demands' && location.pathname.startsWith('/demands'))
                  || location.pathname === item.href;
                return (
                  <div
                    key={item.name}
                    className="relative group"
                    ref={el => { menuRefs.current[item.name] = el; }}
                    onMouseEnter={() => handleMenuEnter(item.name)}
                  >
                    <a
                      href={item.href}
                      className={`block px-4 py-6 text-lg font-medium transition-colors duration-200 ${
                        isActive
                          ? 'text-etri-blue-600 border-b-2 border-etri-blue-600'
                          : 'text-gray-700 hover:text-etri-blue-600'
                      }`}
                    >
                      {item.name}
                    </a>
                  </div>
                );
              })}
            </div>

            {/* 우측 메뉴 */}
            <div className="flex items-center space-x-4">
              <button className="text-gray-500 hover:text-gray-700 p-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
              <button className="text-gray-500 hover:text-gray-700 p-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                </svg>
              </button>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">관리</span>
              </div>
              <a href="/login" className="px-2 py-1 rounded text-sm text-etri-blue-700 border border-etri-blue-100 hover:bg-etri-blue-50 transition">로그인</a>
              <a href="/signup" className="px-2 py-1 rounded text-sm text-etri-blue-600 border border-etri-blue-100 hover:bg-etri-blue-50 transition">회원가입</a>
            </div>
          </div>
        </div>
      </nav>

      {/* 메가메뉴: 메뉴별로 해당 메뉴 아래에 위치, 오버레이 없음 */}
      {activeMenu && menuPosition && (
        <>

          {/* 자연스러운 삼각형 화살표 */}
          <div
            className="fixed z-[1001]"
            style={{
              left: menuPosition.left + (menuRefs.current[activeMenu]?.offsetWidth || 0) / 2 - 6,
              top: menuPosition.top + 2,
              pointerEvents: 'none',
            }}
          >
            <svg width="12" height="8" viewBox="0 0 12 8" className="drop-shadow-lg">
              <path
                d="M6 0L12 8H0L6 0Z"
                fill="white"
                stroke="#d1d5db"
                strokeWidth="1"
              />
            </svg>
          </div>

          <div
            className="fixed"
            style={{
              left: Math.max(16, Math.min(menuPosition.left, window.innerWidth - 400 - 16)),
              top: menuPosition.top + 8, // 헤더 아래 약간 띄움
              minWidth: 320,
              maxWidth: 'min(400px, calc(100vw - 32px))',
              width: 'max-content',
              zIndex: 1000,
            }}
          >
            <div
              className="bg-white shadow-2xl border border-gray-300 rounded-xl p-6 animate-fadeIn"
              style={{
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
                backdropFilter: 'blur(8px)'
              }}
              role="menu"
              aria-label={`${activeMenu} 메뉴`}
              onMouseEnter={handleWrapperEnter}
              onMouseLeave={handleWrapperLeave}
            >
              <div className="grid grid-cols-1 gap-1">
                {menuItems.find(item => item.name === activeMenu)?.submenu.map((subItem) => (
                  <a
                    key={subItem.name}
                    href={subItem.href}
                    className="mega-menu-item group flex items-start gap-4 p-4 rounded-lg hover:bg-gray-50/80 transition-all duration-200 ease-out"
                    role="menuitem"
                    aria-describedby={`${subItem.name}-desc`}
                  >
                    {/* 아이콘 */}
                    <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 group-hover:bg-etri-blue-100 group-hover:text-etri-blue-600 transition-all duration-200">
                      {getIcon(subItem.icon)}
                    </div>

                    {/* 콘텐츠 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-sm font-semibold text-gray-900 group-hover:text-etri-blue-900 transition-colors duration-200">
                          {subItem.name}
                        </h3>
                      </div>
                      <p
                        id={`${subItem.name}-desc`}
                        className="text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-200 leading-relaxed"
                      >
                        {subItem.description}
                      </p>
                    </div>

                    {/* 화살표 아이콘 */}
                    <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>

                  </a>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// 공지사항 작성 페이지 래퍼
const NoticeCreatePage: React.FC = () => {
  const handleSubmit = (data: any) => {
    console.log('Creating notice:', data);
    alert('공지사항이 작성되었습니다! (실제 구현에서는 API 호출)');
    window.history.back();
  };

  const handleCancel = () => {
    window.history.back();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <NoticeForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
      />
    </div>
  );
};

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-white">
        <Navigation />
        <main>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/notices" element={<div className="pt-20"><NoticeListPage /></div>} />
            <Route path="/notices/:id" element={<div className="pt-20"><NoticeDetailPage /></div>} />
            <Route path="/admin/notices" element={<div className="pt-20"><NoticeManagePage /></div>} />
            <Route path="/admin/notices/new" element={<div className="pt-20"><NoticeCreatePage /></div>} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignupPage />} />
            <Route path="/intro/about" element={<div className="pt-20"><AboutPage /></div>} />
            <Route path="/intro/tech" element={<div className="pt-20"><TechListPage /></div>} />
            <Route path="/intro/tech/:id" element={<div className="pt-20"><TechDetailPage /></div>} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
