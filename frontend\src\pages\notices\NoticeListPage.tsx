import React, { useMemo } from 'react';
import { mockNotices } from '@/data/mockNotices';
import { Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const NoticeListPage: React.FC = () => {
  const navigate = useNavigate();

  // 정렬: 고정 공지 먼저, 그 다음 생성일 순
  const sortedNotices = useMemo(() => {
    return [...mockNotices].sort((a, b) => {
      // 고정 공지 우선
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // 생성일 내림차순
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, []);

  const handleNoticeClick = (noticeId: string) => {
    navigate(`/notices/${noticeId}`);
  };

  const handleCreateNotice = () => {
    navigate('/admin/notices/new');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* 메인 컨테이너 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* 사이드바와 메인 콘텐츠 */}
        <div className="flex gap-8">

          {/* 좌측 사이드바 */}
          <div className="w-64 flex-shrink-0">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">공지사항</h2>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <nav className="space-y-2">
                <a href="#" className="flex items-center px-4 py-3 text-base font-bold text-etri-blue-700 bg-etri-blue-100 border-l-4 border-etri-blue-600 rounded-md">
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                  </svg>
                  공지사항
                </a>
                <a href="#" className="flex items-center px-4 py-3 text-base text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md transition-colors duration-200">
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Q&A
                </a>
              </nav>
            </div>
          </div>

          {/* 메인 콘텐츠 */}
          <div className="flex-1">

            {/* 페이지 헤더 */}
            <div className="mb-10">
              <nav className="flex items-center text-base text-gray-500 mb-4" aria-label="Breadcrumb">
                <a href="/" className="hover:text-gray-700">홈</a>
                <svg className="w-5 h-5 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900 font-medium">공지사항</span>
              </nav>
              <h1 className="text-3xl font-bold text-gray-900">공지사항</h1>
            </div>

            {/* 주요 공지 섹션 */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-left">주요 공지</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {sortedNotices.filter(notice => notice.isPinned).slice(0, 3).map((notice) => (
                  <div key={notice.id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer p-5"
                       onClick={() => handleNoticeClick(notice.id)}>
                    <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                      <div className="text-gray-400">
                        <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-etri-blue-100 text-etri-blue-800">
                        중요
                      </span>
                    </div>
                    <h3 className="font-semibold text-gray-900 text-base line-clamp-2 mb-3">
                      {notice.title}
                    </h3>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>{notice.author.name}</span>
                      <span>{new Date(notice.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500 mt-2">
                      <span>조회수: {notice.viewCount.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 관리자 버튼 */}
            <div className="flex justify-end mb-8">
              <button
                onClick={handleCreateNotice}
                className="inline-flex items-center px-4 py-2 bg-etri-blue-600 text-white text-sm font-medium rounded-md hover:bg-etri-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                새 공고 작성
              </button>
            </div>

            {/* 사업공고 섹션 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 text-left mb-6">사업공고</h2>

              {/* 검색 및 필터 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    placeholder="제목, 내용 검색"
                    className="text-base border border-gray-300 rounded-md px-3 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-etri-blue-500 focus:border-transparent"
                  />
                  <button className="px-4 py-2 bg-etri-blue-600 text-white text-base rounded-md hover:bg-etri-blue-700">
                    검색
                  </button>
                </div>
                <div className="flex items-center space-x-4">
                  <select className="text-base border border-gray-300 rounded-md px-3 py-2">
                    <option>전체 분류</option>
                    <option>일반</option>
                    <option>중요</option>
                    <option>긴급</option>
                  </select>
                  <button className="flex items-center text-base text-gray-600 border border-gray-300 rounded-md px-3 py-2 hover:bg-gray-50">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                    </svg>
                    필터 적용
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">

              {/* 테이블 헤더 */}
              <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-base font-semibold text-gray-700">
                  <div className="col-span-1 text-center">순번</div>
                  <div className="col-span-1 text-center">분류</div>
                  <div className="col-span-4">제목</div>
                  <div className="col-span-2 text-center">담당자</div>
                  <div className="col-span-2 text-center">등록일</div>
                  <div className="col-span-1 text-center">조회수</div>
                  <div className="col-span-1 text-center">첨부</div>
                </div>
              </div>

              {/* 테이블 내용 */}
              <div className="divide-y divide-gray-200">
                {sortedNotices.slice(0, 10).map((notice, index) => (
                  <div key={notice.id} className="px-6 py-5 hover:bg-gray-50 cursor-pointer"
                       onClick={() => handleNoticeClick(notice.id)}>
                    <div className="grid grid-cols-12 gap-4 text-base">
                      <div className="col-span-1 text-center text-gray-900 font-medium">
                        {sortedNotices.length - index}
                      </div>
                      <div className="col-span-1 text-center">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
                          notice.category === 'important' ? 'bg-blue-100 text-blue-800' :
                          notice.category === 'urgent' ? 'bg-red-100 text-red-800' :
                          notice.category === 'event' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {notice.category === 'important' ? '중요' :
                           notice.category === 'urgent' ? '긴급' :
                           notice.category === 'event' ? '행사' : '일반'}
                        </span>
                      </div>
                      <div className="col-span-4">
                        <div className="flex items-center">
                          {notice.isPinned && (
                            <svg className="w-4 h-4 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 6.707 6.293a1 1 0 00-1.414 1.414l4 4a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                          <span className="text-etri-blue-600 hover:text-etri-blue-800 font-medium truncate">
                            {notice.title}
                          </span>
                        </div>
                      </div>
                      <div className="col-span-2 text-center text-gray-600">
                        {notice.author.name}
                      </div>
                      <div className="col-span-2 text-center text-gray-600">
                        {new Date(notice.createdAt).toLocaleDateString('ko-KR')}
                      </div>
                      <div className="col-span-1 text-center text-gray-600">
                        {notice.viewCount.toLocaleString()}
                      </div>
                      <div className="col-span-1 text-center">
                        {notice.files.length > 0 && (
                          <svg className="w-4 h-4 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                          </svg>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              </div>

              {/* 페이지네이션 - 카드 밖으로 이동 */}
              <div className="flex items-center justify-center space-x-1 mt-6">
                <button className="px-3 py-1 text-base text-gray-500 hover:text-gray-700">
                  &lt; 이전
                </button>
                <button className="px-3 py-1 text-base bg-etri-blue-600 text-white rounded">
                  1
                </button>
                <button className="px-3 py-1 text-base text-gray-700 hover:text-gray-900">
                  2
                </button>
                <button className="px-3 py-1 text-base text-gray-500 hover:text-gray-700">
                  다음 &gt;
                </button>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};


