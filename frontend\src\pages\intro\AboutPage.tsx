import React from 'react';
import { <PERSON>, Target, Users, Star, Layers, Globe } from 'lucide-react';
import { TechSidebar } from '@/components/intro/TechSidebar';

const missionList = [
  {
    icon: <Target className="w-10 h-10 text-etri-blue-600" />,
    title: '미션',
    desc: '딥테크 혁신 생태계 조성 및 기술 사업화 지원',
  },
  {
    icon: <Rocket className="w-10 h-10 text-emerald-600" />,
    title: '비전',
    desc: '글로벌 경쟁력을 갖춘 DeepTech Valley 실현',
  },
  {
    icon: <Users className="w-10 h-10 text-yellow-500" />,
    title: '핵심 가치',
    desc: '개방, 협력, 성장, 혁신',
  },
];

const serviceList = [
  {
    icon: <Star className="w-7 h-7 text-pink-500" />,
    title: '기술 매칭/이전',
    desc: '기업-기관-연구자 간 기술 매칭 및 이전 지원',
  },
  {
    icon: <Layers className="w-7 h-7 text-indigo-500" />,
    title: '사업화 지원',
    desc: '딥테크 기술의 사업화 및 성장 지원 프로그램',
  },
  {
    icon: <Globe className="w-7 h-7 text-green-500" />,
    title: '글로벌 네트워킹',
    desc: '국내외 파트너와의 협력 및 네트워킹 기회 제공',
  },
];

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* 좌측: 사이드바 */}
          <TechSidebar />
          {/* 우측: 본문 */}
          <div className="flex-1">
            {/* 브레드크럼 */}
            <nav className="flex items-center text-base text-gray-500 mb-4" aria-label="Breadcrumb">
              <a href="/" className="hover:text-gray-700">홈</a>
              <svg className="w-5 h-5 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-900 font-medium">사업소개</span>
            </nav>
            {/* 타이틀/슬로건 */}
            <h1 className="text-3xl font-bold text-gray-900 mb-2">DeepTech Valley Platform</h1>
            <div className="mb-8">
              <p className="text-lg text-gray-600 mb-2">딥테크 혁신의 시작, 미래를 연결하는 플랫폼</p>
              <span className="inline-block bg-etri-blue-50 text-etri-blue-700 rounded-full px-6 py-2 text-base font-semibold border border-etri-blue-100">Connecting Technology, Creating Value</span>
            </div>
            {/* 미션/비전/핵심가치 카드 */}
            <section className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {missionList.map((item, i) => (
                <div key={i} className="bg-white rounded-xl shadow border border-gray-100 p-8 flex flex-col items-center text-center hover:shadow-lg transition">
                  <div className="mb-2">{item.icon}</div>
                  <h3 className="mt-2 text-xl font-bold text-etri-blue-700">{item.title}</h3>
                  <p className="mt-2 text-gray-700 text-base">{item.desc}</p>
                </div>
              ))}
            </section>
            {/* 주요 서비스 */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">주요 서비스</h2>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                {serviceList.map((item, i) => (
                  <div key={i} className="bg-gray-50 rounded-lg border border-gray-200 p-6 flex flex-col items-center text-center hover:bg-white transition">
                    <div className="mb-2">{item.icon}</div>
                    <h4 className="mt-2 text-lg font-semibold text-gray-900">{item.title}</h4>
                    <p className="mt-1 text-gray-600 text-sm">{item.desc}</p>
                  </div>
                ))}
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage; 