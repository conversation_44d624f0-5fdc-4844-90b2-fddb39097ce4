import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { techList } from '@/data/techData';
import { TechSidebar } from '@/components/intro/TechSidebar';
import { TechCodeViewer } from '@/components/intro/TechCodeViewer';
import { Info, Star } from 'lucide-react';

const TAB_LIST = [
  { key: 'guide', label: '기술가이드' },
  { key: 'code', label: '소스코드' },
];

const TechDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [tab, setTab] = useState<'guide' | 'code'>('guide');
  const tech = techList.find((t) => t.id === id);

  if (!tech) {
    return <div className="p-10 text-center text-gray-500">존재하지 않는 기술입니다.</div>;
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* 좌측 사이드바 */}
          <TechSidebar />

          {/* 메인 콘텐츠 */}
          <div className="flex-1">
            <div className="mb-10">
              <nav className="flex items-center text-base text-gray-500 mb-4" aria-label="Breadcrumb">
                <a href="/" className="hover:text-gray-700">홈</a>
                <svg className="w-5 h-5 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <a href="/intro/tech" className="hover:text-gray-700">기술소개</a>
                <svg className="w-5 h-5 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900 font-medium">{tech.name}</span>
              </nav>
              <h1 className="text-3xl font-bold text-gray-900">{tech.name}</h1>
            </div>
            {/* 탭 */}
            <div className="mb-8 border-b border-gray-200">
              <nav className="flex space-x-8" aria-label="Tabs">
                {TAB_LIST.map((t) => (
                  <button
                    key={t.key}
                    onClick={() => setTab(t.key as 'guide' | 'code')}
                    className={`px-4 py-2 text-base font-medium border-b-2 transition-colors duration-200 focus:outline-none ${tab === t.key ? 'border-etri-blue-600 text-etri-blue-700' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
                  >
                    {t.label}
                  </button>
                ))}
              </nav>
            </div>
            {/* 탭 내용 */}
            <div>
              {tab === 'guide' && (
                <div className="space-y-8">
                  {/* 요약 카드 */}
                  {tech.guideSummary && (
                    <div className="bg-etri-blue-50 border-l-4 border-etri-blue-500 rounded-lg p-6 flex items-start gap-4 shadow-sm">
                      <Info className="w-7 h-7 text-etri-blue-600 flex-shrink-0 mt-1" />
                      <div>
                        <h2 className="text-xl font-bold text-etri-blue-700 mb-2">주요 요약</h2>
                        <p className="text-gray-800 text-base">{tech.guideSummary}</p>
                      </div>
                    </div>
                  )}
                  {/* 특징 리스트 */}
                  {tech.guideFeatures && tech.guideFeatures.length > 0 && (
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                      <h3 className="text-lg font-bold text-gray-900 mb-3 flex items-center"><Star className="w-5 h-5 mr-2 text-yellow-400" />주요 특징</h3>
                      <ul className="list-disc pl-6 space-y-1 text-gray-700">
                        {tech.guideFeatures.map((f, i) => (
                          <li key={i}>{f}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {/* 본문 설명 */}
                  <div className="bg-gray-50 border border-gray-100 rounded-lg p-6 shadow-sm">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">상세 설명</h3>
                    <p className="text-gray-700 whitespace-pre-line">{tech.guide}</p>
                  </div>
                </div>
              )}
              {tab === 'code' && tech.code ? (
                <div className="space-y-4">
                  <h2 className="text-xl font-bold text-etri-blue-700 mb-4 flex items-center">
                    <Star className="w-5 h-5 mr-2 text-etri-blue-500" />샘플 코드
                  </h2>
                  <TechCodeViewer code={tech.code} language={tech.codeLanguage} />
                </div>
              ) : tab === 'code' ? (
                <div className="prose max-w-none text-gray-500">
                  <p>샘플 코드가 없습니다.</p>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechDetailPage; 