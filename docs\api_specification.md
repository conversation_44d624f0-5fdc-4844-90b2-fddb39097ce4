# DeepTech Valley Platform API 명세서

## 1. API 개요

### 1.1 기본 정보
- **프로젝트명**: DeepTech Valley Platform
- **Base URL**:
  - 개발환경: `http://localhost:8000/api/v1`
  - 스테이징: `https://staging-api.deeptech-valley.com/api/v1`
  - 프로덕션: `https://api.deeptech-valley.com/api/v1`
- **인증 방식**: J<PERSON><PERSON>
- **응답 형식**: JSON
- **문자 인코딩**: UTF-8

### 1.2 공통 응답 형식
```json
{
  "success": true,
  "data": {},
  "message": "Success",
  "timestamp": "2025-06-26T10:00:00Z"
}
```

### 1.3 에러 응답 형식
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  },
  "timestamp": "2025-06-26T10:00:00Z"
}
```

## 2. 인증 API

### 2.1 로그인
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "userType": "company"
}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "userType": "company",
      "permissions": ["business:read", "demand:create"]
    }
  }
}
```

### 2.2 토큰 갱신
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 2.3 로그아웃
```http
POST /auth/logout
Authorization: Bearer {accessToken}
```

## 3. 사용자 관리 API

### 3.1 사용자 등록
```http
POST /users/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "userType": "company",
  "profile": {
    "companyName": "테크 스타트업",
    "businessType": "AI/ML",
    "companySize": "startup"
  }
}
```

### 3.2 사용자 프로필 조회
```http
GET /users/profile
Authorization: Bearer {accessToken}
```

### 3.3 사용자 프로필 수정
```http
PUT /users/profile
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "profile": {
    "companyName": "업데이트된 회사명",
    "businessType": "Blockchain"
  }
}
```

## 4. 사업소개 API

### 4.1 사업 정보 조회
```http
GET /business/info
Authorization: Bearer {accessToken}
```

### 4.2 기술 가이드 목록 조회
```http
GET /business/tech-guides
Authorization: Bearer {accessToken}
Query Parameters:
- category: string (optional)
- page: number (default: 1)
- limit: number (default: 10)
```

### 4.3 기술 가이드 상세 조회
```http
GET /business/tech-guides/{guideId}
Authorization: Bearer {accessToken}
```

### 4.4 소스코드 목록 조회
```http
GET /business/source-code
Authorization: Bearer {accessToken}
Query Parameters:
- language: string (optional)
- category: string (optional)
```

## 5. 공지관리 API

### 5.1 공지사항 목록 조회
```http
GET /notices
Query Parameters:
- page: number (default: 1)
- limit: number (default: 10)
- search: string (optional)
- category: string (optional)
```

### 5.2 공지사항 상세 조회
```http
GET /notices/{noticeId}
```

### 5.3 공지사항 작성 (관리자)
```http
POST /notices
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "title": "공지사항 제목",
  "content": "공지사항 내용",
  "category": "general",
  "isPinned": false,
  "attachments": ["file1.pdf", "file2.doc"]
}
```

### 5.4 공지사항 수정 (관리자)
```http
PUT /notices/{noticeId}
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "title": "수정된 제목",
  "content": "수정된 내용"
}
```

### 5.5 공지사항 삭제 (관리자)
```http
DELETE /notices/{noticeId}
Authorization: Bearer {accessToken}
```

## 6. 수요관리 API

### 6.1 공모사업 목록 조회
```http
GET /demands/projects
Query Parameters:
- status: string (optional) - "open", "closed", "upcoming"
- category: string (optional)
- page: number (default: 1)
- limit: number (default: 10)
```

### 6.2 공모사업 상세 조회
```http
GET /demands/projects/{projectId}
```

### 6.3 사업신청 제출
```http
POST /demands/applications
Authorization: Bearer {accessToken}
Content-Type: multipart/form-data

{
  "projectId": "project123",
  "companyInfo": {
    "name": "회사명",
    "size": "startup",
    "businessType": "AI/ML"
  },
  "projectInfo": {
    "title": "프로젝트 제목",
    "description": "프로젝트 설명",
    "budget": *********
  },
  "attachments": [File, File, ...]
}
```

### 6.4 신청서 목록 조회
```http
GET /demands/applications
Authorization: Bearer {accessToken}
Query Parameters:
- status: string (optional) - "submitted", "under_review", "approved", "rejected"
- page: number (default: 1)
- limit: number (default: 10)
```

### 6.5 신청서 상세 조회
```http
GET /demands/applications/{applicationId}
Authorization: Bearer {accessToken}
```

### 6.6 신청서 문서 생성
```http
POST /demands/applications/{applicationId}/generate-document
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "format": "pdf",
  "template": "standard"
}
```

### 6.7 신청서 문서 다운로드
```http
GET /demands/applications/{applicationId}/document
Authorization: Bearer {accessToken}
```

## 7. 밸리관리 API

### 7.1 에코시스템 데이터 조회
```http
GET /valley/ecosystem
Authorization: Bearer {accessToken}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "company1",
        "name": "테크 스타트업",
        "type": "company",
        "size": "startup",
        "x": 100,
        "y": 150
      }
    ],
    "links": [
      {
        "source": "company1",
        "target": "institution1",
        "type": "partnership",
        "strength": 0.8
      }
    ]
  }
}
```

### 7.2 참여기업 목록 조회
```http
GET /valley/companies
Authorization: Bearer {accessToken}
Query Parameters:
- size: string (optional) - "startup", "small", "medium", "large"
- businessType: string (optional)
- page: number (default: 1)
- limit: number (default: 10)
```

### 7.3 참여기업 상세 조회
```http
GET /valley/companies/{companyId}
Authorization: Bearer {accessToken}
```

## 8. 통계 API

### 8.1 전체 통계 조회
```http
GET /statistics/overview
Authorization: Bearer {accessToken}
```

**응답:**
```json
{
  "success": true,
  "data": {
    "totalCompanies": 150,
    "totalProjects": 45,
    "totalApplications": 320,
    "approvalRate": 0.65,
    "monthlyStats": [
      {
        "month": "2025-01",
        "applications": 25,
        "approvals": 16
      }
    ]
  }
}
```

### 8.2 기업 규모별 통계
```http
GET /statistics/companies-by-size
Authorization: Bearer {accessToken}
```

### 8.3 분야별 통계
```http
GET /statistics/by-business-type
Authorization: Bearer {accessToken}
```

### 8.4 지역별 통계
```http
GET /statistics/by-region
Authorization: Bearer {accessToken}
```

## 9. 파일 관리 API

### 9.1 파일 업로드
```http
POST /files/upload
Authorization: Bearer {accessToken}
Content-Type: multipart/form-data

{
  "file": File,
  "category": "application",
  "description": "사업계획서"
}
```

### 9.2 파일 다운로드
```http
GET /files/{fileId}
Authorization: Bearer {accessToken}
```

### 9.3 파일 삭제
```http
DELETE /files/{fileId}
Authorization: Bearer {accessToken}
```

## 10. 에러 코드

| 코드 | 메시지 | 설명 |
|------|--------|------|
| AUTH_001 | Invalid credentials | 잘못된 인증 정보 |
| AUTH_002 | Token expired | 토큰 만료 |
| AUTH_003 | Insufficient permissions | 권한 부족 |
| USER_001 | User not found | 사용자를 찾을 수 없음 |
| USER_002 | Email already exists | 이미 존재하는 이메일 |
| FILE_001 | File too large | 파일 크기 초과 |
| FILE_002 | Invalid file format | 지원하지 않는 파일 형식 |
| VALIDATION_001 | Required field missing | 필수 필드 누락 |
| VALIDATION_002 | Invalid format | 잘못된 형식 |

## 11. 레이트 리미팅

- **일반 API**: 1000 requests/hour
- **파일 업로드**: 100 requests/hour
- **인증 API**: 10 requests/minute

## 12. 페이지네이션

모든 목록 API는 다음 형식의 페이지네이션을 지원합니다:

```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "totalPages": 15,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```
