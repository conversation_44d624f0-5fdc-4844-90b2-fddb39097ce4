# DeepTech Valley Platform 구현 가이드

## 1. 개발 환경 설정

### 1.1 필수 도구 설치
```bash
# Node.js 18+ 설치
node --version  # v18.0.0 이상

# Python 3.11+ 설치
python --version  # 3.11.0 이상

# Git 설치
git --version
```

### 1.2 프로젝트 초기 설정

#### 프론트엔드 설정
```bash
# 프로젝트 생성
npm create vite@latest frontend -- --template react-ts
cd frontend

# 의존성 설치
npm install

# ShadcnUI 설치
npx shadcn-ui@latest init

# 추가 패키지 설치
npm install @tanstack/react-query axios react-router-dom
npm install framer-motion lucide-react
npm install react-hook-form @hookform/resolvers zod
```

#### 백엔드 설정
```bash
# 가상환경 생성
python -m venv backend
cd backend
source bin/activate  # Windows: Scripts\activate

# 의존성 설치
pip install fastapi uvicorn sqlalchemy alembic
pip install python-jose[cryptography] passlib[bcrypt]
pip install python-multipart pandas reportlab
pip install celery redis pydantic-settings
```

### 1.3 환경변수 설정

#### 프론트엔드 (.env)
```env
VITE_API_URL=http://localhost:8000
VITE_APP_TITLE=딥테크 오픈플랫폼
```

#### 백엔드 (.env)
```env
DATABASE_URL=sqlite:///./deeptech_platform.db
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
UPLOAD_DIR=./uploads
```

## 2. 프로젝트 구조 생성

### 2.1 프론트엔드 구조
```
frontend/
├── src/
│   ├── components/
│   │   ├── ui/              # ShadcnUI 컴포넌트
│   │   ├── common/          # 공통 컴포넌트
│   │   ├── layout/          # 레이아웃 컴포넌트
│   │   ├── forms/           # 폼 컴포넌트
│   │   └── charts/          # 차트 컴포넌트
│   ├── pages/
│   │   ├── auth/            # 인증 페이지
│   │   ├── dashboard/       # 대시보드
│   │   ├── admin/           # 관리자 페이지
│   │   ├── business/        # 사업소개
│   │   ├── notice/          # 공지관리
│   │   ├── demand/          # 수요관리
│   │   └── valley/          # 밸리관리
│   ├── hooks/               # 커스텀 훅
│   ├── services/            # API 서비스
│   ├── contexts/            # React Context
│   ├── utils/               # 유틸리티
│   ├── constants/           # 상수
│   ├── types/               # TypeScript 타입
│   └── assets/              # 정적 자원
├── public/
└── package.json
```

### 2.2 백엔드 구조
```
backend/
├── app/
│   ├── api/                 # API 라우터
│   │   ├── auth.py
│   │   ├── users.py
│   │   ├── business.py
│   │   ├── notices.py
│   │   ├── demands.py
│   │   ├── valley.py
│   │   ├── statistics.py
│   │   └── files.py
│   ├── core/                # 핵심 설정
│   │   ├── config.py
│   │   ├── security.py
│   │   └── database.py
│   ├── models/              # SQLAlchemy 모델
│   │   ├── user.py
│   │   ├── notice.py
│   │   ├── application.py
│   │   └── content.py
│   ├── schemas/             # Pydantic 스키마
│   ├── services/            # 비즈니스 로직
│   ├── utils/               # 유틸리티
│   └── main.py              # FastAPI 앱
├── alembic/                 # DB 마이그레이션
├── tests/                   # 테스트
└── requirements.txt
```

## 3. 핵심 구현 패턴

### 3.1 인증 시스템 구현

#### JWT 토큰 생성 (backend/app/core/security.py)
```python
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)
```

#### 프론트엔드 인증 Context (frontend/src/contexts/AuthContext.tsx)
```typescript
import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  email: string;
  userType: 'individual' | 'company' | 'institution' | 'admin';
  permissions: string[];
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  const login = async (email: string, password: string) => {
    const response = await apiClient.post('/auth/login', { email, password });
    const { accessToken, user } = response.data.data;
    
    localStorage.setItem('accessToken', accessToken);
    setUser(user);
  };

  const logout = () => {
    localStorage.removeItem('accessToken');
    setUser(null);
  };

  const hasPermission = (permission: string) => {
    return user?.permissions.includes(permission) || false;
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, hasPermission }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 3.2 API 클라이언트 설정

#### Axios 인터셉터 (frontend/src/services/apiClient.ts)
```typescript
import axios from 'axios';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000,
});

// 요청 인터셉터
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 응답 인터셉터
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

### 3.3 폼 구현 패턴

#### ShadcnUI + React Hook Form
```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const formSchema = z.object({
  companyName: z.string().min(2, '회사명은 2글자 이상이어야 합니다'),
  companySize: z.enum(['startup', 'small', 'medium', 'large']),
  projectTitle: z.string().min(5, '프로젝트 제목은 5글자 이상이어야 합니다'),
});

export const ApplicationForm = () => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      companyName: '',
      companySize: 'startup',
      projectTitle: '',
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await apiClient.post('/demands/applications', values);
      // 성공 처리
    } catch (error) {
      // 에러 처리
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="companyName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>회사명</FormLabel>
              <FormControl>
                <Input placeholder="회사명을 입력하세요" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button type="submit">제출하기</Button>
      </form>
    </Form>
  );
};
```

### 3.4 파일 업로드 구현

#### 청크 업로드 (frontend/src/components/forms/FileUpload.tsx)
```typescript
import { useState } from 'react';
import { Progress } from '@/components/ui/progress';

export const FileUpload = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const uploadFile = async (file: File) => {
    setUploading(true);
    const chunkSize = 1024 * 1024; // 1MB
    const chunks = Math.ceil(file.size / chunkSize);

    for (let i = 0; i < chunks; i++) {
      const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
      const formData = new FormData();
      formData.append('chunk', chunk);
      formData.append('chunkIndex', i.toString());
      formData.append('totalChunks', chunks.toString());
      formData.append('fileName', file.name);

      await apiClient.post('/files/upload-chunk', formData);
      setProgress(((i + 1) / chunks) * 100);
    }

    setUploading(false);
  };

  return (
    <div className="space-y-4">
      <input
        type="file"
        onChange={(e) => e.target.files?.[0] && uploadFile(e.target.files[0])}
        disabled={uploading}
      />
      {uploading && <Progress value={progress} />}
    </div>
  );
};
```

### 3.5 PDF 생성 서비스

#### ReportLab PDF 생성 (backend/app/services/document_service.py)
```python
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io

class DocumentService:
    @staticmethod
    def generate_application_pdf(application_data: dict) -> bytes:
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=A4)
        width, height = A4

        # 한글 폰트 설정 (필요시)
        # pdfmetrics.registerFont(TTFont('NanumGothic', 'NanumGothic.ttf'))
        # p.setFont('NanumGothic', 12)

        # 제목
        p.setFont('Helvetica-Bold', 16)
        p.drawString(50, height - 50, '사업신청서')

        # 내용
        p.setFont('Helvetica', 12)
        y_position = height - 100

        p.drawString(50, y_position, f"회사명: {application_data['company_name']}")
        y_position -= 30

        p.drawString(50, y_position, f"프로젝트: {application_data['project_title']}")
        y_position -= 30

        p.drawString(50, y_position, f"예산: {application_data['budget']:,}원")

        p.save()
        buffer.seek(0)
        return buffer.getvalue()
```

## 4. 데이터베이스 설계

### 4.1 사용자 모델 (backend/app/models/user.py)
```python
from sqlalchemy import Column, Integer, String, Enum, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum

Base = declarative_base()

class UserType(enum.Enum):
    INDIVIDUAL = "individual"
    COMPANY = "company"
    INSTITUTION = "institution"
    ADMIN = "admin"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    user_type = Column(Enum(UserType), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 4.2 신청서 모델 (backend/app/models/application.py)
```python
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
import enum

class ApplicationStatus(enum.Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"

class Application(Base):
    __tablename__ = "applications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    company_name = Column(String, nullable=False)
    company_size = Column(String, nullable=False)
    project_title = Column(String, nullable=False)
    project_description = Column(Text)
    budget = Column(Integer)
    status = Column(Enum(ApplicationStatus), default=ApplicationStatus.DRAFT)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = relationship("User", back_populates="applications")
```

## 5. 테스트 전략

### 5.1 백엔드 테스트 (pytest)
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_login():
    response = client.post(
        "/auth/login",
        json={"email": "<EMAIL>", "password": "testpass"}
    )
    assert response.status_code == 200
    assert "accessToken" in response.json()["data"]
```

### 5.2 프론트엔드 테스트 (Vitest + Testing Library)
```typescript
import { render, screen } from '@testing-library/react';
import { LoginPage } from '@/pages/auth/LoginPage';

test('renders login form', () => {
  render(<LoginPage />);
  expect(screen.getByLabelText(/이메일/i)).toBeInTheDocument();
  expect(screen.getByLabelText(/비밀번호/i)).toBeInTheDocument();
});
```

## 6. 배포 준비

### 6.1 프론트엔드 빌드
```bash
npm run build
npm run preview  # 빌드 결과 미리보기
```

### 6.2 백엔드 배포 설정
```python
# backend/app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="딥테크 오픈플랫폼 API", version="1.0.0")

# CORS 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://your-frontend-domain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 7. 2차 개발 - D3.js 에코시스템 맵 구현

### 7.1 D3.js와 React 통합 패턴
```typescript
// frontend/src/hooks/useD3.ts
import { useRef, useEffect } from 'react';
import * as d3 from 'd3';

export const useD3 = (renderChartFn: (svg: d3.Selection<SVGSVGElement, unknown, null, undefined>) => void, dependencies: any[]) => {
  const ref = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (ref.current) {
      const svg = d3.select(ref.current);
      renderChartFn(svg);
    }
  }, dependencies);

  return ref;
};
```

### 7.2 에코시스템 맵 컴포넌트
```typescript
// frontend/src/components/charts/EcosystemMap.tsx
import React from 'react';
import * as d3 from 'd3';
import { useD3 } from '@/hooks/useD3';

interface Node {
  id: string;
  name: string;
  type: 'company' | 'institution';
  size: number;
}

interface Link {
  source: string;
  target: string;
  strength: number;
}

export const EcosystemMap: React.FC<{ nodes: Node[]; links: Link[] }> = ({ nodes, links }) => {
  const ref = useD3((svg) => {
    const width = 800;
    const height = 600;

    // 시뮬레이션 설정
    const simulation = d3.forceSimulation(nodes)
      .force('link', d3.forceLink(links).id((d: any) => d.id))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2));

    // 링크 그리기
    const link = svg.selectAll('.link')
      .data(links)
      .enter().append('line')
      .attr('class', 'link')
      .style('stroke', '#999')
      .style('stroke-opacity', 0.6);

    // 노드 그리기
    const node = svg.selectAll('.node')
      .data(nodes)
      .enter().append('circle')
      .attr('class', 'node')
      .attr('r', (d) => d.size)
      .style('fill', (d) => d.type === 'company' ? '#3b82f6' : '#10b981')
      .call(d3.drag()
        .on('start', dragstarted)
        .on('drag', dragged)
        .on('end', dragended));

    // 시뮬레이션 업데이트
    simulation.on('tick', () => {
      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      node
        .attr('cx', (d: any) => d.x)
        .attr('cy', (d: any) => d.y);
    });

    function dragstarted(event: any, d: any) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event: any, d: any) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragended(event: any, d: any) {
      if (!event.active) simulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }
  }, [nodes, links]);

  return <svg ref={ref} width="800" height="600" />;
};
```

### 7.3 밸리관리 데이터 모델
```python
# backend/app/models/company.py
from sqlalchemy import Column, Integer, String, Text, Float, DateTime, Enum
from sqlalchemy.orm import relationship
import enum

class CompanySize(enum.Enum):
    STARTUP = "startup"
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"

class Company(Base):
    __tablename__ = "companies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    size = Column(Enum(CompanySize), nullable=False)
    industry = Column(String, nullable=False)
    description = Column(Text)
    location = Column(String)
    founded_year = Column(Integer)
    employee_count = Column(Integer)
    website = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 관계
    relationships_as_source = relationship("Relationship", foreign_keys="Relationship.source_id")
    relationships_as_target = relationship("Relationship", foreign_keys="Relationship.target_id")
```

## 8. 3차 개발 - 성능 최적화 및 접근성

### 8.1 코드 분할 및 지연 로딩
```typescript
// frontend/src/App.tsx
import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

// 지연 로딩
const MainPage = React.lazy(() => import('@/pages/MainPage'));
const Dashboard = React.lazy(() => import('@/pages/dashboard/AdminDashboard'));
const ValleyManagement = React.lazy(() => import('@/pages/valley/ValleyManagement'));

function App() {
  return (
    <Router>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<MainPage />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/valley" element={<ValleyManagement />} />
        </Routes>
      </Suspense>
    </Router>
  );
}
```

### 8.2 접근성 개선
```typescript
// frontend/src/components/accessibility/SkipToContent.tsx
import React from 'react';

export const SkipToContent: React.FC = () => {
  return (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4
                 bg-primary-600 text-white px-4 py-2 rounded-md z-50"
    >
      본문으로 바로가기
    </a>
  );
};

// 사용 예시
export const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <>
      <SkipToContent />
      <header>...</header>
      <main id="main-content" tabIndex={-1}>
        {children}
      </main>
    </>
  );
};
```

### 8.3 백업 시스템
```python
# backend/app/services/backup_service.py
import os
import subprocess
from datetime import datetime
from celery import Celery

celery_app = Celery('backup')

@celery_app.task
def backup_database():
    """데이터베이스 백업 태스크"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"backup_db_{timestamp}.sql"

    try:
        # PostgreSQL 백업 예시
        subprocess.run([
            'pg_dump',
            '-h', os.getenv('DB_HOST'),
            '-U', os.getenv('DB_USER'),
            '-d', os.getenv('DB_NAME'),
            '-f', f'/backups/{backup_file}'
        ], check=True)

        return f"Database backup completed: {backup_file}"
    except subprocess.CalledProcessError as e:
        return f"Backup failed: {str(e)}"

@celery_app.task
def backup_files():
    """파일 시스템 백업 태스크"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    try:
        subprocess.run([
            'tar', '-czf', f'/backups/files_{timestamp}.tar.gz',
            '/app/uploads'
        ], check=True)

        return f"Files backup completed: files_{timestamp}.tar.gz"
    except subprocess.CalledProcessError as e:
        return f"Files backup failed: {str(e)}"
```

## 9. 모니터링 및 로깅

### 9.1 로깅 설정
```python
import logging
from fastapi import Request

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    logger.info(f"{request.method} {request.url} - {response.status_code} - {process_time:.2f}s")
    return response
```

### 9.2 헬스체크 엔드포인트
```python
# backend/app/api/health.py
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db

router = APIRouter()

@router.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """시스템 헬스체크"""
    try:
        # 데이터베이스 연결 확인
        db.execute("SELECT 1")

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow(),
            "database": "connected",
            "version": "1.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow(),
            "error": str(e)
        }
```
