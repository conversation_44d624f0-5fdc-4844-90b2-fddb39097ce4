/**
 * 공지사항 관련 타입 정의
 */

export interface NoticeFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: Date;
}

export interface Notice {
  id: string;
  title: string;
  content: string;
  summary?: string;
  author: {
    id: string;
    name: string;
    role: 'admin' | 'manager';
  };
  category: 'general' | 'important' | 'urgent' | 'event';
  status: 'draft' | 'published' | 'archived';
  isImportant: boolean;
  isPinned: boolean;
  viewCount: number;
  files: NoticeFile[];
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

export interface NoticeSearchParams {
  query?: string;
  category?: Notice['category'] | 'all';
  status?: Notice['status'] | 'all';
  isImportant?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  author?: string;
  tags?: string[];
}

export interface NoticePagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface NoticeListResponse {
  notices: Notice[];
  pagination: NoticePagination;
}

export type NoticeFormData = Omit<Notice, 'id' | 'viewCount' | 'createdAt' | 'updatedAt' | 'publishedAt'>;

export interface NoticeStats {
  total: number;
  published: number;
  draft: number;
  important: number;
  thisMonth: number;
}
