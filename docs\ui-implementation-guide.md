# DeepTech Valley Platform - UI 구현 가이드

## 1. 개발 완료된 컴포넌트 및 패턴

### 1.1 헤더 네비게이션 (Header Navigation)

#### 구현된 기능
- **고정 헤더**: `fixed top-0 left-0 right-0 z-50`
- **ETRI 로고**: 실제 이미지 파일 사용 (`/1.-wordmark.png`)
- **메가메뉴**: 호버 시 나타나는 드롭다운 메뉴
- **반응형 디자인**: 모바일/데스크톱 대응

#### 코드 구조
```tsx
// App.tsx에서 Navigation 컴포넌트
const Navigation: React.FC = () => {
  const [activeMenu, setActiveMenu] = React.useState<string | null>(null);
  
  // 메뉴 아이템 정의 (아이콘 포함)
  const menuItems = [
    {
      name: '공지사항',
      href: '/notices',
      submenu: [
        { name: '공지사항', href: '/notices', description: '최신 공지사항을 확인하세요', icon: 'megaphone' },
        // ...
      ]
    }
  ];
}
```

#### 스타일 가이드
- **헤더 높이**: `h-20` (80px)
- **그림자**: `shadow-xs` (매우 연한 그림자)
- **폰트 크기**: `text-lg` (18px)
- **메뉴 간격**: `space-x-8`

### 1.2 메가메뉴 (Mega Menu)

#### 구현된 기능
- **배경 오버레이**: 페이지 전체 반투명 배경
- **3D 효과**: 상단 화살표, 호버 애니메이션
- **아이콘 시스템**: 각 메뉴 항목별 의미있는 아이콘
- **안정적인 호버**: 메뉴 이동 시 닫히지 않음

#### 스타일 특징
```css
/* 메가메뉴 컨테이너 */
.mega-menu {
  max-width: 6xl; /* 1152px */
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 2px solid #d1d5db;
  border-radius: 0.75rem;
}

/* 카드 호버 효과 */
.mega-menu-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}
```

### 1.3 사이드바 네비게이션 (Sidebar Navigation)

#### 구현된 기능
- **선택 상태 강조**: 현재 페이지 진하게 표시
- **아이콘 포함**: 각 메뉴 항목에 SVG 아이콘
- **호버 효과**: 부드러운 색상 전환

#### 선택된 항목 스타일
```tsx
<a className="flex items-center px-4 py-3 text-base font-bold text-etri-blue-700 bg-etri-blue-100 border-l-4 border-etri-blue-600 rounded-md">
  {/* 아이콘 */}
  <svg className="w-5 h-5 mr-3">...</svg>
  공지사항
</a>
```

### 1.4 검색 및 필터 시스템

#### 구현된 기능
- **실시간 검색**: 입력창 + 검색 버튼
- **필터 드롭다운**: 분류별 필터링
- **포커스 효과**: ETRI 파란색 링 효과

#### 검색 입력창 스타일
```tsx
<input 
  type="text" 
  placeholder="제목, 내용 검색"
  className="text-base border border-gray-300 rounded-md px-3 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-etri-blue-500 focus:border-transparent"
/>
```

### 1.5 테이블 시스템

#### 구현된 기능
- **그리드 레이아웃**: 12컬럼 시스템
- **호버 효과**: 행별 배경색 변경
- **상태 배지**: 분류별 색상 구분
- **아이콘 표시**: 첨부파일, 고정 등

#### 테이블 헤더
```tsx
<div className="grid grid-cols-12 gap-4 text-base font-semibold text-gray-700">
  <div className="col-span-1 text-center">순번</div>
  <div className="col-span-1 text-center">분류</div>
  <div className="col-span-4">제목</div>
  {/* ... */}
</div>
```

### 1.6 페이지네이션

#### 구현된 기능
- **간단한 구조**: 이전 | 1 2 | 다음
- **현재 페이지 강조**: ETRI 파란색 배경
- **카드 외부 배치**: 테이블과 분리

#### 페이지네이션 스타일
```tsx
<div className="flex items-center justify-center space-x-1 mt-6">
  <button className="px-3 py-1 text-base text-gray-500 hover:text-gray-700">
    &lt; 이전
  </button>
  <button className="px-3 py-1 text-base bg-etri-blue-600 text-white rounded">
    1
  </button>
  {/* ... */}
</div>
```

## 2. 색상 시스템 (ETRI 브랜드)

### 2.1 주요 색상
- **ETRI Blue**: `etri-blue-600` (#2563eb)
- **ETRI Blue Light**: `etri-blue-100` (#dbeafe)
- **ETRI Blue Dark**: `etri-blue-700` (#1d4ed8)

### 2.2 상태 색상
- **중요**: `bg-blue-100 text-blue-800`
- **긴급**: `bg-red-100 text-red-800`
- **행사**: `bg-green-100 text-green-800`
- **일반**: `bg-gray-100 text-gray-800`

## 3. 타이포그래피 시스템

### 3.1 확대된 폰트 크기
- **페이지 제목**: `text-3xl font-bold` (30px)
- **섹션 제목**: `text-2xl font-bold` (24px)
- **네비게이션**: `text-lg font-medium` (18px)
- **본문**: `text-base` (16px)
- **작은 텍스트**: `text-sm` (14px)

### 3.2 제목 배치
- **섹션 제목**: 카드 외부, 왼쪽 정렬
- **사이드바 제목**: 카드 외부, 더 큰 폰트

## 4. 간격 시스템

### 4.1 확장된 간격
- **페이지 섹션**: `mb-12` (48px)
- **섹션 간격**: `mb-10` (40px)
- **요소 간격**: `mb-6` (24px)
- **작은 간격**: `mb-4` (16px)

### 4.2 패딩 시스템
- **카드 내부**: `p-5` (20px)
- **테이블 셀**: `px-6 py-5`
- **버튼**: `px-4 py-2`

## 5. 그림자 시스템

### 5.1 부드러운 그림자
- **기본 카드**: `shadow-sm`
- **호버 효과**: `shadow-md`
- **메가메뉴**: `shadow-2xl` + 커스텀
- **강조**: `shadow-lg`

## 6. 애니메이션 시스템

### 6.1 전환 효과
- **기본 전환**: `transition-colors duration-200`
- **메가메뉴**: `transition-all duration-300`
- **호버 변형**: `transform hover:-translate-y-1`

## 7. 아이콘 시스템

### 7.1 사용된 아이콘
- **공지사항**: 확성기 아이콘
- **Q&A**: 물음표 아이콘
- **첨부파일**: 클립 아이콘
- **고정**: 핀 아이콘 (SVG)

### 7.2 아이콘 크기
- **사이드바**: `w-5 h-5`
- **메가메뉴**: `w-5 h-5` (12x12 박스 내)
- **테이블**: `w-4 h-4`

## 8. 반응형 디자인

### 8.1 브레이크포인트
- **모바일**: 기본 (< 768px)
- **태블릿**: `md:` (≥ 768px)
- **데스크톱**: `lg:` (≥ 1024px)

### 8.2 반응형 패턴
- **메가메뉴**: `hidden md:flex` (모바일 숨김)
- **그리드**: `grid-cols-1 md:grid-cols-3`
- **간격**: `space-x-4 md:space-x-8`

## 9. 다른 페이지 적용 가이드

### 9.1 새 페이지 생성 시 기본 구조

```tsx
// 새 페이지 컴포넌트 기본 템플릿
import React from 'react';

const NewPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 페이지 컨테이너 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">

          {/* 사이드바 (필요한 경우) */}
          <div className="w-64 flex-shrink-0">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">섹션명</h2>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              {/* 사이드바 내용 */}
            </div>
          </div>

          {/* 메인 컨텐츠 */}
          <div className="flex-1">
            {/* 페이지 헤더 */}
            <div className="mb-10">
              <nav className="flex items-center text-base text-gray-500 mb-4" aria-label="Breadcrumb">
                <a href="/" className="hover:text-gray-700">홈</a>
                <svg className="w-5 h-5 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900 font-medium">현재 페이지</span>
              </nav>
              <h1 className="text-3xl font-bold text-gray-900">페이지 제목</h1>
            </div>

            {/* 컨텐츠 섹션들 */}
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-left">섹션 제목</h2>
              {/* 섹션 내용 */}
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default NewPage;
```

### 9.2 공통 컴포넌트 재사용

#### 검색 및 필터 컴포넌트
```tsx
const SearchAndFilter: React.FC = () => (
  <div className="flex items-center justify-between mb-4">
    <div className="flex items-center space-x-2">
      <input
        type="text"
        placeholder="검색어 입력"
        className="text-base border border-gray-300 rounded-md px-3 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-etri-blue-500 focus:border-transparent"
      />
      <button className="px-4 py-2 bg-etri-blue-600 text-white text-base rounded-md hover:bg-etri-blue-700">
        검색
      </button>
    </div>
    <div className="flex items-center space-x-4">
      <select className="text-base border border-gray-300 rounded-md px-3 py-2">
        <option>전체 분류</option>
      </select>
      <button className="flex items-center text-base text-gray-600 border border-gray-300 rounded-md px-3 py-2 hover:bg-gray-50">
        필터 적용
      </button>
    </div>
  </div>
);
```

#### 페이지네이션 컴포넌트
```tsx
const Pagination: React.FC = () => (
  <div className="flex items-center justify-center space-x-1 mt-6">
    <button className="px-3 py-1 text-base text-gray-500 hover:text-gray-700">
      &lt; 이전
    </button>
    <button className="px-3 py-1 text-base bg-etri-blue-600 text-white rounded">
      1
    </button>
    <button className="px-3 py-1 text-base text-gray-700 hover:text-gray-900">
      2
    </button>
    <button className="px-3 py-1 text-base text-gray-500 hover:text-gray-700">
      다음 &gt;
    </button>
  </div>
);
```

### 9.3 사이드바 네비게이션 적용

```tsx
// 사이드바 메뉴 아이템 구조
const sidebarItems = [
  {
    name: '메뉴명',
    href: '/path',
    icon: 'iconName',
    active: true // 현재 페이지 여부
  }
];

// 사이드바 렌더링
{sidebarItems.map((item) => (
  <a
    key={item.name}
    href={item.href}
    className={`flex items-center px-4 py-3 text-base rounded-md transition-colors duration-200 ${
      item.active
        ? 'font-bold text-etri-blue-700 bg-etri-blue-100 border-l-4 border-etri-blue-600'
        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
    }`}
  >
    <svg className="w-5 h-5 mr-3">...</svg>
    {item.name}
  </a>
))}
```

### 9.4 테이블 시스템 적용

```tsx
// 테이블 컨테이너
<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
  {/* 테이블 헤더 */}
  <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
    <div className="grid grid-cols-12 gap-4 text-base font-semibold text-gray-700">
      <div className="col-span-2">컬럼1</div>
      <div className="col-span-4">컬럼2</div>
      <div className="col-span-3">컬럼3</div>
      <div className="col-span-3">컬럼4</div>
    </div>
  </div>

  {/* 테이블 내용 */}
  <div className="divide-y divide-gray-200">
    {data.map((item) => (
      <div key={item.id} className="px-6 py-5 hover:bg-gray-50 cursor-pointer">
        <div className="grid grid-cols-12 gap-4 text-base">
          <div className="col-span-2">{item.field1}</div>
          <div className="col-span-4">{item.field2}</div>
          <div className="col-span-3">{item.field3}</div>
          <div className="col-span-3">{item.field4}</div>
        </div>
      </div>
    ))}
  </div>
</div>
```

### 9.5 버튼 시스템

```tsx
// 주요 버튼 (Primary)
<button className="inline-flex items-center px-4 py-2 bg-etri-blue-600 text-white text-sm font-medium rounded-md hover:bg-etri-blue-700">
  <PlusIcon className="w-4 h-4 mr-2" />
  버튼 텍스트
</button>

// 보조 버튼 (Secondary)
<button className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">
  버튼 텍스트
</button>

// 위험 버튼 (Danger)
<button className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">
  삭제
</button>
```

### 9.6 카드 시스템

```tsx
// 기본 카드
<div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
  <h3 className="text-lg font-semibold text-gray-900 mb-3">카드 제목</h3>
  <p className="text-base text-gray-600">카드 내용</p>
</div>

// 호버 효과가 있는 카드
<div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer p-6">
  {/* 카드 내용 */}
</div>
```

## 10. 체크리스트

### 10.1 새 페이지 개발 시 확인사항
- [ ] ETRI 색상 시스템 적용 (`etri-blue-*`)
- [ ] 확대된 폰트 크기 사용 (`text-base` 이상)
- [ ] 섹션 제목 왼쪽 정렬 (`text-left`)
- [ ] 적절한 간격 시스템 (`mb-6`, `mb-8`, `mb-10`)
- [ ] 부드러운 그림자 적용 (`shadow-sm`)
- [ ] 호버 효과 및 전환 애니메이션
- [ ] 반응형 디자인 고려
- [ ] 아이콘 일관성 유지
- [ ] 접근성 고려 (aria-label 등)

### 10.2 품질 확인사항
- [ ] 모든 텍스트가 읽기 쉬운 크기인가?
- [ ] 색상 대비가 충분한가?
- [ ] 호버 상태가 명확한가?
- [ ] 로딩 상태가 고려되었는가?
- [ ] 에러 상태가 처리되었는가?
