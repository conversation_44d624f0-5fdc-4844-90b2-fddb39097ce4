import React from 'react';
import { Building2, TrendingUp, Microscope, Target } from 'lucide-react';
import StatCard from '@/components/common/StatCard';

interface StatisticItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  value: number;
  suffix: string;
  label: string;
  description?: string;
}

interface StatisticsSectionProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

/**
 * 메인페이지 통계 섹션 컴포넌트
 * 주요 통계 정보를 카드 형태로 표시
 */
const StatisticsSection: React.FC<StatisticsSectionProps> = ({
  title = "주요 통계",
  subtitle,
  className = ""
}) => {
  // 통계 데이터 정의
  const statisticsData: StatisticItem[] = [
    {
      id: 'companies',
      icon: Building2,
      value: 1200,
      suffix: '+',
      label: '등록된 기업',
      description: '딥테크 밸리에 참여하는 기업 수'
    },
    {
      id: 'institutions',
      icon: TrendingUp,
      value: 500,
      suffix: '+',
      label: '참여 기관',
      description: '연구기관 및 대학 등'
    },
    {
      id: 'projects',
      icon: Microscope,
      value: 3450,
      suffix: '+',
      label: '연구 프로젝트',
      description: '진행 중인 연구개발 프로젝트'
    },
    {
      id: 'patents',
      icon: Target,
      value: 800,
      suffix: '+',
      label: '기술 특허',
      description: '등록된 특허 및 지식재산권'
    }
  ];

  return (
    <section className={`py-16 px-4 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto">
        {/* 섹션 헤더 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          {subtitle && (
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>

        {/* 통계 카드 그리드 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {statisticsData.map((stat) => (
            <StatCard
              key={stat.id}
              icon={stat.icon}
              value={stat.value}
              suffix={stat.suffix}
              label={stat.label}
              description={stat.description}
            />
          ))}
        </div>

        {/* 추가 정보 (선택사항) */}
        <div className="text-center mt-12">
          <p className="text-gray-500 text-sm">
            * 통계는 2024년 12월 기준이며, 실시간으로 업데이트됩니다.
          </p>
        </div>
      </div>
    </section>
  );
};

export default StatisticsSection;
