# Tailwind CSS 설정 가이드 - ETRI 색상 시스템

## 1. Tailwind CSS 설정 파일 업데이트

### 1.1 tailwind.config.js 수정

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // ETRI 브랜드 색상 시스템
        'etri-blue': {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',  // 메인 ETRI 블루
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        'etri-orange': {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#f97316',  // 메인 ETRI 오렌지
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        }
      },
      fontFamily: {
        // 한국어 최적화 폰트
        'sans': [
          'Pretendard',
          '-apple-system',
          'BlinkMacSystemFont',
          'system-ui',
          'Roboto',
          'Helvetica Neue',
          'Segoe UI',
          'Apple SD Gothic Neo',
          'Noto Sans KR',
          'Malgun Gothic',
          'Apple Color Emoji',
          'Segoe UI Emoji',
          'Segoe UI Symbol',
          'sans-serif'
        ]
      },
      spacing: {
        // 추가 간격 시스템
        '18': '4.5rem',   // 72px
        '88': '22rem',    // 352px
        '128': '32rem',   // 512px
      },
      boxShadow: {
        // 커스텀 그림자
        'xs': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'mega': '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
      },
      animation: {
        // 커스텀 애니메이션
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        }
      }
    },
  },
  plugins: [
    // 추가 플러그인들
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

### 1.2 CSS 변수 설정 (선택사항)

```css
/* src/index.css 또는 globals.css */
:root {
  /* ETRI 브랜드 색상 */
  --etri-blue-50: #eff6ff;
  --etri-blue-100: #dbeafe;
  --etri-blue-600: #2563eb;
  --etri-blue-700: #1d4ed8;
  --etri-blue-800: #1e40af;
  
  /* 그림자 변수 */
  --shadow-mega: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
}
```

## 2. 색상 사용 가이드

### 2.1 ETRI Blue 사용법

```tsx
// 배경색
<div className="bg-etri-blue-600">메인 배경</div>
<div className="bg-etri-blue-100">연한 배경</div>

// 텍스트 색상
<span className="text-etri-blue-600">메인 텍스트</span>
<span className="text-etri-blue-700">진한 텍스트</span>

// 테두리
<div className="border-etri-blue-600">테두리</div>

// 호버 효과
<button className="bg-etri-blue-600 hover:bg-etri-blue-700">
  버튼
</button>
```

### 2.2 상태별 색상 매핑

```tsx
// 중요 (파란색)
<span className="bg-blue-100 text-blue-800">중요</span>

// 긴급 (빨간색)  
<span className="bg-red-100 text-red-800">긴급</span>

// 행사 (초록색)
<span className="bg-green-100 text-green-800">행사</span>

// 일반 (회색)
<span className="bg-gray-100 text-gray-800">일반</span>
```

## 3. 컴포넌트별 색상 적용

### 3.1 네비게이션
```tsx
// 활성 메뉴
<a className="text-etri-blue-600 border-b-2 border-etri-blue-600">
  공지사항
</a>

// 일반 메뉴
<a className="text-gray-700 hover:text-etri-blue-600">
  메뉴
</a>
```

### 3.2 사이드바
```tsx
// 선택된 항목
<a className="text-etri-blue-700 bg-etri-blue-100 border-l-4 border-etri-blue-600">
  선택된 메뉴
</a>

// 일반 항목
<a className="text-gray-600 hover:text-gray-900 hover:bg-gray-50">
  일반 메뉴
</a>
```

### 3.3 버튼
```tsx
// 주요 버튼
<button className="bg-etri-blue-600 text-white hover:bg-etri-blue-700">
  주요 액션
</button>

// 보조 버튼
<button className="border border-gray-300 text-gray-700 hover:bg-gray-50">
  보조 액션
</button>
```

### 3.4 폼 요소
```tsx
// 입력창 포커스
<input className="focus:ring-2 focus:ring-etri-blue-500 focus:border-transparent" />

// 선택 상태
<select className="border-gray-300 focus:border-etri-blue-500 focus:ring-etri-blue-500" />
```

## 4. 다크 모드 지원 (향후 확장)

```javascript
// tailwind.config.js에 다크 모드 설정
module.exports = {
  darkMode: 'class', // 또는 'media'
  theme: {
    extend: {
      colors: {
        'etri-blue': {
          // 다크 모드용 색상 추가
          950: '#0f1629',
        }
      }
    }
  }
}
```

```tsx
// 다크 모드 사용 예시
<div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
  <button className="bg-etri-blue-600 dark:bg-etri-blue-500">
    버튼
  </button>
</div>
```

## 5. 성능 최적화

### 5.1 사용하지 않는 클래스 제거
```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    // 정확한 경로 지정으로 번들 크기 최적화
  ],
  // ...
}
```

### 5.2 JIT 모드 활용
```javascript
// tailwind.config.js
module.exports = {
  mode: 'jit', // Just-In-Time 컴파일
  // ...
}
```

## 6. 개발 도구

### 6.1 VS Code 확장
- Tailwind CSS IntelliSense
- Headwind (클래스 정렬)
- Tailwind Docs

### 6.2 브라우저 확장
- Tailwind CSS Devtools

## 7. 마이그레이션 가이드

### 7.1 기존 색상을 ETRI 색상으로 변경
```bash
# 일괄 변경 스크립트 (예시)
# blue-600 → etri-blue-600
find ./src -name "*.tsx" -exec sed -i 's/bg-blue-600/bg-etri-blue-600/g' {} +
find ./src -name "*.tsx" -exec sed -i 's/text-blue-600/text-etri-blue-600/g' {} +
find ./src -name "*.tsx" -exec sed -i 's/border-blue-600/border-etri-blue-600/g' {} +
```

### 7.2 점진적 적용
1. 새로운 컴포넌트부터 ETRI 색상 적용
2. 기존 컴포넌트는 수정 시점에 업데이트
3. 전체 일관성 검토 후 일괄 적용

## 8. 품질 관리

### 8.1 색상 일관성 체크리스트
- [ ] 모든 파란색이 ETRI 파란색으로 통일되었는가?
- [ ] 호버 상태 색상이 일관되게 적용되었는가?
- [ ] 접근성 기준(WCAG)을 만족하는가?
- [ ] 다양한 화면에서 테스트되었는가?

### 8.2 자동화 도구
```json
// package.json scripts
{
  "scripts": {
    "lint:css": "stylelint '**/*.css'",
    "check:colors": "node scripts/check-color-consistency.js"
  }
}
```
