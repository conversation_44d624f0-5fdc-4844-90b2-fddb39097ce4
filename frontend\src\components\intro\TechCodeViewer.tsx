import React from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Download } from 'lucide-react';

interface TechCodeViewerProps {
  code: string;
  language?: string;
}

// 언어 코드 → 확장자/이름 매핑
const languageMap: Record<string, { ext: string; label: string; color: string }> = {
  python: { ext: 'py', label: 'Python', color: 'bg-yellow-100 text-yellow-800 border-yellow-300' },
  solidity: { ext: 'sol', label: 'Solidity', color: 'bg-indigo-100 text-indigo-800 border-indigo-300' },
  javascript: { ext: 'js', label: 'JavaScript', color: 'bg-amber-100 text-amber-800 border-amber-300' },
  typescript: { ext: 'ts', label: 'TypeScript', color: 'bg-blue-100 text-blue-800 border-blue-300' },
  java: { ext: 'java', label: 'Java', color: 'bg-orange-100 text-orange-800 border-orange-300' },
  c: { ext: 'c', label: 'C', color: 'bg-gray-100 text-gray-800 border-gray-300' },
  cpp: { ext: 'cpp', label: 'C++', color: 'bg-gray-100 text-gray-800 border-gray-300' },
  go: { ext: 'go', label: 'Go', color: 'bg-cyan-100 text-cyan-800 border-cyan-300' },
  html: { ext: 'html', label: 'HTML', color: 'bg-pink-100 text-pink-800 border-pink-300' },
  css: { ext: 'css', label: 'CSS', color: 'bg-blue-50 text-blue-700 border-blue-200' },
  sql: { ext: 'sql', label: 'SQL', color: 'bg-green-100 text-green-800 border-green-300' },
  shell: { ext: 'sh', label: 'Shell', color: 'bg-gray-200 text-gray-800 border-gray-300' },
  // 기본값
  default: { ext: 'txt', label: '코드', color: 'bg-gray-100 text-gray-800 border-gray-300' },
};

export const TechCodeViewer: React.FC<TechCodeViewerProps> = ({ code, language = 'python' }) => {
  const langInfo = languageMap[language] || languageMap['default'];
  const fileName = `sample-code.${langInfo.ext}`;

  // 코드 복사 기능
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      alert('코드가 복사되었습니다!');
    } catch {
      alert('복사에 실패했습니다.');
    }
  };

  // 코드 다운로드 기능
  const handleDownload = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="rounded-lg overflow-hidden border border-gray-200 relative">
      {/* 상단 바: 파일명, 언어 뱃지, 버튼 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <span className="font-mono text-sm text-gray-700">{fileName}</span>
          <span className={`ml-2 px-2 py-0.5 rounded border text-xs font-semibold ${langInfo.color}`}>{langInfo.label}</span>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleCopy}
            className="flex items-center gap-1 px-2 py-1 text-xs rounded hover:bg-gray-200 active:bg-gray-300 transition"
            title="코드 복사"
          >
            <Copy className="w-4 h-4" /> 복사
          </button>
          <button
            onClick={handleDownload}
            className="flex items-center gap-1 px-2 py-1 text-xs rounded hover:bg-gray-200 active:bg-gray-300 transition"
            title="코드 다운로드"
          >
            <Download className="w-4 h-4" /> 다운
          </button>
        </div>
      </div>
      {/* 코드 하이라이터: 밝은 테마(oneLight) 사용 */}
      <SyntaxHighlighter
        language={language}
        style={oneLight}
        showLineNumbers
        customStyle={{
          margin: 0,
          padding: '1.5rem',
          fontSize: '1rem',
          fontFamily: 'Fira Mono, Menlo, Consolas, monospace',
          lineHeight: 1.7,
          minHeight: '120px',
        }}
        lineNumberStyle={{ color: '#bbb', marginRight: '16px' }}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  );
}; 