# Task Breakdown Template

## 🎯 목적
Shrimp MCP의 `split_tasks` 결과를 GitHub 이슈 생성 및 AI 개발에 적합한 구조화된 작업 명세서로 변환하는 템플릿입니다.

## 📋 문서 구조

### 헤더 정보
```markdown
# [프로젝트명] 작업 분해 명세서

**생성일**: YYYY-MM-DD
**Shrimp 분석 기반**: split_tasks 실행 결과
**총 작업 수**: X개
**예상 총 기간**: X주
**프로젝트 복잡도**: Low/Medium/High
```

### 전체 작업 요약
```markdown
## 📊 작업 분해 개요

### 작업 통계
- **Feature 개발**: X개 작업
- **API 개발**: X개 작업  
- **문서화**: X개 작업
- **인프라/설정**: X개 작업

### 진행 계획
- **Phase 1 (MVP)**: X개 작업 (X주)
- **Phase 2 (고도화)**: X개 작업 (X주)
- **Phase 3 (배포준비)**: X개 작업 (X주)

### 병렬 처리 가능
- **동시 진행 가능**: X개 작업 그룹
- **크리티컬 패스**: [핵심 순서 작업들]
```

## 🔧 개별 작업 명세 형식

### 작업 기본 정보
```markdown
## 작업 #X: [작업명]

### 기본 정보
- **작업 유형**: Feature/API/Bug/Documentation/Infrastructure
- **우선순위**: High/Medium/Low
- **예상 소요시간**: 0.5일/1일/1.5일/2일
- **담당 유형**: AI/Human/Review Required
- **Phase**: 1(MVP)/2(고도화)/3(배포준비)
```

### 작업 상세 설명
```markdown
### 📝 작업 설명
**배경**: [왜 이 작업이 필요한지]
**목표**: [이 작업으로 달성하고자 하는 것]
**범위**: 
- 포함사항: [구현해야 할 것들]
- 제외사항: [이번 작업에서 하지 않을 것들]

### 🔧 기술적 요구사항
- **주요 기능**: [핵심 기능 3-5개]
- **기술 스택**: [사용할 기술/라이브러리]
- **성능 요구사항**: [응답시간, 처리량 등]
- **보안 고려사항**: [인증, 데이터 보호 등]
```

### 구현 가이드
```markdown
### 💡 구현 가이드
**주요 구현 단계**:
1. [첫 번째 단계]
2. [두 번째 단계]  
3. [세 번째 단계]

**핵심 고려사항**:
- [중요한 구현 포인트 1]
- [중요한 구현 포인트 2]
- [중요한 구현 포인트 3]

**참고 사항**:
- [추가 고려할 점들]
- [다른 작업과의 연관성]
```

### 파일 및 의존성
```markdown
### 📁 관련 파일
| 파일 경로 | 작업 유형 | 설명 | 예상 라인 수 |
|-----------|-----------|------|-------------|
| src/components/UserForm.jsx | 생성 | 사용자 입력 폼 컴포넌트 | 100-150 |
| src/api/userService.js | 수정 | 사용자 API 서비스 추가 | 50-80 |
| docs/api/users.md | 업데이트 | API 문서 업데이트 | 20-30 |

### 🔗 의존성
**선행 작업** (완료되어야 시작 가능):
- 작업 #X: [작업명] - [의존 이유]
- 작업 #Y: [작업명] - [의존 이유]

**후속 작업** (이 작업 완료 후 가능):
- 작업 #Z: [작업명] - [연결 이유]

**병렬 진행 가능**:
- 작업 #A, #B, #C와 동시 진행 가능
```

### 완료 기준
```markdown
### ✅ 완료 기준

#### 기능적 완료 기준
- [ ] 모든 요구사항이 구현됨
- [ ] 정상 케이스 동작 확인
- [ ] 에러 케이스 처리 확인
- [ ] 에지 케이스 처리 확인

#### 기술적 완료 기준  
- [ ] 코딩 표준 준수 (ESLint/Prettier 통과)
- [ ] 단위 테스트 작성 및 통과
- [ ] 코드 리뷰 완료
- [ ] 성능 기준 충족

#### 문서화 완료 기준
- [ ] 코드 주석 작성
- [ ] API 문서 업데이트 (해당시)
- [ ] README 업데이트 (해당시)
- [ ] 변경사항 기록

#### 통합 완료 기준
- [ ] 기존 코드와 충돌 없음
- [ ] 다른 컴포넌트 영향도 확인
- [ ] 브랜치 머지 가능
- [ ] 배포 준비 완료
```

## 🔄 의존성 관리

### 의존성 그래프
```markdown
## 📈 전체 의존성 관계도

### 크리티컬 패스 (순서 중요)
```
작업1 → 작업3 → 작업7 → 작업12 → 작업15
```

### 병렬 처리 그룹
**그룹 A** (동시 진행 가능):
- 작업2, 작업4, 작업5

**그룹 B** (그룹 A 완료 후):
- 작업6, 작업8, 작업9

**그룹 C** (최종 통합):
- 작업13, 작업14
```

### 블로킹 위험도
```markdown
### ⚠️ 블로킹 위험 분석
**높은 위험** (지연시 전체 영향):
- 작업 #X: [작업명] - [위험 이유]

**중간 위험** (일부 작업 영향):
- 작업 #Y: [작업명] - [위험 이유]

**낮은 위험** (독립적 진행):
- 작업 #Z: [작업명] - [독립 가능 이유]
```

## 📋 우선순위 매트릭스

### 우선순위 결정 기준
```markdown
## 🎯 작업 우선순위

### High Priority (즉시 시작)
1. **작업 #X**: [작업명] - [선택 이유]
2. **작업 #Y**: [작업명] - [선택 이유]

### Medium Priority (High 완료 후)
1. **작업 #A**: [작업명] - [선택 이유]
2. **작업 #B**: [작업명] - [선택 이유]

### Low Priority (여유있을 때)
1. **작업 #P**: [작업명] - [선택 이유]
2. **작업 #Q**: [작업명] - [선택 이유]

### 우선순위 변경 조건
- **긴급도 증가**: [어떤 상황에서 우선순위 상향]
- **의존성 변경**: [다른 작업 지연시 순서 조정]
- **리소스 가용성**: [개발자 상황에 따른 조정]
```

## 🎪 GitHub 이슈 생성 준비

### 이슈 생성 정보
```markdown
## 📋 GitHub 이슈 변환 정보

각 작업별 GitHub 이슈 생성시 사용할 정보:

### 라벨 매핑
- Feature 개발 → `type-feature`, `priority-high/medium/low`
- API 개발 → `type-api`, `backend`
- 문서화 → `type-docs`, `documentation`
- 인프라 → `type-infrastructure`, `devops`

### 마일스톤
- Phase 1 작업 → `v1.0-MVP`
- Phase 2 작업 → `v1.1-Enhancement`  
- Phase 3 작업 → `v1.2-Production`

### 할당 전략
- AI 개발 가능 → `ai-assignable`
- 인간 리뷰 필요 → `human-review`
- 복잡한 의사결정 → `human-required`
```

## 💡 템플릿 사용 가이드

### 작성시 주의사항
1. **작업 크기**: 각 작업은 1-2일 내 완료 가능해야 함
2. **의존성 명확화**: 블로킹 관계를 정확히 표시
3. **완료 기준 구체화**: 언제 완료인지 명확한 기준 제시
4. **AI 친화적 작성**: AI가 이해하기 쉬운 구체적 설명

### 품질 체크리스트
- [ ] 모든 작업이 1-2일 크기인가?
- [ ] 의존성이 순환하지 않는가?
- [ ] 완료 기준이 구체적인가?
- [ ] GitHub 이슈로 변환 가능한가?

이 템플릿을 사용하여 Shrimp MCP의 `split_tasks` 결과를 체계적으로 문서화하고, GitHub 이슈 생성 및 AI 개발에 활용할 수 있습니다.