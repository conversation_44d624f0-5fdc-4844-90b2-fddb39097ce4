# DeepTech Valley Platform 기술 명세서

## 1. 기술 스택

### 1.1 Frontend Core
- **React 18+**: 함수형 컴포넌트, Concurrent Features
- **TypeScript**: 타입 안정성 및 개발 효율성
- **Vite**: 빠른 개발 환경 및 최적화된 빌드

### 1.2 Backend Core
- **Python 3.11+**: 메인 백엔드 언어
- **FastAPI**: 고성능 웹 프레임워크, 자동 API 문서 생성
- **SQLAlchemy**: ORM (Object-Relational Mapping)
- **Alembic**: 데이터베이스 마이그레이션
- **Pydantic**: 데이터 검증 및 직렬화

### 1.3 UI/Styling
- **ShadcnUI**: 고품질 컴포넌트, Radix UI 기반, 접근성 지원
- **TailwindCSS**: 유틸리티 우선 스타일링
- **Framer Motion**: 애니메이션 및 상호작용

### 1.4 상태 관리
- **Context API**: 전역 상태 (인증, 테마)
- **React Query/TanStack Query**: 서버 상태 관리
- **React Hook Form**: 폼 상태 관리

### 1.5 데이터 시각화
- **React Flow**: 에코시스템 맵, 노드-링크 다이어그램
- **Chart.js**: 기본 통계 차트
- **D3.js**: 복잡한 커스텀 시각화 (필요시)

### 1.6 라우팅 & 네비게이션
- **React Router v6+**: 클라이언트 사이드 라우팅

### 1.7 백엔드 추가 라이브러리
- **pandas**: 데이터 분석 및 통계 처리
- **ReportLab**: PDF 문서 자동 생성
- **python-multipart**: 파일 업로드 처리
- **python-jose**: JWT 토큰 처리
- **Celery**: 백그라운드 작업 처리
- **Redis**: 캐싱 및 세션 관리

## 2. 프로젝트 아키텍처

### 2.1 프론트엔드 디렉토리 구조
```
frontend/src/
├── components/
│   ├── ui/                 # ShadcnUI 컴포넌트
│   ├── common/            # 공통 재사용 컴포넌트
│   ├── layout/            # 레이아웃 컴포넌트
│   ├── forms/             # 폼 관련 컴포넌트
│   └── charts/            # 시각화 컴포넌트
├── pages/
│   ├── auth/              # 인증 관련 페이지
│   ├── dashboard/         # 대시보드
│   ├── admin/             # 관리자 페이지
│   ├── business/          # 사업소개
│   ├── notice/            # 공지관리
│   ├── demand/            # 수요관리
│   └── valley/            # 밸리관리
├── hooks/                 # 커스텀 훅
├── services/              # API 서비스 레이어
├── contexts/              # React Context
├── utils/                 # 유틸리티 함수
├── constants/             # 상수 정의
├── types/                 # TypeScript 타입 정의
└── assets/                # 정적 자원
```

### 2.2 백엔드 디렉토리 구조
```
backend/
├── app/
│   ├── api/               # API 라우터
│   │   ├── auth/          # 인증 관련 API
│   │   ├── users/         # 사용자 관리 API
│   │   ├── business/      # 사업소개 API
│   │   ├── notices/       # 공지관리 API
│   │   ├── demands/       # 수요관리 API
│   │   ├── valley/        # 밸리관리 API
│   │   └── statistics/    # 통계 API
│   ├── core/              # 핵심 설정
│   │   ├── config.py      # 환경 설정
│   │   ├── security.py    # 보안 관련
│   │   └── database.py    # 데이터베이스 설정
│   ├── models/            # SQLAlchemy 모델
│   ├── schemas/           # Pydantic 스키마
│   ├── services/          # 비즈니스 로직
│   ├── utils/             # 유틸리티 함수
│   └── main.py            # FastAPI 앱 진입점
├── alembic/               # 데이터베이스 마이그레이션
├── tests/                 # 테스트 코드
└── requirements.txt       # Python 의존성
```

### 2.2 아키텍처 패턴
- **컴포넌트 기반 모듈화**: 재사용 가능한 컴포넌트 설계
- **관심사 분리**: UI, 비즈니스 로직, 데이터 레이어 분리
- **서비스 레이어 패턴**: API 호출 로직 추상화

## 3. 핵심 기능별 기술 설계

### 3.1 권한 기반 인증 시스템

#### 사용자 타입 정의
```typescript
enum UserType {
  INDIVIDUAL = 'individual',
  COMPANY = 'company',
  INSTITUTION = 'institution', 
  ADMIN = 'admin'
}

interface Permission {
  resource: string;
  action: 'read' | 'write' | 'delete' | 'admin';
  scope?: 'own' | 'all';
}
```

#### 권한 관리 Context
```typescript
const AuthContext = createContext({
  user: null,
  permissions: [],
  hasPermission: (permission: string) => boolean,
  login: (credentials) => Promise<void>,
  logout: () => void
});
```

#### 권한 기반 라우트 보호
```typescript
const ProtectedRoute = ({ children, requiredPermission }) => {
  const { hasPermission } = useAuth();
  return hasPermission(requiredPermission) ? children : <Unauthorized />;
};
```

### 3.2 수요관리 폼 시스템

#### 사업신청 폼 데이터 구조
```typescript
interface ApplicationFormData {
  companyInfo: {
    name: string;
    size: 'startup' | 'small' | 'medium' | 'large';
    businessType: string;
    registrationNumber: string;
  };
  projectInfo: {
    title: string;
    description: string;
    budget: number;
    duration: number;
  };
  attachments: {
    businessPlan?: File;
    financialStatement?: File;
    technicalDocument?: File;
    others: File[];
  };
}
```

#### 파일 업로드 시스템
```typescript
const useFileUpload = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  
  const uploadFiles = async (files: File[]) => {
    setUploading(true);
    for (const file of files) {
      await uploadFileWithProgress(file, setProgress);
    }
    setUploading(false);
  };
  
  return { files, uploading, progress, uploadFiles };
};
```

#### 문서 자동 생성 서비스
```typescript
const documentService = {
  generateApplicationPDF: (formData: ApplicationFormData) => Promise<Blob>,
  downloadDocument: (documentId: string) => Promise<void>,
  getDocumentTemplate: (type: string) => Promise<DocumentTemplate>
};
```

### 3.3 React Flow 에코시스템 맵

#### React Flow 기본 설정
```typescript
import ReactFlow, {
  Node,
  Edge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap
} from 'reactflow';
import 'reactflow/dist/style.css';
```

#### 커스텀 노드 타입 정의
```typescript
interface CompanyNodeData {
  label: string;
  type: 'company' | 'institution';
  size: 'startup' | 'small' | 'medium' | 'large';
  sector: string;
}

const CompanyNode: React.FC<NodeProps<CompanyNodeData>> = ({ data }) => {
  return (
    <div className={cn(
      "px-4 py-2 rounded-lg border-2 bg-white shadow-md",
      data.type === 'company' ? 'border-etri-blue' : 'border-etri-orange'
    )}>
      <div className="font-semibold text-sm">{data.label}</div>
      <div className="text-xs text-gray-500">{data.sector}</div>
    </div>
  );
};
```

#### 에코시스템 맵 컴포넌트
```typescript
const EcosystemMap: React.FC<EcosystemMapProps> = ({
  initialNodes = [],
  initialEdges = []
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const nodeTypes = {
    company: CompanyNode,
    institution: CompanyNode,
  };

  return (
    <div className="w-full h-[600px] border rounded-lg">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  );
};
```

### 3.4 통계 대시보드

#### 통계 데이터 관리
```typescript
const useStatistics = () => {
  return useQuery({
    queryKey: ['statistics'],
    queryFn: () => statisticsService.getOverview(),
    refetchInterval: 30000,
    staleTime: 10000
  });
};
```

#### 애니메이션 카운터
```typescript
const AnimatedCounter = ({ value, duration = 2000, suffix = '' }) => {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    // 애니메이션 로직
  }, [value, duration]);
  
  return (
    <span className="text-2xl font-bold">
      {count.toLocaleString()}{suffix}
    </span>
  );
};
```

## 4. 성능 최적화

### 4.1 코드 분할
```typescript
const Dashboard = lazy(() => import('./pages/Dashboard'));
const AdminPanel = lazy(() => import('./pages/AdminPanel'));

const ExpensiveChart = memo(({ data }) => {
  const processedData = useMemo(() => 
    processChartData(data), [data]
  );
  return <Chart data={processedData} />;
});
```

### 4.2 이미지 최적화
```typescript
const LazyImage = ({ src, alt, ...props }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsInView(true);
        observer.disconnect();
      }
    });
    
    if (imgRef.current) observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, []);
  
  return (
    <img 
      ref={imgRef}
      src={isInView ? src : '/placeholder.jpg'}
      alt={alt}
      onLoad={() => setIsLoaded(true)}
      {...props}
    />
  );
};
```

## 5. 보안 및 접근성

### 5.1 보안 구현
```typescript
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 10000
});

apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

const sanitizeInput = (input: string) => {
  return DOMPurify.sanitize(input);
};
```

### 5.2 접근성 구현
```typescript
const AccessibleButton = ({ onClick, children, ...props }) => {
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  };
  
  return (
    <button
      onClick={onClick}
      onKeyDown={handleKeyDown}
      aria-label={props['aria-label']}
      className="focus:outline-none focus:ring-2 focus:ring-blue-500"
      {...props}
    >
      {children}
    </button>
  );
};
```

## 6. 개발 환경 설정

### 6.1 Vite 설정
```typescript
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/pages': path.resolve(__dirname, './src/pages')
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog'],
          charts: ['reactflow', 'chart.js']
        }
      }
    }
  }
});
```

## 7. 백엔드 아키텍처 설계

### 7.1 FastAPI 애플리케이션 구조
```python
# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, users, business, notices, demands, valley, statistics

app = FastAPI(title="딥테크 오픈플랫폼 API", version="1.0.0")

# CORS 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 프론트엔드 URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API 라우터 등록
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
app.include_router(business.router, prefix="/api/v1/business", tags=["business"])
app.include_router(notices.router, prefix="/api/v1/notices", tags=["notices"])
app.include_router(demands.router, prefix="/api/v1/demands", tags=["demands"])
app.include_router(valley.router, prefix="/api/v1/valley", tags=["valley"])
app.include_router(statistics.router, prefix="/api/v1/statistics", tags=["statistics"])
```

### 7.2 데이터베이스 모델 예시
```python
# models/user.py
from sqlalchemy import Column, Integer, String, Enum, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from enum import Enum as PyEnum

Base = declarative_base()

class UserType(PyEnum):
    INDIVIDUAL = "individual"
    COMPANY = "company"
    INSTITUTION = "institution"
    ADMIN = "admin"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    user_type = Column(Enum(UserType), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 7.3 PDF 문서 생성 서비스
```python
# services/document_service.py
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
import io

class DocumentService:
    @staticmethod
    def generate_application_pdf(application_data: dict) -> bytes:
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=A4)

        # PDF 내용 생성
        p.drawString(100, 750, f"사업신청서")
        p.drawString(100, 700, f"회사명: {application_data['company_name']}")
        p.drawString(100, 650, f"프로젝트: {application_data['project_title']}")

        p.save()
        buffer.seek(0)
        return buffer.getvalue()
```

## 8. 개발 단계별 구현 계획

### 1차 개발 (1개월)
**Frontend:**
- 프로젝트 초기 설정 및 ShadcnUI 통합
- 인증/권한 시스템 구현
- 회원관리 기본 기능
- 사업소개 모듈 (권한 기반)
- 공지관리 게시판
- 수요관리 폼 시스템

**Backend:**
- FastAPI 프로젝트 초기 설정
- 데이터베이스 모델 설계 및 마이그레이션
- JWT 인증 시스템 구현
- 사용자 관리 API
- 공지관리 API
- 기본 수요관리 API

### 2차 개발 (1개월)
**Frontend:**
- React Flow 에코시스템 맵 구현
- 밸리관리 시스템
- 참여기업 통계 대시보드
- 메인페이지 인터랙티브 시각화

**Backend:**
- 밸리관리 API 구현
- 통계 데이터 처리 서비스
- 파일 업로드/다운로드 API
- PDF 문서 자동 생성 서비스
- 백그라운드 작업 처리 (Celery)

### 3차 개발 (1개월)
**Frontend:**
- 반응형 웹 최적화
- 성능 최적화 (코드 분할, 이미지 최적화)
- 접근성 개선

**Backend:**
- 데이터 백업 시스템
- API 성능 최적화
- 로깅 및 모니터링 시스템
- 보안 강화 (레이트 리미팅, 입력 검증)

**통합:**
- 전체 기능 통합 테스트
- 배포 준비 및 문서화
