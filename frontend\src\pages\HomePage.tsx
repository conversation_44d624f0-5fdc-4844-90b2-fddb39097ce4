import React from 'react';
import Ecosystem from './home/<USER>';
import StatisticsSection from './home/<USER>';
import RecentActivities from './home/<USER>';
import ParticipatingCompanies from './home/<USER>';

/**
 * DeepTech Valley Platform 메인페이지
 * ETRI 브랜드 기반 그라데이션 배너, 독립적인 에코시스템 맵,
 * 주요 통계 섹션, 최근 활동 및 참여 기업 섹션을 포함
 */
const HomePage: React.FC = () => {

  return (
    <>
      {/* Hero 배너 - 에코시스템 맵과 겹치도록 확장 */}
      <section
        className="h-[90vh] bg-gradient-to-br from-etri-blue-600 via-etri-blue-500 to-etri-orange-500 relative"
        style={{
          width: '100vw',
          marginLeft: 'calc(-50vw + 50%)',
          marginRight: 'calc(-50vw + 50%)',
          overflow: 'visible'
        }}
      >
      
        {/* 현대적 배경 패턴 */}
        <div className="absolute inset-0">
          {/* 그리드 패턴 */}
          <div
            className="absolute inset-0 opacity-[0.05]"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255,255,255,0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.2) 1px, transparent 1px)
              `,
              backgroundSize: '60px 60px'
            }}
          ></div>

          {/* 플로팅 요소들 */}
          <div className="absolute top-16 left-16 w-2 h-2 bg-white/20 rounded-full animate-pulse"></div>
          <div className="absolute top-32 right-32 w-1 h-1 bg-white/30 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-24 left-1/4 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-2000"></div>
          <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-500"></div>

          {/* 글로우 효과 */}
          <div className="absolute top-20 left-20 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-etri-orange-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        {/* 메인 콘텐츠 - 중앙 정렬 */}
        <div className="max-w-7xl mx-auto px-8 pt-20 pb-20 relative z-20 h-full flex items-center justify-center">
          <div className="text-center space-y-12">
            {/* 서브 타이틀 */}
            <div className="flex items-center justify-center space-x-3">
              <div className="w-12 h-0.5 bg-gradient-to-r from-white to-etri-orange-300"></div>
              <span className="text-white/80 text-sm font-medium tracking-wider uppercase">
                ETRI Deep Tech Valley
              </span>
              <div className="w-12 h-0.5 bg-gradient-to-l from-white to-etri-orange-300"></div>
            </div>

            {/* 메인 타이틀 */}
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-black text-white leading-[0.9] tracking-tight drop-shadow-2xl">
              딥테크
              <br />
              <span className="bg-gradient-to-r from-white via-etri-orange-100 to-yellow-100 bg-clip-text text-transparent">
                스케일업
              </span>
              <br />
              <span className="text-4xl md:text-5xl lg:text-6xl font-light text-white/90">밸리</span>
            </h1>

            {/* 설명 텍스트 */}
            <p className="text-xl md:text-2xl text-white/90 leading-relaxed drop-shadow-lg max-w-4xl mx-auto font-light">
              ETRI가 주도하는 혁신적인 딥테크 생태계에서
              <span className="font-semibold text-etri-orange-200 bg-white/10 px-2 py-1 rounded-lg mx-1">
                차세대 기술
              </span>과
              <span className="font-semibold text-etri-orange-200 bg-white/10 px-2 py-1 rounded-lg mx-1">
                스케일업 기업
              </span>들이
              함께 미래를 만들어갑니다.
            </p>

            {/* CTA 버튼 */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="group px-10 py-5 bg-white text-etri-blue-600 font-bold rounded-2xl hover:bg-etri-orange-50 hover:text-etri-blue-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 relative overflow-hidden">
                <span className="relative z-10 flex items-center justify-center space-x-2">
                  <span>생태계 참여하기</span>
                  <svg className="w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-etri-blue-50 to-etri-orange-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>
              <button className="group px-10 py-5 border-2 border-white/40 text-white font-bold rounded-2xl hover:bg-white/10 hover:border-white/60 transition-all duration-300 backdrop-blur-sm relative overflow-hidden">
                <span className="relative z-10 flex items-center justify-center space-x-2">
                  <span>자세히 알아보기</span>
                  <svg className="w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* 배경 장식 요소 - 더 역동적 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-96 h-96 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-etri-orange-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/3 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-2xl animate-pulse delay-500"></div>
          <div className="absolute bottom-1/4 right-1/3 w-24 h-24 bg-etri-orange-300/10 rounded-full blur-xl animate-pulse delay-700"></div>

          {/* 기하학적 패턴 */}
          <div className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full"></div>
          <div className="absolute top-40 right-32 w-1 h-1 bg-white/40 rounded-full"></div>
          <div className="absolute bottom-32 left-20 w-3 h-3 bg-etri-orange-300/40 rounded-full"></div>
        </div>

        {/* 비대칭 곡선 하단 전환 - 왼쪽에서 오른쪽 아래로 흐름 */}
        <div className="absolute bottom-0 left-0 w-full overflow-hidden">
          <svg
            className="relative block w-full h-24"
            viewBox="0 0 1200 120"
            preserveAspectRatio="none"
          >
            <path
              d="M0,20 Q300,0 600,40 T1200,80 L1200,120 L0,120 Z"
              fill="rgba(249, 250, 251, 0.7)"
            />
          </svg>
        </div>

      </section>

      {/* 에코시스템 맵 섹션 - 히어로 배너와 겹치게 */}
      <section
        className="-mt-48 pt-20 pb-20 relative z-10"
        style={{
          width: '100vw',
          marginLeft: 'calc(-50vw + 50%)',
          marginRight: 'calc(-50vw + 50%)'
        }}
      >

        <div className="max-w-7xl mx-auto px-8 relative z-10">
          {/* 섹션 헤더 */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-12 h-0.5 bg-gradient-to-r from-etri-blue-600 to-etri-orange-500"></div>
              <span className="text-etri-blue-600 text-sm font-medium tracking-wider uppercase">
                Ecosystem Map
              </span>
              <div className="w-12 h-0.5 bg-gradient-to-l from-etri-blue-600 to-etri-orange-500"></div>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              딥테크 생태계 지도
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              ETRI를 중심으로 연결된 혁신적인 딥테크 생태계를 탐험해보세요.
              <br />
              각 노드를 호버하여 참여 기업과 기관들을 확인할 수 있습니다.
            </p>
          </div>

          {/* 에코시스템 맵 컨테이너 - 히어로 배너와 겹치기 */}
          <div className="relative">
            <div className="bg-white/20 backdrop-blur-[1px] rounded-3xl shadow-xl border border-white/30 p-12 relative overflow-visible">
              {/* 에코시스템 맵 */}
              <div className="relative" style={{ minHeight: '700px', overflow: 'visible' }}>
                <Ecosystem
                  className="w-full h-full"
                />
              </div>
            </div>



            {/* 장식 요소 */}
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-etri-blue-600 rounded-full opacity-20"></div>
            <div className="absolute -bottom-4 -right-4 w-6 h-6 bg-etri-orange-500 rounded-full opacity-30"></div>
            <div className="absolute top-1/4 -left-2 w-4 h-4 bg-etri-blue-400 rounded-full opacity-25"></div>
          </div>
        </div>
      </section>

      {/* 통계 섹션 */}
      <StatisticsSection />

      {/* 최근 활동 섹션 */}
      <RecentActivities />

      {/* 참여 기업 섹션 */}
      <ParticipatingCompanies />
    </>
  );
};

export default HomePage;
