@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ETRI 기반 컬러 시스템 */
    --etri-blue-50: #eff6ff;
    --etri-blue-100: #dbeafe;
    --etri-blue-500: #1c5aa0;
    --etri-blue-600: #1e4a8c;
    --etri-blue-700: #1a3f78;
    --etri-blue-900: #0f2852;

    --etri-orange-50: #fff7ed;
    --etri-orange-100: #ffedd5;
    --etri-orange-500: #f55a2c;
    --etri-orange-600: #ea580c;
    --etri-orange-700: #c2410c;
    --etri-orange-900: #7c2d12;

    /* ShadcnUI 기본 변수 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 79% 37%; /* ET<PERSON> Blue */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 79% 37%; /* ETRI Blue */
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: 'Pretendard', 'Noto Sans KR', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* ETRI 브랜드 컬러 유틸리티 */
  .text-etri-blue {
    color: var(--etri-blue-500);
  }

  .bg-etri-blue {
    background-color: var(--etri-blue-500);
  }

  .text-etri-orange {
    color: var(--etri-orange-500);
  }

  .bg-etri-orange {
    background-color: var(--etri-orange-500);
  }
}
