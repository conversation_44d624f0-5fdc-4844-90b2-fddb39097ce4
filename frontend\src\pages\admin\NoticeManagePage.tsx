import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockNotices, mockNoticeStats } from '@/data/mockNotices';
import type { NoticeSearchParams } from '@/types/notice';
import { NoticeCard } from '@/components/notices/NoticeCard';
import { NoticeSearch } from '@/components/notices/NoticeSearch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge';
import { Plus, BarChart3, FileText, AlertCircle, Clock } from 'lucide-react';

export const NoticeManagePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useState<NoticeSearchParams>({
    query: '',
    category: 'all',
    status: 'all',
  });

  // 검색 및 필터링된 공지사항
  const filteredNotices = mockNotices.filter((notice) => {
    if (searchParams.query) {
      const query = searchParams.query.toLowerCase();
      const matchesTitle = notice.title.toLowerCase().includes(query);
      const matchesContent = notice.content.toLowerCase().includes(query);
      if (!matchesTitle && !matchesContent) return false;
    }

    if (searchParams.category && searchParams.category !== 'all') {
      if (notice.category !== searchParams.category) return false;
    }

    if (searchParams.status && searchParams.status !== 'all') {
      if (notice.status !== searchParams.status) return false;
    }

    if (searchParams.isImportant !== undefined) {
      if (notice.isImportant !== searchParams.isImportant) return false;
    }

    return true;
  });

  const handleCreateNotice = () => {
    navigate('/admin/notices/new');
  };

  const handleEditNotice = (noticeId: string) => {
    navigate(`/admin/notices/${noticeId}/edit`);
  };

  const handleDeleteNotice = (noticeId: string) => {
    // 실제 구현에서는 삭제 확인 모달 표시 후 API 호출
    if (confirm('정말로 이 공지사항을 삭제하시겠습니까?')) {
      console.log(`Deleting notice: ${noticeId}`);
      alert('공지사항이 삭제되었습니다. (실제 구현에서는 API 호출)');
    }
  };

  const handleNoticeClick = (noticeId: string) => {
    navigate(`/notices/${noticeId}`);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* 헤더 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">공지사항 관리</h1>
          <p className="text-gray-600 mt-1">
            공지사항을 작성, 편집, 삭제하고 통계를 확인하세요
          </p>
        </div>
        
        <Button onClick={handleCreateNotice}>
          <Plus className="h-4 w-4 mr-2" />
          새 공지사항 작성
        </Button>
      </div>

      {/* 통계 카드 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">전체 공지사항</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockNoticeStats.total}</div>
            <p className="text-xs text-muted-foreground">
              총 공지사항 수
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">게시된 공지</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{mockNoticeStats.published}</div>
            <p className="text-xs text-muted-foreground">
              현재 게시 중
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">임시저장</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{mockNoticeStats.draft}</div>
            <p className="text-xs text-muted-foreground">
              작성 중인 공지
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">중요 공지</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{mockNoticeStats.important}</div>
            <p className="text-xs text-muted-foreground">
              중요 표시된 공지
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 검색 및 필터링 */}
      <div className="mb-8">
        <NoticeSearch
          searchParams={searchParams}
          onSearchChange={setSearchParams}
        />
      </div>

      {/* 결과 통계 */}
      <div className="flex justify-between items-center mb-6">
        <div className="text-sm text-gray-600">
          총 <span className="font-semibold text-gray-900">{filteredNotices.length}</span>개의 공지사항
          {searchParams.query && (
            <span> ('{searchParams.query}' 검색 결과)</span>
          )}
        </div>
        
        {/* 상태별 필터 빠른 버튼 */}
        <div className="flex gap-2">
          <Button
            variant={searchParams.status === 'published' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSearchParams({ ...searchParams, status: 'published' })}
          >
            게시됨
          </Button>
          <Button
            variant={searchParams.status === 'draft' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSearchParams({ ...searchParams, status: 'draft' })}
          >
            임시저장
          </Button>
          <Button
            variant={searchParams.status === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSearchParams({ ...searchParams, status: 'all' })}
          >
            전체
          </Button>
        </div>
      </div>

      {/* 공지사항 목록 */}
      {filteredNotices.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">📢</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            공지사항이 없습니다
          </h3>
          <p className="text-gray-600 mb-6">
            {searchParams.query || searchParams.category !== 'all' || searchParams.status !== 'all'
              ? '검색 조건을 변경해 보세요'
              : '첫 번째 공지사항을 작성해 보세요'}
          </p>
          <Button onClick={handleCreateNotice}>
            <Plus className="h-4 w-4 mr-2" />
            공지사항 작성
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredNotices.map((notice) => (
            <NoticeCard
              key={notice.id}
              notice={notice}
              onClick={() => handleNoticeClick(notice.id)}
              showActions={true}
              onEdit={() => handleEditNotice(notice.id)}
              onDelete={() => handleDeleteNotice(notice.id)}
            />
          ))}
        </div>
      )}

      {/* 페이지네이션 (향후 구현) */}
      {filteredNotices.length > 0 && (
        <div className="flex justify-center mt-12">
          <div className="text-sm text-gray-500">
            페이지네이션은 백엔드 API 연동 시 구현됩니다
          </div>
        </div>
      )}
    </div>
  );
};
