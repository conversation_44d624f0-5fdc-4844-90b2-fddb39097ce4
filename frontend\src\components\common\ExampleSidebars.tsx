import React from 'react';
import { 
  Users, 
  Building, 
  FileText, 
  Settings, 
  BarChart3, 
  Calendar,
  Mail,
  Phone,
  MapPin,
  Award,
  Briefcase,
  GraduationCap,
  Lightbulb,
  Target,
  TrendingUp
} from 'lucide-react';
import { Sidebar, SidebarMenuItem } from './Sidebar';

// 관리자 페이지용 사이드바
export const AdminSidebar: React.FC = () => {
  const adminMenuItems: SidebarMenuItem[] = [
    {
      id: 'dashboard',
      name: '대시보드',
      icon: BarChart3,
      path: '/admin/dashboard'
    },
    {
      id: 'users',
      name: '사용자 관리',
      icon: Users,
      path: '/admin/users',
      subItems: [
        { id: 'user-list', name: '사용자 목록', path: '/admin/users/list' },
        { id: 'user-roles', name: '권한 관리', path: '/admin/users/roles' },
        { id: 'user-activity', name: '활동 로그', path: '/admin/users/activity' }
      ]
    },
    {
      id: 'content',
      name: '콘텐츠 관리',
      icon: FileText,
      path: '/admin/content',
      subItems: [
        { id: 'posts', name: '게시글 관리', path: '/admin/content/posts' },
        { id: 'pages', name: '페이지 관리', path: '/admin/content/pages' },
        { id: 'media', name: '미디어 관리', path: '/admin/content/media' }
      ]
    },
    {
      id: 'settings',
      name: '시스템 설정',
      icon: Settings,
      path: '/admin/settings'
    }
  ];

  return (
    <Sidebar
      title="관리자"
      menuItems={adminMenuItems}
      width="w-72"
    />
  );
};

// 기업 정보 페이지용 사이드바
export const CompanySidebar: React.FC = () => {
  const companyMenuItems: SidebarMenuItem[] = [
    {
      id: 'overview',
      name: '회사 개요',
      icon: Building,
      path: '/company/overview'
    },
    {
      id: 'history',
      name: '연혁',
      icon: Calendar,
      path: '/company/history'
    },
    {
      id: 'organization',
      name: '조직도',
      icon: Users,
      path: '/company/organization',
      subItems: [
        { id: 'leadership', name: '경영진', path: '/company/organization/leadership' },
        { id: 'departments', name: '부서별 조직', path: '/company/organization/departments' },
        { id: 'research-teams', name: '연구팀', path: '/company/organization/research-teams' }
      ]
    },
    {
      id: 'achievements',
      name: '성과 및 수상',
      icon: Award,
      path: '/company/achievements'
    },
    {
      id: 'contact',
      name: '연락처',
      icon: Phone,
      path: '/company/contact',
      subItems: [
        { id: 'office-info', name: '사무실 정보', path: '/company/contact/office' },
        { id: 'departments-contact', name: '부서별 연락처', path: '/company/contact/departments' },
        { id: 'location', name: '오시는 길', path: '/company/contact/location' }
      ]
    }
  ];

  return (
    <Sidebar
      title="회사소개"
      menuItems={companyMenuItems}
      width="w-80"
    />
  );
};

// 연구개발 페이지용 사이드바
export const ResearchSidebar: React.FC = () => {
  const researchMenuItems: SidebarMenuItem[] = [
    {
      id: 'overview',
      name: 'R&D 개요',
      icon: Lightbulb,
      path: '/research/overview'
    },
    {
      id: 'projects',
      name: '연구 프로젝트',
      icon: Target,
      path: '/research/projects',
      subItems: [
        { id: 'current-projects', name: '진행중인 프로젝트', path: '/research/projects/current' },
        { id: 'completed-projects', name: '완료된 프로젝트', path: '/research/projects/completed' },
        { id: 'future-projects', name: '계획중인 프로젝트', path: '/research/projects/future' }
      ]
    },
    {
      id: 'publications',
      name: '연구 성과',
      icon: GraduationCap,
      path: '/research/publications',
      subItems: [
        { id: 'papers', name: '논문', path: '/research/publications/papers' },
        { id: 'patents', name: '특허', path: '/research/publications/patents' },
        { id: 'reports', name: '연구보고서', path: '/research/publications/reports' }
      ]
    },
    {
      id: 'collaboration',
      name: '산학협력',
      icon: Building,
      path: '/research/collaboration'
    },
    {
      id: 'facilities',
      name: '연구시설',
      icon: MapPin,
      path: '/research/facilities'
    }
  ];

  return (
    <Sidebar
      title="연구개발"
      menuItems={researchMenuItems}
      width="w-80"
    />
  );
};

// 사업 분야 페이지용 사이드바
export const BusinessSidebar: React.FC = () => {
  const businessMenuItems: SidebarMenuItem[] = [
    {
      id: 'overview',
      name: '사업 개요',
      icon: Briefcase,
      path: '/business/overview'
    },
    {
      id: 'solutions',
      name: '솔루션',
      icon: Lightbulb,
      path: '/business/solutions',
      subItems: [
        { id: 'ai-solutions', name: 'AI 솔루션', path: '/business/solutions/ai' },
        { id: 'iot-solutions', name: 'IoT 솔루션', path: '/business/solutions/iot' },
        { id: 'cloud-solutions', name: '클라우드 솔루션', path: '/business/solutions/cloud' },
        { id: 'security-solutions', name: '보안 솔루션', path: '/business/solutions/security' }
      ]
    },
    {
      id: 'industries',
      name: '산업 분야',
      icon: Building,
      path: '/business/industries',
      subItems: [
        { id: 'manufacturing', name: '제조업', path: '/business/industries/manufacturing' },
        { id: 'healthcare', name: '헬스케어', path: '/business/industries/healthcare' },
        { id: 'finance', name: '금융', path: '/business/industries/finance' },
        { id: 'education', name: '교육', path: '/business/industries/education' }
      ]
    },
    {
      id: 'case-studies',
      name: '성공 사례',
      icon: TrendingUp,
      path: '/business/case-studies'
    },
    {
      id: 'partnerships',
      name: '파트너십',
      icon: Users,
      path: '/business/partnerships'
    }
  ];

  return (
    <Sidebar
      title="사업분야"
      menuItems={businessMenuItems}
      width="w-80"
    />
  );
};

// 고객 지원 페이지용 사이드바
export const SupportSidebar: React.FC = () => {
  const supportMenuItems: SidebarMenuItem[] = [
    {
      id: 'faq',
      name: '자주 묻는 질문',
      icon: FileText,
      path: '/support/faq'
    },
    {
      id: 'documentation',
      name: '문서',
      icon: FileText,
      path: '/support/docs',
      subItems: [
        { id: 'user-guide', name: '사용자 가이드', path: '/support/docs/user-guide' },
        { id: 'api-docs', name: 'API 문서', path: '/support/docs/api' },
        { id: 'tutorials', name: '튜토리얼', path: '/support/docs/tutorials' }
      ]
    },
    {
      id: 'contact',
      name: '문의하기',
      icon: Mail,
      path: '/support/contact',
      subItems: [
        { id: 'technical-support', name: '기술 지원', path: '/support/contact/technical' },
        { id: 'sales-inquiry', name: '영업 문의', path: '/support/contact/sales' },
        { id: 'general-inquiry', name: '일반 문의', path: '/support/contact/general' }
      ]
    },
    {
      id: 'downloads',
      name: '다운로드',
      icon: FileText,
      path: '/support/downloads'
    }
  ];

  return (
    <Sidebar
      title="고객지원"
      menuItems={supportMenuItems}
      width="w-72"
    />
  );
};
