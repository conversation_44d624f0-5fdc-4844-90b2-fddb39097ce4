import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { mockActivities, getCategoryColor, type Activity } from '@/data/mockActivities';
import { Calendar, ArrowRight } from 'lucide-react';

interface RecentActivitiesProps {
  className?: string;
  maxItems?: number;
}

const RecentActivities: React.FC<RecentActivitiesProps> = ({ 
  className = '', 
  maxItems = 6 
}) => {
  const displayActivities = mockActivities.slice(0, maxItems);

  return (
    <section className={`py-16 px-4 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto">
        {/* 섹션 헤더 */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            최근 활동
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ETRI 딥테크 밸리의 최신 소식과 활동을 확인하세요
          </p>
        </div>

        {/* 활동 카드 그리드 */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {displayActivities.map((activity) => (
            <Card 
              key={activity.id} 
              className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer group bg-white border-gray-200"
            >
              <CardHeader className="pb-4">
                {/* 카테고리 배지 */}
                <div className="flex items-center justify-between mb-3">
                  <Badge 
                    variant="outline" 
                    className={`${getCategoryColor(activity.category)} border`}
                  >
                    {activity.category}
                  </Badge>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="w-4 h-4 mr-1" />
                    {activity.date}
                  </div>
                </div>

                {/* 제목 */}
                <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-etri-blue-600 transition-colors line-clamp-2">
                  {activity.title}
                </CardTitle>
              </CardHeader>

              {/* 설명 */}
              {activity.description && (
                <CardContent className="pt-0">
                  <CardDescription className="text-gray-600 line-clamp-3 leading-relaxed">
                    {activity.description}
                  </CardDescription>
                  
                  {/* 더보기 링크 */}
                  <div className="flex items-center mt-4 text-etri-blue-600 group-hover:text-etri-blue-700 transition-colors">
                    <span className="text-sm font-medium">자세히 보기</span>
                    <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>

        {/* 더보기 버튼 */}
        <div className="text-center mt-12">
          <button className="inline-flex items-center px-8 py-3 bg-etri-blue-600 text-white font-semibold rounded-xl hover:bg-etri-blue-700 transition-colors duration-300 shadow-lg hover:shadow-xl">
            <span>모든 활동 보기</span>
            <ArrowRight className="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default RecentActivities;
