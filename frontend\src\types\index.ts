// TypeScript 타입 정의 export

import React from 'react';

// 통계 카드 데이터 타입
export interface StatisticItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  value: number;
  suffix: string;
  label: string;
  description?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
}

// 통계 섹션 데이터 타입
export interface StatisticsData {
  title: string;
  subtitle?: string;
  items: StatisticItem[];
}
