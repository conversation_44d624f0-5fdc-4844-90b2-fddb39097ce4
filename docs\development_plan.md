# DeepTech Valley Platform 개발 계획서

## 1. 프로젝트 개요

### 1.1 프로젝트 정보
- **프로젝트명**: DeepTech Valley Platform
- **개발 기간**: 2025.06.17 ~ 2025.09.15 (3개월)
- **개발 방식**: 3단계 순차 개발
- **총 작업 수**: 29개 (완료 2개 + 진행 예정 27개)
- **기술 스택**: React 18 + TypeScript + Vite + ShadcnUI + Python + FastAPI

### 1.2 개발 목표
밸리 내 기업 소통, 공모, 선정을 지원하는 통합 오픈플랫폼 개발

## 2. 전체 개발 작업 분해

### 2.1 1차 개발 작업 분해 (1개월) - 핵심 기능

### 작업 1: 프로젝트 초기 설정 및 개발 환경 구축
**예상 기간**: 3-4일  
**담당**: 풀스택 개발자  
**우선순위**: 최고

**주요 작업:**
- 프론트엔드 프로젝트 생성 (React + TypeScript + Vite)
- 백엔드 프로젝트 생성 (Python + FastAPI)
- ShadcnUI 설치 및 설정
- TailwindCSS 설정
- 기본 프로젝트 구조 생성
- Git 저장소 초기화

**완료 기준:**
- 프론트엔드/백엔드 개발 서버 정상 실행
- ShadcnUI 컴포넌트 정상 렌더링
- 기본 라우팅 작동

**관련 파일:**
- `package.json`, `requirements.txt`
- `vite.config.ts`, `tailwind.config.js`
- `components.json`

---

### 작업 2: 데이터베이스 모델 설계 및 마이그레이션 시스템 구축
**예상 기간**: 3-4일  
**담당**: 백엔드 개발자  
**우선순위**: 최고  
**의존성**: 작업 1

**주요 작업:**
- SQLAlchemy 모델 클래스 설계
- User, Notice, Application, Company 모델 정의
- Alembic 마이그레이션 설정
- 데이터베이스 연결 설정
- 초기 마이그레이션 생성 및 실행

**완료 기준:**
- 데이터베이스 테이블 정상 생성
- 마이그레이션 오류 없이 실행
- 모델 간 관계 올바르게 설정

**관련 파일:**
- `backend/app/models/user.py`
- `backend/app/models/notice.py`
- `backend/app/models/application.py`
- `backend/app/core/database.py`

---

### 작업 3: JWT 기반 인증 시스템 구현
**예상 기간**: 4-5일  
**담당**: 백엔드 개발자  
**우선순위**: 최고  
**의존성**: 작업 2

**주요 작업:**
- JWT 토큰 생성/검증 함수 구현
- 비밀번호 해싱 (bcrypt) 구현
- 로그인 API 엔드포인트 구현
- JWT 토큰 검증 의존성 함수 구현
- 권한 기반 접근 제어 데코레이터 구현
- 토큰 갱신 API 구현

**완료 기준:**
- 로그인 API 정상 작동
- JWT 토큰 올바르게 생성/검증
- 권한 기반 접근 제어 정상 동작

**관련 파일:**
- `backend/app/core/security.py`
- `backend/app/api/auth.py`
- `backend/app/schemas/auth.py`
- `backend/app/services/auth_service.py`

---

### 작업 4: 프론트엔드 인증 시스템 및 라우팅 구현
**예상 기간**: 4-5일  
**담당**: 프론트엔드 개발자  
**우선순위**: 최고  
**의존성**: 작업 3

**주요 작업:**
- AuthContext 생성 및 Provider 구현
- useAuth 커스텀 훅 구현
- ProtectedRoute 컴포넌트 구현
- 로그인/회원가입 폼 컴포넌트 (ShadcnUI)
- API 클라이언트 설정 (axios interceptors)
- 토큰 자동 갱신 로직 구현
- React Router 설정

**완료 기준:**
- 로그인/회원가입 정상 작동
- 인증 상태 올바르게 관리
- 보호된 라우트 접근 제어 정상 동작

**관련 파일:**
- `frontend/src/contexts/AuthContext.tsx`
- `frontend/src/hooks/useAuth.ts`
- `frontend/src/components/ProtectedRoute.tsx`
- `frontend/src/pages/auth/LoginPage.tsx`
- `frontend/src/services/apiClient.ts`

---

### 작업 5: 사용자 관리 API 및 프로필 관리 시스템 구현
**예상 기간**: 4-5일  
**담당**: 풀스택 개발자  
**우선순위**: 높음  
**의존성**: 작업 4

**주요 작업:**
- 사용자 CRUD API 엔드포인트 구현
- 사용자 프로필 스키마 정의 (Pydantic)
- 사용자 서비스 레이어 구현
- 프론트엔드 사용자 관리 페이지 구현
- 사용자 프로필 편집 폼 구현
- 관리자 사용자 목록 및 권한 관리 인터페이스

**완료 기준:**
- 사용자 CRUD 기능 정상 작동
- 프로필 편집 가능
- 관리자가 사용자 권한 관리 가능

**관련 파일:**
- `backend/app/api/users.py`
- `backend/app/schemas/user.py`
- `frontend/src/pages/admin/UserManagement.tsx`
- `frontend/src/pages/profile/ProfilePage.tsx`

---

### 작업 6: 공지관리 시스템 구현
**예상 기간**: 3-4일  
**담당**: 풀스택 개발자  
**우선순위**: 중간  
**의존성**: 작업 5

**주요 작업:**
- 공지사항 CRUD API 구현
- 파일 업로드 API 구현
- 검색 및 페이지네이션 기능
- 공지사항 목록/상세 페이지 구현
- 관리자 공지사항 작성/편집 폼
- 중요 공지 상단 고정 기능

**완료 기준:**
- 공지사항 CRUD 정상 작동
- 파일 첨부 가능
- 검색 및 필터링 정상 동작

**관련 파일:**
- `backend/app/api/notices.py`
- `frontend/src/pages/notice/NoticeList.tsx`
- `frontend/src/pages/admin/NoticeManagement.tsx`

---

### 작업 7: 수요관리 폼 기반 신청 시스템 구현
**예상 기간**: 5-6일  
**담당**: 풀스택 개발자  
**우선순위**: 최고  
**의존성**: 작업 6

**주요 작업:**
- 사업신청 폼 데이터 모델 및 API 구현
- 다중 파일 업로드 API (청크 업로드 지원)
- 임시저장 기능 구현
- 신청서 상태 관리 (제출/검토중/승인/거부)
- 프론트엔드 다단계 폼 구현
- 파일 업로드 진행률 표시
- 관리자 신청서 검토 인터페이스

**완료 기준:**
- 사업신청 폼 정상 작동
- 파일 업로드 가능
- 임시저장 및 제출 정상 동작

**관련 파일:**
- `backend/app/api/demands.py`
- `backend/app/api/files.py`
- `frontend/src/pages/demand/ApplicationForm.tsx`
- `frontend/src/components/forms/FileUpload.tsx`

---

### 작업 8: PDF 문서 자동 생성 및 출력 시스템 구현
**예상 기간**: 4-5일  
**담당**: 백엔드 개발자  
**우선순위**: 높음  
**의존성**: 작업 7

**주요 작업:**
- ReportLab을 사용한 PDF 생성 서비스 구현
- 신청서 데이터를 PDF로 변환하는 템플릿 시스템
- 문서 생성 API 엔드포인트
- 생성된 문서 저장 및 관리
- 프론트엔드 문서 생성/다운로드 인터페이스
- 문서 미리보기 기능

**완료 기준:**
- 신청서 데이터가 PDF로 정상 생성
- 다운로드 가능
- 문서 형식 올바름

**관련 파일:**
- `backend/app/services/document_service.py`
- `backend/app/templates/application_template.py`
- `backend/app/api/documents.py`
- `frontend/src/components/DocumentViewer.tsx`

---

### 작업 9: 기본 통계 시스템 및 대시보드 구현
**예상 기간**: 3-4일  
**담당**: 풀스택 개발자  
**우선순위**: 중간  
**의존성**: 작업 8

**주요 작업:**
- 통계 데이터 수집 서비스 구현
- 기본 통계 API (총 기업 수, 신청 수, 승인률 등)
- Chart.js를 사용한 기본 차트 컴포넌트
- 관리자 대시보드 페이지 구현
- 실시간 데이터 갱신 (React Query)
- 애니메이션 카운터 컴포넌트

**완료 기준:**
- 기본 통계 데이터 정상 수집
- 차트 올바르게 표시
- 실시간 데이터 갱신 작동

**관련 파일:**
- `backend/app/api/statistics.py`
- `frontend/src/pages/dashboard/AdminDashboard.tsx`
- `frontend/src/components/charts/BasicChart.tsx`
- `frontend/src/components/AnimatedCounter.tsx`

---

### 작업 10: 사업소개 모듈 및 권한 기반 접근 제어 구현
**예상 기간**: 4-5일  
**담당**: 풀스택 개발자  
**우선순위**: 높음  
**의존성**: 작업 9

**주요 작업:**
- 사업소개 콘텐츠 모델 및 API 구현
- 권한 기반 접근 제어 미들웨어
- Wiki 형태 콘텐츠 에디터 (마크다운 지원)
- 트리 구조 게시판 구현
- 소스코드 뷰어 컴포넌트 (문법 하이라이팅)
- 권한별 콘텐츠 표시/숨김 처리

**완료 기준:**
- 권한 기반 접근 제어 정상 작동
- Wiki 형태 콘텐츠 편집 가능
- 소스코드 뷰어 정상 동작

**관련 파일:**
- `backend/app/api/business.py`
- `frontend/src/pages/business/BusinessIntro.tsx`
- `frontend/src/components/CodeViewer.tsx`
- `frontend/src/components/PermissionGate.tsx`

### 2.2 2차 개발 작업 분해 (1개월) - 고급 기능

### 작업 11: D3.js 에코시스템 맵 기반 구조 구축
**예상 기간**: 4-5일
**담당**: 프론트엔드 개발자
**우선순위**: 최고
**의존성**: 작업 10

**주요 작업:**
- D3.js 설치 및 React 통합 훅 구현
- SVG 기반 시각화 컨테이너 생성
- 포스 시뮬레이션 (forceSimulation) 설정
- 노드와 링크 데이터 구조 정의
- 기본 드래그, 줌, 팬 기능 구현
- 반응형 SVG 크기 조정 로직

**완료 기준:**
- 기본 노드-링크 그래프 렌더링
- 드래그/줌 상호작용 정상 작동
- 반응형 크기 조정 동작

---

### 작업 12: 밸리관리 API 및 데이터 모델 구현
**예상 기간**: 4-5일
**담당**: 백엔드 개발자
**우선순위**: 최고
**의존성**: 작업 11

**주요 작업:**
- Company, Institution, Relationship 모델 정의
- 밸리관리 API 엔드포인트 구현
- 기업-기관 관계 매핑 로직
- 에코시스템 데이터 집계 서비스
- 기업 정보 검색 및 필터링 API
- 관계 강도 및 유형 관리 시스템

**완료 기준:**
- 기업/기관 정보 정상 저장
- 관계 데이터 올바르게 관리
- 에코시스템 API 정상 응답

---

### 작업 13: 참여기업 통계 시스템 구현
**예상 기간**: 5-6일
**담당**: 풀스택 개발자
**우선순위**: 높음
**의존성**: 작업 12

**주요 작업:**
- pandas를 사용한 통계 데이터 처리 서비스
- 기업 규모별/분야별/지역별 집계 API
- Chart.js 고급 차트 컴포넌트 (도넛, 막대, 라인)
- 통계 필터링 및 날짜 범위 선택
- 데이터 드릴다운 기능
- CSV/Excel 데이터 내보내기

**완료 기준:**
- 통계 데이터 정확히 집계
- 차트 올바르게 표시
- 필터링 및 내보내기 기능 정상 작동

---

### 작업 14: 메인페이지 인터랙티브 에코시스템 맵 구현
**예상 기간**: 5-6일
**담당**: 프론트엔드 개발자
**우선순위**: 최고
**의존성**: 작업 13

**주요 작업:**
- 메인페이지 에코시스템 맵 컴포넌트 구현
- 노드 호버 시 툴팁 및 관련 기업 하이라이트
- 클릭 시 기업 상세 페이지 이동
- Framer Motion을 사용한 진입 애니메이션
- 하단 통계 지표 (아이콘 + 숫자) 구현
- 실시간 데이터 업데이트 및 애니메이션

**완료 기준:**
- 메인페이지 에코시스템 맵 부드럽게 애니메이션
- 호버/클릭 상호작용 정상 작동
- 통계 지표 실시간 업데이트

---

### 작업 15: 밸리관리 시스템 통합 및 관리 인터페이스 구현
**예상 기간**: 4-5일
**담당**: 풀스택 개발자
**우선순위**: 높음
**의존성**: 작업 14

**주요 작업:**
- 밸리관리 메인 페이지 구현
- 기업/기관 정보 CRUD 인터페이스
- 관계 설정 및 편집 모달
- 관리자용 에코시스템 맵 뷰 (편집 가능)
- CSV/Excel 데이터 가져오기 기능
- 벌크 데이터 처리 및 검증

**완료 기준:**
- 밸리관리 인터페이스 정상 작동
- 데이터 편집 가능
- 가져오기/내보내기 기능 정상 동작

### 2.3 3차 개발 작업 분해 (1개월) - 최적화 및 완성

### 작업 16: 반응형 웹 디자인 최적화 및 모바일 UX 개선
**예상 기간**: 4-5일
**담당**: 프론트엔드 개발자
**우선순위**: 최고
**의존성**: 작업 15

**주요 작업:**
- 모든 컴포넌트의 반응형 클래스 점검 및 수정
- 모바일 네비게이션 (햄버거 메뉴, 하단 탭) 구현
- 터치 제스처 지원 (스와이프, 핀치 줌)
- 모바일 폼 최적화 (키보드 대응, 입력 필드 크기)
- 에코시스템 맵 모바일 최적화
- 크로스 브라우저 테스트 및 수정

**완료 기준:**
- 모든 화면 크기에서 UI 올바르게 표시
- 모바일 터치 인터페이스 정상 작동
- 주요 브라우저에서 호환성 확인

---

### 작업 17: 성능 최적화 및 번들 크기 최적화
**예상 기간**: 3-4일
**담당**: 프론트엔드 개발자
**우선순위**: 높음
**의존성**: 작업 16

**주요 작업:**
- Webpack Bundle Analyzer로 번들 크기 분석
- React.lazy를 사용한 페이지별 코드 분할
- 이미지 최적화 (WebP 변환, 지연 로딩)
- React.memo, useMemo, useCallback 최적화
- API 응답 캐싱 (React Query 설정)
- 불필요한 리렌더링 방지

**완료 기준:**
- 초기 로딩 시간 3초 이내
- 번들 크기 최적화
- 메모리 사용량 안정적

---

### 작업 18: 접근성 개선 및 WCAG 2.1 표준 준수
**예상 기간**: 4-5일
**담당**: 프론트엔드 개발자
**우선순위**: 높음
**의존성**: 작업 17

**주요 작업:**
- 모든 인터랙티브 요소에 키보드 접근성 추가
- ARIA 라벨 및 역할 속성 설정
- 색상 대비 4.5:1 이상 확보
- 포커스 표시 개선 (focus-visible)
- 스크린 리더 테스트 및 최적화
- 접근성 자동 테스트 도구 통합

**완료 기준:**
- WCAG 2.1 AA 수준 준수
- 키보드만으로 모든 기능 접근 가능
- 스크린 리더 정상 동작

---

### 작업 19: 데이터 백업 시스템 및 복구 메커니즘 구현
**예상 기간**: 3-4일
**담당**: 백엔드 개발자
**우선순위**: 높음
**의존성**: 작업 18

**주요 작업:**
- Celery를 사용한 자동 백업 스케줄링
- 데이터베이스 덤프 및 복원 스크립트
- 파일 시스템 백업 (업로드된 파일)
- 백업 데이터 무결성 검증
- 백업 로그 및 모니터링
- 복구 테스트 및 문서화

**완료 기준:**
- 자동 백업 정상 실행
- 백업 데이터로부터 복구 가능
- 백업 상태 모니터링 동작

---

### 작업 20: 보안 강화 및 취약점 점검
**예상 기간**: 4-5일
**담당**: 풀스택 개발자
**우선순위**: 최고
**의존성**: 작업 19

**주요 작업:**
- OWASP Top 10 취약점 점검 및 수정
- 입력 데이터 검증 및 sanitization 강화
- SQL 인젝션 방지 (SQLAlchemy ORM 활용)
- XSS 방지 (DOMPurify 적용)
- CSRF 토큰 구현
- 보안 헤더 설정 (HTTPS, CSP, HSTS)

**완료 기준:**
- 보안 취약점 스캔 통과
- 모든 입력 데이터 검증
- 보안 헤더 정상 설정

---

### 작업 21: 통합 테스트 및 E2E 테스트 구현
**예상 기간**: 4-5일
**담당**: 풀스택 개발자
**우선순위**: 높음
**의존성**: 작업 20

**주요 작업:**
- Playwright를 사용한 E2E 테스트 구현
- 주요 사용자 플로우 테스트 시나리오
- API 통합 테스트 (pytest)
- 성능 테스트 (Lighthouse CI)
- 크로스 브라우저 테스트
- CI/CD 파이프라인 테스트 통합

**완료 기준:**
- 모든 E2E 테스트 통과
- API 통합 테스트 성공
- 성능 기준 충족

---

### 작업 22: 배포 준비 및 운영 환경 설정
**예상 기간**: 3-4일
**담당**: DevOps/풀스택 개발자
**우선순위**: 최고
**의존성**: 작업 21

**주요 작업:**
- 프로덕션 환경 설정 파일 작성
- 환경변수 보안 관리 (secrets)
- 구조화된 로깅 시스템 구현
- 헬스체크 엔드포인트 구현
- 배포 스크립트 및 Docker 설정
- 운영 매뉴얼 및 트러블슈팅 가이드

**완료 기준:**
- 프로덕션 환경에서 정상 배포
- 모니터링 작동
- 운영 문서 완성

## 3. 전체 개발 일정 및 마일스톤

### 3.1 1차 개발 일정 (1개월) - 핵심 기능
```
Week 1: 작업 1-3 (환경 설정, DB 모델, 인증 시스템)
Week 2: 작업 4-5 (프론트엔드 인증, 사용자 관리)
Week 3: 작업 6-8 (공지관리, 수요관리, PDF 생성)
Week 4: 작업 9-10 (통계 시스템, 사업소개 모듈)
```

### 3.2 2차 개발 일정 (1개월) - 고급 기능
```
Week 5: 작업 11-12 (D3.js 에코시스템 맵, 밸리관리 API)
Week 6: 작업 13 (참여기업 통계 시스템)
Week 7: 작업 14 (메인페이지 인터랙티브 맵)
Week 8: 작업 15 (밸리관리 통합 인터페이스)
```

### 3.3 3차 개발 일정 (1개월) - 최적화 및 완성
```
Week 9: 작업 16-17 (반응형 최적화, 성능 최적화)
Week 10: 작업 18-19 (접근성 개선, 데이터 백업)
Week 11: 작업 20-21 (보안 강화, 통합 테스트)
Week 12: 작업 22 (배포 준비 및 운영 환경)
```

### 3.4 주요 마일스톤
**1차 개발 완료 (Month 1):**
- 기본 인증 시스템 작동
- 사용자 관리 기능 완성
- 핵심 비즈니스 로직 완성 (회원관리, 사업소개, 공지관리, 수요관리)

**2차 개발 완료 (Month 2):**
- D3.js 에코시스템 맵 구현
- 밸리관리 시스템 완성
- 참여기업 통계 및 시각화
- 메인페이지 인터랙티브 기능

**3차 개발 완료 (Month 3):**
- 반응형 웹 최적화
- 성능 및 접근성 개선
- 보안 강화 및 백업 시스템
- 배포 준비 완료

## 4. 리스크 관리

### 4.1 기술적 리스크
- **ShadcnUI 학습 곡선**: 초기 설정 단계에서 충분한 시간 할당
- **파일 업로드 복잡성**: 청크 업로드 구현 시 추가 시간 필요 가능성
- **PDF 생성 템플릿**: 기관 요구사항에 맞는 디자인 조정 필요

### 4.2 일정 리스크
- **의존성 지연**: 선행 작업 지연 시 후속 작업 영향
- **요구사항 변경**: 기관 피드백에 따른 수정 작업 발생 가능

### 4.3 대응 방안
- 각 작업에 20% 버퍼 시간 포함
- 주간 진행 상황 점검 및 조정
- 핵심 기능 우선 개발 후 부가 기능 추가

## 5. 품질 관리

### 5.1 코드 품질
- shrimp-rules.md 표준 준수
- TypeScript 타입 안정성 확보
- ESLint/Prettier 코드 포맷팅

### 5.2 테스트 전략
- 단위 테스트: 핵심 비즈니스 로직
- 통합 테스트: API 엔드포인트
- E2E 테스트: 주요 사용자 플로우

### 5.3 문서화
- API 문서 자동 생성 (FastAPI Swagger)
- 컴포넌트 문서화 (Storybook)
- 사용자 매뉴얼 작성
