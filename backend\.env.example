# 백엔드 환경변수 예시 파일
# 실제 사용 시 .env 파일로 복사하여 사용

# 애플리케이션 설정
APP_NAME=DeepTech Valley Platform
APP_VERSION=1.0.0
DEBUG=true

# 데이터베이스 설정 (PostgreSQL 또는 MySQL 중 선택)
# PostgreSQL 예시
DATABASE_URL=postgresql://username:password@localhost:5432/deeptech_valley
# MySQL 예시
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/deeptech_valley

DATABASE_ECHO=false

# 보안 설정
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS 설정
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:5173"]

# 파일 업로드 설정
MAX_FILE_SIZE=104857600
UPLOAD_DIR=uploads

# Redis 설정 (선택적)
REDIS_URL=redis://localhost:6379/0

# 이메일 설정 (선택적)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
