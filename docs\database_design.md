# DeepTech Valley Platform 데이터베이스 설계서

## 1. 데이터베이스 개요

### 1.1 기본 정보
- **데이터베이스**: PostgreSQL 14+
- **ORM**: SQLAlchemy 2.0+
- **마이그레이션**: Alembic
- **연결 풀링**: SQLAlchemy Engine Pool
- **백업 전략**: 일일 자동 백업 + 주간 전체 백업

### 1.2 네이밍 컨벤션
- **테이블명**: snake_case (예: `user_profiles`, `company_applications`)
- **컬럼명**: snake_case (예: `created_at`, `user_type`)
- **인덱스명**: `idx_테이블명_컬럼명` (예: `idx_users_email`)
- **외래키명**: `fk_테이블명_참조테이블명` (예: `fk_applications_users`)
- **제약조건명**: `ck_테이블명_조건명` (예: `ck_users_email_format`)

## 2. 핵심 테이블 설계

### 2.1 사용자 관리 (User Management)

#### users 테이블
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('individual', 'company', 'institution', 'admin')),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT ck_users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### user_profiles 테이블
```sql
CREATE TABLE user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    organization VARCHAR(200),
    position VARCHAR(100),
    bio TEXT,
    profile_image_url VARCHAR(500),
    address JSONB,
    social_links JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_organization ON user_profiles(organization);
```

### 2.2 공지사항 관리 (Notice Management)

#### notices 테이블
```sql
CREATE TABLE notices (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    summary VARCHAR(500),
    author_id INTEGER NOT NULL REFERENCES users(id),
    category VARCHAR(50) DEFAULT 'general',
    priority INTEGER DEFAULT 0 CHECK (priority >= 0 AND priority <= 10),
    is_pinned BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT ck_notices_title_length CHECK (LENGTH(title) >= 5)
);

CREATE INDEX idx_notices_published_at ON notices(published_at DESC);
CREATE INDEX idx_notices_category ON notices(category);
CREATE INDEX idx_notices_is_pinned ON notices(is_pinned);
CREATE INDEX idx_notices_author_id ON notices(author_id);
```

#### notice_attachments 테이블
```sql
CREATE TABLE notice_attachments (
    id SERIAL PRIMARY KEY,
    notice_id INTEGER NOT NULL REFERENCES notices(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_notice_attachments_notice_id ON notice_attachments(notice_id);
```

### 2.3 수요관리 (Demand Management)

#### applications 테이블
```sql
CREATE TABLE applications (
    id SERIAL PRIMARY KEY,
    applicant_id INTEGER NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    application_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'withdrawn')),
    priority INTEGER DEFAULT 0,
    budget_requested DECIMAL(15,2),
    project_duration_months INTEGER,
    application_data JSONB NOT NULL,
    reviewer_id INTEGER REFERENCES users(id),
    review_notes TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT ck_applications_budget_positive CHECK (budget_requested IS NULL OR budget_requested > 0),
    CONSTRAINT ck_applications_duration_positive CHECK (project_duration_months IS NULL OR project_duration_months > 0)
);

CREATE INDEX idx_applications_applicant_id ON applications(applicant_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_submitted_at ON applications(submitted_at DESC);
CREATE INDEX idx_applications_reviewer_id ON applications(reviewer_id);
```

#### application_attachments 테이블
```sql
CREATE TABLE application_attachments (
    id SERIAL PRIMARY KEY,
    application_id INTEGER NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    attachment_type VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_application_attachments_application_id ON application_attachments(application_id);
CREATE INDEX idx_application_attachments_type ON application_attachments(attachment_type);
```

### 2.4 밸리관리 (Valley Management)

#### companies 테이블
```sql
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    business_registration_number VARCHAR(50) UNIQUE,
    company_type VARCHAR(50) NOT NULL,
    industry_sector VARCHAR(100),
    company_size VARCHAR(20) CHECK (company_size IN ('startup', 'small', 'medium', 'large')),
    founded_year INTEGER,
    website_url VARCHAR(500),
    description TEXT,
    address JSONB,
    contact_info JSONB,
    financial_info JSONB,
    technology_stack JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT ck_companies_founded_year CHECK (founded_year IS NULL OR (founded_year >= 1800 AND founded_year <= EXTRACT(YEAR FROM CURRENT_DATE)))
);

CREATE INDEX idx_companies_name ON companies(name);
CREATE INDEX idx_companies_company_type ON companies(company_type);
CREATE INDEX idx_companies_industry_sector ON companies(industry_sector);
CREATE INDEX idx_companies_company_size ON companies(company_size);
```

#### institutions 테이블
```sql
CREATE TABLE institutions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    institution_type VARCHAR(50) NOT NULL,
    parent_organization VARCHAR(200),
    established_year INTEGER,
    website_url VARCHAR(500),
    description TEXT,
    address JSONB,
    contact_info JSONB,
    research_areas JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_institutions_name ON institutions(name);
CREATE INDEX idx_institutions_type ON institutions(institution_type);
```

#### company_relationships 테이블
```sql
CREATE TABLE company_relationships (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    related_company_id INTEGER REFERENCES companies(id) ON DELETE CASCADE,
    institution_id INTEGER REFERENCES institutions(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL,
    relationship_strength INTEGER DEFAULT 1 CHECK (relationship_strength >= 1 AND relationship_strength <= 10),
    description TEXT,
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT ck_company_relationships_target CHECK (
        (related_company_id IS NOT NULL AND institution_id IS NULL) OR
        (related_company_id IS NULL AND institution_id IS NOT NULL)
    ),
    CONSTRAINT ck_company_relationships_not_self CHECK (company_id != related_company_id)
);

CREATE INDEX idx_company_relationships_company_id ON company_relationships(company_id);
CREATE INDEX idx_company_relationships_related_company_id ON company_relationships(related_company_id);
CREATE INDEX idx_company_relationships_institution_id ON company_relationships(institution_id);
CREATE INDEX idx_company_relationships_type ON company_relationships(relationship_type);
```

## 3. 시스템 테이블

### 3.1 감사 로그 (Audit Log)
```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INTEGER NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_record_id ON audit_logs(record_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);
```

### 3.2 파일 관리 (File Management)
```sql
CREATE TABLE file_uploads (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    uploader_id INTEGER NOT NULL REFERENCES users(id),
    upload_purpose VARCHAR(50),
    is_temporary BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT ck_file_uploads_size_positive CHECK (file_size > 0)
);

CREATE INDEX idx_file_uploads_uploader_id ON file_uploads(uploader_id);
CREATE INDEX idx_file_uploads_file_hash ON file_uploads(file_hash);
CREATE INDEX idx_file_uploads_expires_at ON file_uploads(expires_at);
```

## 4. 데이터베이스 관계도 (ERD)

### 4.1 핵심 엔티티 관계
```
users (1) ──── (1) user_profiles
  │
  ├── (1) ──── (N) notices (author)
  ├── (1) ──── (N) applications (applicant)
  ├── (1) ──── (N) applications (reviewer)
  ├── (1) ──── (N) audit_logs
  └── (1) ──── (N) file_uploads

notices (1) ──── (N) notice_attachments

applications (1) ──── (N) application_attachments

companies (1) ──── (N) company_relationships
companies (1) ──── (N) company_relationships (related)

institutions (1) ──── (N) company_relationships
```

### 4.2 주요 비즈니스 규칙
- 사용자는 하나의 프로필만 가질 수 있음
- 공지사항은 관리자만 작성 가능
- 신청서는 기업 사용자만 제출 가능
- 기관 사용자는 신청서 검토 권한 보유
- 회사 관계는 양방향 또는 단방향 가능

## 5. 인덱스 전략

### 5.1 성능 최적화 인덱스
```sql
-- 복합 인덱스 (자주 함께 조회되는 컬럼들)
CREATE INDEX idx_applications_status_submitted_at ON applications(status, submitted_at DESC);
CREATE INDEX idx_notices_published_pinned ON notices(is_published, is_pinned, published_at DESC);
CREATE INDEX idx_company_relationships_active_type ON company_relationships(is_active, relationship_type);

-- 부분 인덱스 (조건부 인덱스)
CREATE INDEX idx_applications_active_submitted ON applications(submitted_at DESC)
WHERE status IN ('submitted', 'under_review');

CREATE INDEX idx_notices_published_only ON notices(published_at DESC)
WHERE is_published = TRUE;

-- 함수 기반 인덱스
CREATE INDEX idx_users_email_lower ON users(LOWER(email));
CREATE INDEX idx_companies_name_lower ON companies(LOWER(name));
```

### 5.2 전문 검색 인덱스
```sql
-- PostgreSQL Full Text Search
ALTER TABLE notices ADD COLUMN search_vector tsvector;

CREATE INDEX idx_notices_search_vector ON notices USING gin(search_vector);

-- 검색 벡터 자동 업데이트 트리거
CREATE OR REPLACE FUNCTION update_notice_search_vector() RETURNS trigger AS $$
BEGIN
    NEW.search_vector := to_tsvector('korean', COALESCE(NEW.title, '') || ' ' || COALESCE(NEW.content, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trig_update_notice_search_vector
BEFORE INSERT OR UPDATE ON notices
FOR EACH ROW EXECUTE FUNCTION update_notice_search_vector();
```

## 6. 데이터 무결성 및 제약조건

### 6.1 체크 제약조건
```sql
-- 이메일 형식 검증
ALTER TABLE users ADD CONSTRAINT ck_users_email_format
CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- 전화번호 형식 검증
ALTER TABLE user_profiles ADD CONSTRAINT ck_user_profiles_phone_format
CHECK (phone IS NULL OR phone ~* '^\+?[0-9\-\s\(\)]{10,20}$');

-- 예산 범위 검증
ALTER TABLE applications ADD CONSTRAINT ck_applications_budget_range
CHECK (budget_requested IS NULL OR (budget_requested >= 1000000 AND budget_requested <= 10000000000));

-- 파일 크기 제한
ALTER TABLE file_uploads ADD CONSTRAINT ck_file_uploads_size_limit
CHECK (file_size <= 104857600); -- 100MB
```

### 6.2 트리거 함수
```sql
-- 자동 updated_at 업데이트
CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS trigger AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 모든 테이블에 updated_at 트리거 적용
CREATE TRIGGER trig_users_updated_at BEFORE UPDATE ON users
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trig_user_profiles_updated_at BEFORE UPDATE ON user_profiles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trig_notices_updated_at BEFORE UPDATE ON notices
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trig_applications_updated_at BEFORE UPDATE ON applications
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trig_companies_updated_at BEFORE UPDATE ON companies
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trig_institutions_updated_at BEFORE UPDATE ON institutions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 7. 데이터 시딩 및 초기 데이터

### 7.1 기본 관리자 계정
```sql
-- 기본 관리자 계정 생성
INSERT INTO users (email, hashed_password, user_type, is_active, is_verified) VALUES
('<EMAIL>', '$2b$12$hashed_password_here', 'admin', TRUE, TRUE);

INSERT INTO user_profiles (user_id, full_name, organization, position) VALUES
(1, '시스템 관리자', 'DeepTech Valley', '시스템 관리자');
```

### 7.2 기본 카테고리 및 설정
```sql
-- 공지사항 카테고리 (ENUM 대신 별도 테이블로 관리 가능)
CREATE TABLE notice_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    color_code VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE
);

INSERT INTO notice_categories (name, description, color_code) VALUES
('general', '일반 공지', '#6B7280'),
('urgent', '긴급 공지', '#EF4444'),
('event', '행사 안내', '#3B82F6'),
('system', '시스템 공지', '#8B5CF6'),
('maintenance', '점검 안내', '#F59E0B');
```

## 8. 백업 및 복구 전략

### 8.1 백업 스크립트
```bash
#!/bin/bash
# daily_backup.sh

DB_NAME="deeptech_valley"
BACKUP_DIR="/var/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)

# 전체 데이터베이스 백업
pg_dump -h localhost -U postgres -d $DB_NAME -f "$BACKUP_DIR/full_backup_$DATE.sql"

# 스키마만 백업
pg_dump -h localhost -U postgres -d $DB_NAME --schema-only -f "$BACKUP_DIR/schema_backup_$DATE.sql"

# 데이터만 백업
pg_dump -h localhost -U postgres -d $DB_NAME --data-only -f "$BACKUP_DIR/data_backup_$DATE.sql"

# 7일 이상 된 백업 파일 삭제
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
```

### 8.2 복구 절차
```bash
# 전체 복구
psql -h localhost -U postgres -d deeptech_valley < full_backup_20250626_120000.sql

# 스키마만 복구
psql -h localhost -U postgres -d deeptech_valley < schema_backup_20250626_120000.sql

# 특정 테이블만 복구
pg_restore -h localhost -U postgres -d deeptech_valley -t users full_backup_20250626_120000.sql
```

## 9. 모니터링 및 성능 튜닝

### 9.1 성능 모니터링 쿼리
```sql
-- 느린 쿼리 확인
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 테이블별 사용량 통계
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
FROM pg_stat_user_tables
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;

-- 인덱스 사용률 확인
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### 9.2 정기 유지보수
```sql
-- 통계 정보 업데이트
ANALYZE;

-- 테이블 정리 (주간 실행)
VACUUM ANALYZE;

-- 인덱스 재구성 (월간 실행)
REINDEX DATABASE deeptech_valley;
```

## 10. 마이그레이션 가이드

### 10.1 Alembic 설정
```python
# alembic/env.py
from sqlalchemy import engine_from_config, pool
from alembic import context
from app.core.database import Base
from app.models import *  # 모든 모델 import

target_metadata = Base.metadata
```

### 10.2 마이그레이션 명령어
```bash
# 새 마이그레이션 생성
alembic revision --autogenerate -m "Add user profiles table"

# 마이그레이션 적용
alembic upgrade head

# 마이그레이션 롤백
alembic downgrade -1

# 현재 상태 확인
alembic current

# 마이그레이션 히스토리 확인
alembic history
```
