# AI Coding Standards

## 🎯 목적
AI가 GitHub 이슈를 받아서 웹 개발을 수행할 때 지켜야 할 코딩 표준과 품질 기준을 정의합니다. 일관성 있고 고품질의 코드 생성을 보장합니다.

## 📋 기본 원칙

### 코딩 철학
- **읽기 쉬운 코드**: 다른 개발자(AI 포함)가 쉽게 이해할 수 있도록
- **일관성 우선**: 기존 코드 스타일과 패턴 유지
- **단순함 추구**: 복잡한 로직보다는 명확하고 단순한 구현
- **테스트 가능**: 단위 테스트 작성이 용이한 구조

### 작업 접근법
1. **이슈 내용 정확히 파악**
2. **기존 코드베이스 패턴 분석**
3. **점진적 구현** (한 번에 모든 것 X)
4. **테스트와 함께 개발**
5. **완료 기준 체크리스트 확인**

## 🌐 웹 프론트엔드 표준 (React)

### 파일 및 폴더 구조
```
src/
├── components/           # 재사용 가능한 컴포넌트
│   ├── common/          # 공통 컴포넌트 (Button, Input 등)
│   ├── layout/          # 레이아웃 컴포넌트 (Header, Footer 등)
│   └── features/        # 기능별 컴포넌트
├── pages/               # 페이지 컴포넌트
├── hooks/               # 커스텀 훅
├── services/            # API 호출 및 외부 서비스
├── utils/               # 유틸리티 함수
├── constants/           # 상수 정의
├── types/               # TypeScript 타입 정의
└── styles/              # 스타일 관련 파일
```

### 컴포넌트 작성 규칙
```jsx
// ✅ 좋은 예시
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/common';
import { userService } from '@/services';
import type { User } from '@/types';

interface UserProfileProps {
  userId: string;
  onUpdate?: (user: User) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ 
  userId, 
  onUpdate 
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadUser();
  }, [userId]);

  const loadUser = async () => {
    try {
      setLoading(true);
      setError(null);
      const userData = await userService.getUser(userId);
      setUser(userData);
    } catch (err) {
      setError('사용자 정보를 불러올 수 없습니다.');
      console.error('User loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async (updates: Partial<User>) => {
    try {
      const updatedUser = await userService.updateUser(userId, updates);
      setUser(updatedUser);
      onUpdate?.(updatedUser);
    } catch (err) {
      setError('사용자 정보 업데이트에 실패했습니다.');
    }
  };

  if (loading) return <div>로딩 중...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!user) return <div>사용자를 찾을 수 없습니다.</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>{user.email}</p>
      <Button 
        onClick={() => handleUpdate({ lastLoginAt: new Date() })}
      >
        로그인 시간 업데이트
      </Button>
    </div>
  );
};
```

### 네이밍 컨벤션
```typescript
// 컴포넌트: PascalCase
const UserProfile = () => {};
const ProductCard = () => {};

// 함수/변수: camelCase  
const getUserData = () => {};
const isLoading = true;
const userCount = 10;

// 상수: UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// 타입/인터페이스: PascalCase
interface User {
  id: string;
  name: string;
}

type ApiResponse<T> = {
  data: T;
  message: string;
};

// 파일명: kebab-case
// user-profile.tsx
// api-service.ts
// auth-utils.ts
```

### 상태 관리 패턴
```typescript
// ✅ 로컬 상태 (useState)
const [formData, setFormData] = useState({
  name: '',
  email: '',
});

// ✅ 복잡한 상태 (useReducer)
const [state, dispatch] = useReducer(userReducer, initialState);

// ✅ 전역 상태 (Context API)
const UserContext = createContext<{
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
} | null>(null);

// ✅ 서버 상태 (React Query 스타일)
const useUser = (userId: string) => {
  return useQuery(['user', userId], () => userService.getUser(userId));
};
```

## 🔧 웹 백엔드 표준 (Node.js/Express)

### 프로젝트 구조
```
src/
├── controllers/         # 라우트 핸들러
├── services/           # 비즈니스 로직
├── models/             # 데이터 모델
├── middleware/         # 미들웨어
├── routes/             # 라우트 정의
├── utils/              # 유틸리티 함수
├── config/             # 설정 파일
├── types/              # TypeScript 타입
└── tests/              # 테스트 파일
```

### API 컨트롤러 작성 규칙
```typescript
// ✅ 좋은 예시
import { Request, Response, NextFunction } from 'express';
import { userService } from '@/services';
import { CreateUserDto, UpdateUserDto } from '@/types';
import { validateRequest } from '@/middleware';

export const userController = {
  // GET /api/users/:id
  async getUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const user = await userService.getUserById(id);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '사용자를 찾을 수 없습니다.'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      next(error);
    }
  },

  // POST /api/users
  async createUser(req: Request, res: Response, next: NextFunction) {
    try {
      const userData: CreateUserDto = req.body;
      const newUser = await userService.createUser(userData);
      
      res.status(201).json({
        success: true,
        data: newUser,
        message: '사용자가 성공적으로 생성되었습니다.'
      });
    } catch (error) {
      next(error);
    }
  },

  // PUT /api/users/:id
  async updateUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const updates: UpdateUserDto = req.body;
      
      const updatedUser = await userService.updateUser(id, updates);
      
      res.json({
        success: true,
        data: updatedUser,
        message: '사용자 정보가 업데이트되었습니다.'
      });
    } catch (error) {
      next(error);
    }
  }
};
```

### 서비스 레이어 패턴
```typescript
// ✅ 비즈니스 로직 분리
import { User } from '@/models';
import { CreateUserDto, UpdateUserDto } from '@/types';
import { hashPassword, validateEmail } from '@/utils';

export const userService = {
  async getUserById(id: string): Promise<User | null> {
    const user = await User.findById(id);
    return user;
  },

  async createUser(userData: CreateUserDto): Promise<User> {
    // 비즈니스 로직
    if (!validateEmail(userData.email)) {
      throw new Error('유효하지 않은 이메일 형식입니다.');
    }

    // 중복 검사
    const existingUser = await User.findByEmail(userData.email);
    if (existingUser) {
      throw new Error('이미 존재하는 이메일입니다.');
    }

    // 비밀번호 해싱
    const hashedPassword = await hashPassword(userData.password);

    // 사용자 생성
    const newUser = await User.create({
      ...userData,
      password: hashedPassword
    });

    return newUser;
  },

  async updateUser(id: string, updates: UpdateUserDto): Promise<User> {
    const user = await User.findById(id);
    if (!user) {
      throw new Error('사용자를 찾을 수 없습니다.');
    }

    // 비밀번호 업데이트시 해싱
    if (updates.password) {
      updates.password = await hashPassword(updates.password);
    }

    const updatedUser = await User.update(id, updates);
    return updatedUser;
  }
};
```

### API 응답 형식 표준
```typescript
// ✅ 성공 응답
{
  success: true,
  data: {...}, // 또는 [...] 
  message?: "optional success message"
}

// ✅ 에러 응답
{
  success: false,
  error: {
    code: "USER_NOT_FOUND",
    message: "사용자를 찾을 수 없습니다.",
    details?: {...} // 추가 에러 정보
  }
}

// ✅ 페이지네이션 응답
{
  success: true,
  data: [...],
  pagination: {
    page: 1,
    limit: 10,
    total: 150,
    totalPages: 15
  }
}
```

## 🔌 인터페이스 정의 표준

### API 인터페이스 정의
```typescript
// ✅ API 요청/응답 타입 정의
interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  role?: 'user' | 'admin';
}

interface UpdateUserRequest {
  name?: string;
  email?: string;
  password?: string;
  isActive?: boolean;
}

interface UserResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 컴포넌트 인터페이스 정의
```typescript
// ✅ React 컴포넌트 Props 타입
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
  isEditable?: boolean;
  showActions?: boolean;
}

interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'number';
  value: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  placeholder?: string;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
}
```

### 서비스 레이어 인터페이스
```typescript
// ✅ 서비스 인터페이스 정의
interface UserService {
  getUser(id: string): Promise<User>;
  getUsers(params: GetUsersParams): Promise<PaginatedResponse<User>>;
  createUser(data: CreateUserRequest): Promise<User>;
  updateUser(id: string, data: UpdateUserRequest): Promise<User>;
  deleteUser(id: string): Promise<void>;
}

interface AuthService {
  login(email: string, password: string): Promise<LoginResponse>;
  logout(): Promise<void>;
  refreshToken(): Promise<TokenResponse>;
  getCurrentUser(): Promise<User>;
}

interface GetUsersParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  isActive?: boolean;
}
```

### 커스텀 훅 인터페이스
```typescript
// ✅ 커스텀 훅 타입 정의
interface UseUserReturn {
  user: User | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseFormReturn<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  handleChange: (field: keyof T, value: any) => void;
  handleSubmit: (onSubmit: (values: T) => void) => (e: React.FormEvent) => void;
  reset: () => void;
  isValid: boolean;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  execute: () => Promise<void>;
}
```

### 공통 타입 정의
```typescript
// ✅ 도메인 모델 타입
interface User {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  imageUrl: string;
  sellerId: string;
  status: 'active' | 'sold' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

// ✅ 유틸리티 타입
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
type CreateInput<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;
type UpdateInput<T> = Partial<CreateInput<T>>;

// 사용 예시
type CreateUserInput = CreateInput<User>; // id, createdAt, updatedAt 제외
type UpdateUserInput = UpdateInput<User>; // 모든 필드 optional
```

### 이벤트 핸들러 타입
```typescript
// ✅ 이벤트 핸들러 타입 정의
interface UserEventHandlers {
  onUserCreate: (user: User) => void;
  onUserUpdate: (user: User) => void;
  onUserDelete: (userId: string) => void;
  onUserSelect: (user: User) => void;
}

interface FormEventHandlers<T> {
  onSubmit: (data: T) => void | Promise<void>;
  onChange: (field: keyof T, value: any) => void;
  onReset: () => void;
  onValidate?: (data: T) => Record<string, string>;
}
```

### 환경설정 타입
```typescript
// ✅ 환경설정 인터페이스
interface AppConfig {
  apiBaseUrl: string;
  jwtSecret: string;
  databaseUrl: string;
  redisUrl?: string;
  uploadMaxSize: number;
  corsOrigins: string[];
}

interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}
```

## 🗄️ 데이터베이스 작업 표준

### 모델 정의 (TypeScript + ORM)
```typescript
// ✅ 사용자 모델 예시
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  name: string;

  @Column()
  password: string;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 비즈니스 메서드
  static async findByEmail(email: string): Promise<User | null> {
    return await this.findOne({ where: { email } });
  }

  static async createUser(userData: CreateUserDto): Promise<User> {
    const user = this.create(userData);
    return await this.save(user);
  }
}
```

### 쿼리 작성 규칙
```typescript
// ✅ Repository 패턴 사용
export const userRepository = {
  async findById(id: string): Promise<User | null> {
    return await User.findOne({ 
      where: { id },
      select: ['id', 'email', 'name', 'isActive', 'createdAt'] // 비밀번호 제외
    });
  },

  async findByEmailWithPassword(email: string): Promise<User | null> {
    return await User.findOne({ 
      where: { email },
      select: ['id', 'email', 'name', 'password', 'isActive'] // 로그인용
    });
  },

  async updateLastLogin(id: string): Promise<void> {
    await User.update(id, { 
      lastLoginAt: new Date() 
    });
  }
};
```

## 🔄 코드 재사용 및 일관성 유지

### 개발 시작 전 필수 확인사항
AI가 새로운 기능을 개발하기 전에 반드시 다음을 확인해야 합니다:

#### 1. 기존 컴포넌트 확인
```bash
# 다음 폴더들을 반드시 확인
src/components/common/     # 재사용 가능한 공통 컴포넌트
src/components/layout/     # 레이아웃 컴포넌트
src/components/features/   # 기능별 컴포넌트
```

**확인 방법**:
- 비슷한 이름이나 기능의 컴포넌트가 있는지 검색
- Button, Input, Modal, Card 등 기본 컴포넌트 재사용
- 새로 만들기 전에 기존 컴포넌트 확장 가능성 검토

#### 2. 유틸 함수 확인
```bash
# 유틸리티 함수 확인 위치
src/utils/               # 공통 유틸리티 함수
src/hooks/               # 커스텀 훅
src/constants/           # 상수 정의
```

**확인 방법**:
- 날짜, 문자열, 배열 처리 등 공통 로직 확인
- API 호출, 데이터 변환 관련 기존 함수 확인
- 같은 기능의 함수를 중복으로 만들지 않기

#### 3. API 서비스 확인
```bash
# API 관련 코드 확인
src/services/            # API 호출 서비스
src/types/               # API 타입 정의
```

**확인 방법**:
- 이미 구현된 API 엔드포인트 확인
- 비슷한 CRUD 패턴이 있는지 확인
- API 응답 타입이 이미 정의되어 있는지 확인

#### 4. 관련 이슈 및 PR 확인
- **다른 이슈들의 구현 방식** 검토
- **최근 머지된 PR**에서 비슷한 패턴 확인
- **같은 기능 영역**의 다른 작업들과 일관성 유지

### 중복 방지 규칙

#### ✅ 재사용해야 하는 경우
```typescript
// ✅ 기존 공통 컴포넌트 재사용
import { Button, Input, Modal } from '@/components/common';

// ✅ 기존 유틸 함수 재사용  
import { formatDate, validateEmail } from '@/utils';

// ✅ 기존 API 서비스 재사용
import { userService } from '@/services';
```

#### ❌ 새로 만들면 안 되는 경우
```typescript
// ❌ 이미 있는 컴포넌트 중복 생성
const MyButton = () => <button>...</button>; // Button 컴포넌트 이미 있음

// ❌ 이미 있는 유틸 함수 중복 생성
const formatUserDate = (date) => {...}; // formatDate 이미 있음

// ❌ 이미 있는 API 함수 중복 생성
const getUserData = (id) => {...}; // userService.getUser 이미 있음
```

### 일관성 유지 패턴

#### 1. 네이밍 패턴 유지
```typescript
// ✅ 기존 패턴 따르기
// 기존: UserCard, ProductCard
const BookCard = () => {}; // 일관성 있음

// ❌ 다른 패턴 사용
const CardForBook = () => {}; // 일관성 없음
```

#### 2. 폴더 구조 패턴 유지
```bash
# ✅ 기존 구조 따르기
src/components/features/user/UserProfile.tsx
src/components/features/product/ProductList.tsx
src/components/features/book/BookDetail.tsx  # 일관성 있음
```

#### 3. API 패턴 유지
```typescript
// ✅ 기존 서비스 패턴 따르기
// 기존: userService.getUser, userService.createUser
export const bookService = {
  getBook: (id: string) => api.get(`/books/${id}`),
  createBook: (data: CreateBookDto) => api.post('/books', data)
}; // 일관성 있음
```

### 공통 모듈화 규칙

#### 3회 반복 규칙
같은 패턴이 **3번 이상 반복**되면 공통 모듈로 분리:

```typescript
// ❌ 반복되는 패턴
const UserCard = ({ user }) => (
  <div className="card">
    <h3>{user.name}</h3>
    <p>{user.email}</p>
  </div>
);

const ProductCard = ({ product }) => (
  <div className="card">
    <h3>{product.name}</h3>
    <p>{product.description}</p>
  </div>
);

// ✅ 공통 컴포넌트로 분리
const Card = ({ title, subtitle, children }) => (
  <div className="card">
    <h3>{title}</h3>
    <p>{subtitle}</p>
    {children}
  </div>
);
```

## 🧪 테스트 작성 표준 (확장)

### 코드 커버리지 요구사항

#### 커버리지 기준
```markdown
- **단위 테스트**: 최소 80% 이상 (필수)
- **통합 테스트**: 핵심 API 플로우 100%
- **E2E 테스트**: 주요 사용자 시나리오 커버
- **중요 함수**: 개별적으로 100% 커버리지
```

#### 우선순위별 커버리지
```markdown
**High Priority (100% 필수)**:
- 인증/인가 관련 함수
- 결제/트랜잭션 로직
- 데이터 검증 함수
- 보안 관련 함수

**Medium Priority (80% 이상)**:
- 비즈니스 로직 함수
- API 컨트롤러
- 데이터 변환 함수

**Low Priority (60% 이상)**:
- UI 컴포넌트
- 유틸리티 함수
- 설정 관련 함수
```

### 테스트 품질 기준

#### 단위 테스트 품질 체크리스트
- [ ] **테스트 이름이 명확한가?** (무엇을 테스트하는지 명시)
- [ ] **Given-When-Then 패턴** 사용
- [ ] **모킹이 적절한가?** (외부 의존성 제거)
- [ ] **경계값 테스트** 포함 (min, max, edge case)
- [ ] **에러 케이스 테스트** 포함

```typescript
// ✅ 좋은 테스트 예시
describe('userService.createUser', () => {
  it('유효한 사용자 데이터로 사용자를 생성한다', async () => {
    // Given
    const userData = {
      name: '홍길동',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    // When
    const result = await userService.createUser(userData);
    
    // Then
    expect(result.id).toBeDefined();
    expect(result.email).toBe(userData.email);
    expect(result.password).toBeUndefined(); // 응답에 비밀번호 없음
  });

  it('중복된 이메일로 생성 시 에러를 발생시킨다', async () => {
    // Given
    const existingEmail = '<EMAIL>';
    await userService.createUser({
      name: '기존 사용자',
      email: existingEmail,
      password: 'password'
    });

    // When & Then
    await expect(userService.createUser({
      name: '새 사용자',
      email: existingEmail,
      password: 'password'
    })).rejects.toThrow('이미 존재하는 이메일입니다');
  });
});
```

#### 통합 테스트 품질 기준
- [ ] **실제 데이터베이스 사용** (테스트 DB)
- [ ] **전체 요청-응답 사이클** 테스트
- [ ] **인증/인가 흐름** 포함
- [ ] **에러 상황 시나리오** 포함

#### E2E 테스트 품질 기준
- [ ] **실제 사용자 시나리오** 기반
- [ ] **주요 비즈니스 플로우** 커버
- [ ] **브라우저 호환성** 고려
- [ ] **성능 기준** 확인 (로딩 시간 등)

### 커버리지 측정 방법

#### Jest 커버리지 설정
```json
// package.json
{
  "jest": {
    "collectCoverage": true,
    "coverageDirectory": "coverage",
    "coverageReporters": ["text", "lcov", "html"],
    "coverageThreshold": {
      "global": {
        "branches": 80,
        "functions": 80,
        "lines": 80,
        "statements": 80
      }
    }
  }
}
```

#### 커버리지 확인 명령어
```bash
# 커버리지 측정 실행
npm run test:coverage

# 특정 파일/폴더만 커버리지 확인
npm run test:coverage -- src/services/

# 커버리지 리포트 확인
open coverage/lcov-report/index.html
```

### 품질 게이트 기준

#### CI/CD 통과 조건
- [ ] **모든 테스트 통과** (단위 + 통합)
- [ ] **커버리지 기준 충족** (80% 이상)
- [ ] **ESLint/Prettier 통과**
- [ ] **TypeScript 컴파일 성공**
- [ ] **보안 스캔 통과** (npm audit)

#### PR 머지 조건
- [ ] **테스트 커버리지 감소 없음**
- [ ] **새로운 기능에 대한 테스트 추가**
- [ ] **기존 테스트 깨짐 없음**
- [ ] **성능 저하 없음** (중요 API 응답시간)

## 🧪 테스트 작성 표준

### 단위 테스트 (Jest + React Testing Library)
```typescript
// ✅ 컴포넌트 테스트
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UserProfile } from '@/components/UserProfile';
import { userService } from '@/services';

// 모킹
jest.mock('@/services', () => ({
  userService: {
    getUser: jest.fn(),
    updateUser: jest.fn()
  }
}));

describe('UserProfile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('사용자 정보를 올바르게 표시한다', async () => {
    const mockUser = {
      id: '1',
      name: '홍길동',
      email: '<EMAIL>'
    };

    (userService.getUser as jest.Mock).mockResolvedValue(mockUser);

    render(<UserProfile userId="1" />);

    await waitFor(() => {
      expect(screen.getByText('홍길동')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('로딩 상태를 표시한다', () => {
    (userService.getUser as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // 무한 대기
    );

    render(<UserProfile userId="1" />);
    expect(screen.getByText('로딩 중...')).toBeInTheDocument();
  });

  it('에러 상황을 처리한다', async () => {
    (userService.getUser as jest.Mock).mockRejectedValue(
      new Error('Network error')
    );

    render(<UserProfile userId="1" />);

    await waitFor(() => {
      expect(screen.getByText('사용자 정보를 불러올 수 없습니다.')).toBeInTheDocument();
    });
  });
});
```

### API 테스트
```typescript
// ✅ API 엔드포인트 테스트
import request from 'supertest';
import app from '@/app';
import { User } from '@/models';

describe('POST /api/users', () => {
  it('유효한 데이터로 사용자를 생성한다', async () => {
    const userData = {
      name: '홍길동',
      email: '<EMAIL>',
      password: 'password123'
    };

    const response = await request(app)
      .post('/api/users')
      .send(userData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.email).toBe(userData.email);
    expect(response.body.data.password).toBeUndefined(); // 비밀번호는 응답에 포함 안됨
  });

  it('중복된 이메일로 생성 시 에러를 반환한다', async () => {
    // 기존 사용자 생성
    await User.create({
      name: '기존 사용자',
      email: '<EMAIL>',
      password: 'hashedpassword'
    });

    const userData = {
      name: '새 사용자',
      email: '<EMAIL>', // 중복 이메일
      password: 'password123'
    };

    const response = await request(app)
      .post('/api/users')
      .send(userData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.message).toBe('이미 존재하는 이메일입니다.');
  });
});
```

## ✅ 완료 기준 체크리스트

### 기능적 완료 기준
- [ ] **요구사항 구현**: 이슈에 명시된 모든 기능이 구현됨
- [ ] **정상 케이스 동작**: 예상되는 입력에 대해 올바른 결과 출력
- [ ] **에러 케이스 처리**: 잘못된 입력, 네트워크 오류 등 적절히 처리
- [ ] **에지 케이스 처리**: 빈 값, null, undefined, 경계값 등 처리
- [ ] **사용자 경험**: 로딩 상태, 에러 메시지, 성공 피드백 제공

### 기술적 완료 기준
- [ ] **코딩 표준 준수**: ESLint, Prettier 규칙 통과
- [ ] **타입 안정성**: TypeScript 에러 없음
- [ ] **성능 최적화**: 불필요한 리렌더링, API 호출 최소화
- [ ] **보안 고려**: XSS, CSRF, SQL Injection 등 방지
- [ ] **접근성**: 기본적인 웹 접근성 지침 준수

### 테스트 완료 기준
- [ ] **단위 테스트**: 핵심 로직에 대한 테스트 작성
- [ ] **통합 테스트**: API 엔드포인트 테스트 (백엔드)
- [ ] **컴포넌트 테스트**: 사용자 상호작용 테스트 (프론트엔드)
- [ ] **테스트 커버리지**: 최소 80% 이상
- [ ] **테스트 실행**: 모든 테스트 통과

### 문서화 완료 기준
- [ ] **코드 주석**: 복잡한 로직에 대한 설명 주석
- [ ] **API 문서**: 새로운 API 엔드포인트 문서화
- [ ] **컴포넌트 문서**: props, 사용법 등 문서화
- [ ] **README 업데이트**: 새로운 기능이나 설정 방법 추가

### 통합 완료 기준
- [ ] **충돌 없음**: 기존 코드와 충돌 없이 병합 가능
- [ ] **의존성 검토**: 새로운 라이브러리 사용시 타당성 검토
- [ ] **환경 변수**: 필요한 환경 변수 문서화 및 예시 제공
- [ ] **마이그레이션**: 데이터베이스 변경시 마이그레이션 스크립트 제공

## 🚀 PR 생성 가이드

### PR 제목 형식
```
[타입] 이슈 제목 (#이슈번호)

예시:
[FEAT] 사용자 로그인 기능 구현 (#1)
[API] 사용자 CRUD API 개발 (#2)
[FIX] 로그인 에러 처리 개선 (#3)
```

### PR 설명 템플릿
```markdown
## 🎯 이슈 링크
Closes #[이슈번호]

## 📝 구현 내용
- [구현한 주요 기능 1]
- [구현한 주요 기능 2]
- [구현한 주요 기능 3]

## 🧪 테스트
- [ ] 단위 테스트 추가/업데이트
- [ ] 기존 테스트 모두 통과
- [ ] 수동 테스트 완료

## 📋 체크리스트
- [ ] 코딩 표준 준수 (ESLint/Prettier)
- [ ] TypeScript 에러 없음
- [ ] 모든 완료 기준 충족
- [ ] 문서 업데이트 완료

## 🔄 변경사항
- 새로운 파일: [파일 목록]
- 수정된 파일: [파일 목록]
- 삭제된 파일: [파일 목록]

## 📸 스크린샷 (UI 변경시)
[스크린샷 또는 GIF]
```

## 💡 AI 개발 시 주의사항

### 할 일 ✅
- **이슈 내용을 정확히 파악**하고 요구사항 재확인
- **기존 코드 스타일과 패턴 분석** 후 일관성 유지
- **작은 단위로 구현**하고 중간중간 검증
- **에러 처리와 예외 상황** 충분히 고려
- **테스트 코드를 함께 작성**하여 품질 보장

### 하지 말 것 ❌
- **요구사항을 임의로 확장**하거나 추가 기능 구현
- **기존 코드를 불필요하게 수정**하거나 리팩토링
- **테스트 없이 코드만** 작성
- **에러 처리 생략**하거나 console.log만으로 처리
- **하드코딩된 값** 사용 (상수로 분리 필요)

## 🔄 코드 통합 및 연관 파일 업데이트 (필수)

### 🚨 새 기능 개발 시 필수 업데이트 항목

#### **프론트엔드 기능 추가 시**
- [ ] **라우팅 업데이트**: `src/App.js` 또는 라우터 파일에 새 경로 추가
- [ ] **네비게이션 업데이트**: Sidebar, Header, 메뉴 컴포넌트에 링크 추가
- [ ] **상위 컴포넌트 수정**: 새 컴포넌트를 import하고 사용하는 곳 업데이트
- [ ] **타입 정의 업데이트**: TypeScript 인터페이스, PropTypes 추가
- [ ] **상태 관리 업데이트**: Redux, Context 등 글로벌 상태에 새 상태 추가

#### **백엔드 API 추가 시**
- [ ] **라우터 등록**: `routes/index.js`에 새 라우터 연결
- [ ] **미들웨어 적용**: 인증, 로깅 등 필요한 미들웨어 추가
- [ ] **에러 핸들러**: 글로벌 에러 핸들러에 새 에러 타입 추가
- [ ] **환경변수**: `.env.example`에 새 환경변수 추가

#### **데이터베이스 변경 시**
- [ ] **마이그레이션**: 스키마 변경 마이그레이션 파일 생성
- [ ] **시드 데이터**: 테스트용 초기 데이터 업데이트
- [ ] **백업 로직**: 기존 데이터 마이그레이션 계획

#### **의존성 추가 시**
- [ ] **package.json**: 새 패키지 의존성 추가
- [ ] **package-lock.json**: 버전 고정
- [ ] **Docker**: Dockerfile이나 docker-compose.yml 업데이트

### 🔍 기존 코드 분석 필수 체크리스트

#### **새 기능 개발 전 반드시 확인**
- [ ] **기존 컴포넌트 재사용**: 비슷한 기능이 이미 있는지 확인
- [ ] **네이밍 컨벤션**: 기존 파일/함수 이름 패턴 따르기
- [ ] **폴더 구조**: 기존 프로젝트 구조에 맞게 파일 배치
- [ ] **코딩 스타일**: 기존 코드와 일관된 스타일 유지

#### **통합 시점 체크리스트**
- [ ] **라우팅 테스트**: 새 페이지에 실제로 접근 가능한지 확인
- [ ] **네비게이션 테스트**: 메뉴에서 새 기능으로 이동 가능한지 확인
- [ ] **상태 연동 테스트**: 글로벌 상태와 새 기능이 잘 연동되는지 확인
- [ ] **API 연동 테스트**: 프론트엔드에서 새 API 호출 가능한지 확인

### ⚠️ 통합 실패 시 이슈 재오픈
새 기능을 만들었지만 다른 부분과 연결되지 않으면:
1. 이슈를 다시 열기
2. "Integration Failed" 라벨 추가  
3. 누락된 통합 작업 체크리스트 작성
4. 통합 완료 후 이슈 닫기