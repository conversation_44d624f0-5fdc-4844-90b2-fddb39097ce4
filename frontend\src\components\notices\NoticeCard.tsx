import React from 'react';
import type { Notice } from '@/types/notice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, FileText, Pin, Calendar, User } from 'lucide-react';
import { format } from 'date-fns';
import { ko } from 'date-fns/locale';
import { cn } from '@/lib/utils';

interface NoticeCardProps {
  notice: Notice;
  onClick?: () => void;
  showActions?: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
}

const categoryConfig = {
  general: { label: '일반', color: 'bg-gray-100 text-gray-800' },
  important: { label: '중요', color: 'bg-blue-100 text-blue-800' },
  urgent: { label: '긴급', color: 'bg-red-100 text-red-800' },
  event: { label: '행사', color: 'bg-green-100 text-green-800' },
};

const statusConfig = {
  published: { label: '게시됨', color: 'bg-green-100 text-green-800' },
  draft: { label: '임시저장', color: 'bg-yellow-100 text-yellow-800' },
  archived: { label: '보관됨', color: 'bg-gray-100 text-gray-800' },
};

export const NoticeCard: React.FC<NoticeCardProps> = ({
  notice,
  onClick,
  showActions = false,
  onEdit,
  onDelete,
}) => {
  const categoryInfo = categoryConfig[notice.category];
  const statusInfo = statusConfig[notice.status];

  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all hover:shadow-md",
        notice.isPinned && "border-etri-blue bg-blue-50/30",
        onClick && "hover:bg-gray-50"
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {notice.isPinned && (
                <Pin className="h-4 w-4 text-etri-blue" />
              )}
              <Badge className={categoryInfo.color}>
                {categoryInfo.label}
              </Badge>
              <Badge className={statusInfo.color}>
                {statusInfo.label}
              </Badge>
              {notice.isImportant && (
                <Badge className="bg-red-100 text-red-800">
                  중요
                </Badge>
              )}
            </div>
            <CardTitle className={cn(
              "text-lg leading-tight",
              notice.isPinned && "text-etri-blue"
            )}>
              {notice.title}
            </CardTitle>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {notice.summary && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {notice.summary}
          </p>
        )}

        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              <span>{notice.author.name}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>
                {format(notice.createdAt, 'yyyy.MM.dd', { locale: ko })}
              </span>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {notice.files.length > 0 && (
              <div className="flex items-center gap-1">
                <FileText className="h-4 w-4" />
                <span>{notice.files.length}</span>
              </div>
            )}
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{notice.viewCount.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {notice.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {notice.tags.map((tag) => (
              <Badge 
                key={tag} 
                variant="outline" 
                className="text-xs"
              >
                #{tag}
              </Badge>
            ))}
          </div>
        )}

        {showActions && (
          <div className="flex gap-2 mt-4 pt-3 border-t">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={(e) => {
                e.stopPropagation();
                onEdit?.();
              }}
            >
              편집
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={(e) => {
                e.stopPropagation();
                onDelete?.();
              }}
            >
              삭제
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
