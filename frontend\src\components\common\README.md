# 범용 사이드바 컴포넌트 (Sidebar)

재사용 가능한 사이드바 컴포넌트로, 다양한 페이지에서 일관된 네비게이션 UI를 제공합니다.

## 🎯 주요 특징

- **완전 재사용 가능**: 메뉴 구성만 변경하면 어떤 페이지에서든 사용 가능
- **자동 경로 감지**: 현재 URL에 따라 자동으로 메뉴 선택 상태 동기화
- **서브메뉴 지원**: 2단계 메뉴 구조 지원 (메인 메뉴 + 서브 메뉴)
- **부드러운 애니메이션**: 서브메뉴 펼침/접힘 애니메이션
- **반응형 디자인**: 다양한 너비 옵션 제공
- **TypeScript 완전 지원**: 타입 안전성 보장

## 📦 설치 및 사용법

### 1. 기본 사용법

```tsx
import { Sidebar, SidebarMenuItem } from '@/components/common/Sidebar';
import { Home, Settings, Users } from 'lucide-react';

const MyPage: React.FC = () => {
  const menuItems: SidebarMenuItem[] = [
    {
      id: 'home',
      name: '홈',
      icon: Home,
      path: '/home'
    },
    {
      id: 'users',
      name: '사용자',
      icon: Users,
      path: '/users',
      subItems: [
        { id: 'user-list', name: '사용자 목록', path: '/users/list' },
        { id: 'user-roles', name: '권한 관리', path: '/users/roles' }
      ]
    },
    {
      id: 'settings',
      name: '설정',
      icon: Settings,
      path: '/settings'
    }
  ];

  return (
    <div className="flex">
      <Sidebar
        title="관리자"
        menuItems={menuItems}
        width="w-80"
      />
      <main className="flex-1">
        {/* 메인 콘텐츠 */}
      </main>
    </div>
  );
};
```

### 2. 서브메뉴가 있는 복잡한 구조

```tsx
const complexMenuItems: SidebarMenuItem[] = [
  {
    id: 'dashboard',
    name: '대시보드',
    icon: BarChart3,
    path: '/admin/dashboard'
  },
  {
    id: 'content',
    name: '콘텐츠 관리',
    icon: FileText,
    path: '/admin/content',
    subItems: [
      { id: 'posts', name: '게시글 관리', path: '/admin/content/posts' },
      { id: 'pages', name: '페이지 관리', path: '/admin/content/pages' },
      { id: 'media', name: '미디어 관리', path: '/admin/content/media' }
    ]
  }
];
```

## 🔧 Props 설명

### Sidebar Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | 사이드바 상단에 표시될 제목 |
| `menuItems` | `SidebarMenuItem[]` | - | 메뉴 아이템 배열 |
| `className` | `string` | `''` | 추가 CSS 클래스 |
| `width` | `'w-64' \| 'w-72' \| 'w-80' \| 'w-96'` | `'w-80'` | 사이드바 너비 |

### SidebarMenuItem 타입

```tsx
interface SidebarMenuItem {
  id: string;           // 고유 식별자
  name: string;         // 메뉴에 표시될 이름
  icon: LucideIcon;     // Lucide React 아이콘
  path: string;         // 라우터 경로
  subItems?: SidebarSubItem[];  // 서브메뉴 (선택사항)
}
```

### SidebarSubItem 타입

```tsx
interface SidebarSubItem {
  id: string;    // 고유 식별자
  name: string;  // 서브메뉴에 표시될 이름
  path: string;  // 라우터 경로
}
```

## 🎨 스타일링

### 기본 스타일
- **미니멀한 디자인**: 깔끔하고 현대적인 UI
- **ETRI 브랜딩**: ETRI 블루 컬러 사용
- **호버 효과**: 부드러운 상호작용
- **선택 상태**: 왼쪽 보더와 배경색으로 표시

### 커스터마이징
```tsx
<Sidebar
  title="커스텀 사이드바"
  menuItems={menuItems}
  width="w-96"
  className="border-r-2 border-gray-300"
/>
```

## 📱 반응형 지원

다양한 화면 크기에 맞는 너비 옵션:
- `w-64`: 256px (모바일/태블릿)
- `w-72`: 288px (기본)
- `w-80`: 320px (데스크톱, 권장)
- `w-96`: 384px (와이드 스크린)

## 🔄 자동 상태 관리

### 경로 기반 자동 선택
- 현재 URL과 메뉴 경로를 자동으로 매칭
- 서브메뉴 항목 선택 시 상위 메뉴도 자동 선택
- 서브메뉴가 있는 메뉴의 하위 경로 진입 시 자동 펼침

### 예시
```
현재 URL: /admin/content/posts
→ 'content' 메뉴 선택됨
→ 'posts' 서브메뉴 선택됨
→ 서브메뉴 자동 펼침
```

## 📋 미리 만들어진 사이드바 예시

`ExampleSidebars.tsx`에서 다양한 용도의 사이드바를 확인할 수 있습니다:

- `AdminSidebar`: 관리자 페이지용
- `CompanySidebar`: 회사소개 페이지용
- `ResearchSidebar`: 연구개발 페이지용
- `BusinessSidebar`: 사업분야 페이지용
- `SupportSidebar`: 고객지원 페이지용

## 🚀 성능 최적화

- **지연 렌더링**: 서브메뉴는 펼쳐질 때만 DOM에 렌더링
- **메모이제이션**: 불필요한 리렌더링 방지
- **부드러운 애니메이션**: CSS transition 사용

## 🔧 개발자 팁

### 1. 아이콘 선택
Lucide React 아이콘을 사용하세요:
```tsx
import { Home, Users, Settings, FileText } from 'lucide-react';
```

### 2. 경로 구조
일관된 경로 구조를 사용하세요:
```
/section/subsection/detail
예: /admin/users/list
```

### 3. 메뉴 ID
고유하고 의미있는 ID를 사용하세요:
```tsx
{ id: 'user-management', name: '사용자 관리', ... }
```

## 🐛 문제 해결

### 메뉴가 선택되지 않는 경우
- 경로가 정확한지 확인
- 대소문자 구분 확인
- 슬래시(/) 위치 확인

### 서브메뉴가 펼쳐지지 않는 경우
- `subItems` 배열이 올바르게 설정되었는지 확인
- 서브메뉴 경로가 메인 메뉴 경로를 포함하는지 확인

### 애니메이션이 부자연스러운 경우
- CSS 파일에 `slideInLeft` 애니메이션이 정의되어 있는지 확인
- `App.css`에 애니메이션 키프레임 추가 필요
