"""
DeepTech Valley Platform - FastAPI 메인 애플리케이션
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

# API 라우터 import (향후 추가 예정)
# from app.api import auth, users, business, notices, demands, valley, statistics

app = FastAPI(
    title="DeepTech Valley Platform API",
    description="딥테크 스케일업 밸리 육성을 위한 오픈플랫폼 API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # 프론트엔드 개발 서버
        "http://localhost:5173",  # Vite 개발 서버
        "https://deeptech-valley.com",  # 프로덕션 도메인 (예시)
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 신뢰할 수 있는 호스트 설정
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.deeptech-valley.com"]
)

# API 라우터 등록 (향후 추가 예정)
# app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
# app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
# app.include_router(business.router, prefix="/api/v1/business", tags=["business"])
# app.include_router(notices.router, prefix="/api/v1/notices", tags=["notices"])
# app.include_router(demands.router, prefix="/api/v1/demands", tags=["demands"])
# app.include_router(valley.router, prefix="/api/v1/valley", tags=["valley"])
# app.include_router(statistics.router, prefix="/api/v1/statistics", tags=["statistics"])

@app.get("/")
async def root():
    """루트 엔드포인트 - API 상태 확인"""
    return {
        "message": "DeepTech Valley Platform API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """헬스 체크 엔드포인트"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
