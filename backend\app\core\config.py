"""
애플리케이션 설정 관리
"""

from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """애플리케이션 설정 클래스"""
    
    # 애플리케이션 기본 설정
    APP_NAME: str = "DeepTech Valley Platform"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 데이터베이스 설정 (미정 - PostgreSQL/MySQL 중 선택)
    DATABASE_URL: Optional[str] = None
    DATABASE_ECHO: bool = False
    
    # 보안 설정
    SECRET_KEY: str = "your-secret-key-here"  # 실제 운영에서는 환경변수로 설정
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS 설정
    ALLOWED_ORIGINS: list[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
    ]
    
    # 파일 업로드 설정
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    UPLOAD_DIR: str = "uploads"
    
    # Redis 설정 (캐싱 및 세션)
    REDIS_URL: Optional[str] = None
    
    # 이메일 설정 (향후 알림 기능용)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: Optional[int] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 전역 설정 인스턴스
settings = Settings()
