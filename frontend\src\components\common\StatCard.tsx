import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import AnimatedCounter from './AnimatedCounter';

interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>;
  value: number;
  suffix?: string;
  label: string;
  description?: string;
  className?: string;
}

/**
 * 통계 정보를 표시하는 카드 컴포넌트
 * 아이콘, 애니메이션 카운터, 라벨을 포함
 */
const StatCard: React.FC<StatCardProps> = ({
  icon: Icon,
  value,
  suffix = '+',
  label,
  description,
  className = ''
}) => {
  return (
    <Card className={`text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${className}`}>
      <CardContent className="p-6">
        {/* 아이콘 */}
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-etri-blue-50 rounded-full">
            <Icon className="w-8 h-8 text-etri-blue-600" />
          </div>
        </div>

        {/* 애니메이션 카운터 */}
        <div className="mb-2">
          <AnimatedCounter value={value} suffix={suffix} />
        </div>

        {/* 라벨 */}
        <p className="text-gray-600 font-medium text-base">
          {label}
        </p>

        {/* 설명 (선택사항) */}
        {description && (
          <p className="text-gray-500 text-sm mt-2">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default StatCard;
