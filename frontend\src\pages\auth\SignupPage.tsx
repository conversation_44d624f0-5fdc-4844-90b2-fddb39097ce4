import React, { useState } from 'react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';

// 회원 유형 옵션
const USER_TYPES = [
  { key: 'institution', label: '기관' },
  { key: 'company', label: '기업' },
  { key: 'person', label: '개인' },
];

// 회원가입 페이지 컴포넌트
const SignupPage: React.FC = () => {
  // 회원 유형 상태
  const [userType, setUserType] = useState<'institution' | 'company' | 'person'>('person');
  // 입력 상태
  const [fields, setFields] = useState<any>({
    name: '',
    email: '',
    password: '',
    password2: '',
    institutionName: '',
    companyName: '',
    businessNumber: '',
    managerName: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // 입력값 변경 핸들러
  const handleChange = (key: string, value: string) => {
    setFields((prev: any) => ({ ...prev, [key]: value }));
  };

  // 회원가입 폼 제출
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    // 유형별 필수값 체크
    if (userType === 'institution') {
      if (!fields.institutionName || !fields.managerName || !fields.email || !fields.password || !fields.password2) {
        setError('모든 항목을 입력하세요.'); return;
      }
    } else if (userType === 'company') {
      if (!fields.companyName || !fields.businessNumber || !fields.managerName || !fields.email || !fields.password || !fields.password2) {
        setError('모든 항목을 입력하세요.'); return;
      }
    } else {
      if (!fields.name || !fields.email || !fields.password || !fields.password2) {
        setError('모든 항목을 입력하세요.'); return;
      }
    }
    if (fields.password !== fields.password2) {
      setError('비밀번호가 일치하지 않습니다.'); return;
    }
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      alert(`${USER_TYPES.find(t=>t.key===userType)?.label} 회원가입 성공! (실제 구현에서는 API 연동)`);
    }, 1000);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] py-12">
      <div className="w-full max-w-md bg-white rounded-xl shadow-lg p-8 border border-gray-100">
        <h2 className="text-2xl font-bold mb-6 text-center">회원가입</h2>
        {/* 유형 선택 탭 */}
        <div className="flex justify-center mb-8 gap-2">
          {USER_TYPES.map(type => (
            <button
              key={type.key}
              type="button"
              className={`px-4 py-2 rounded-lg text-sm font-semibold border transition-all ${userType===type.key ? 'bg-etri-blue-600 text-white border-etri-blue-600' : 'bg-white text-etri-blue-700 border-etri-blue-100 hover:bg-etri-blue-50'}`}
              onClick={() => setUserType(type.key as any)}
            >
              {type.label}
            </button>
          ))}
        </div>
        <form onSubmit={handleSubmit} className="space-y-5">
          {/* 유형별 입력 폼 */}
          {userType === 'institution' && (
            <>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700">기관명</label>
                <Input type="text" value={fields.institutionName} onChange={e=>handleChange('institutionName',e.target.value)} placeholder="기관명" required />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700">담당자명</label>
                <Input type="text" value={fields.managerName} onChange={e=>handleChange('managerName',e.target.value)} placeholder="담당자명" required />
              </div>
            </>
          )}
          {userType === 'company' && (
            <>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700">기업명</label>
                <Input type="text" value={fields.companyName} onChange={e=>handleChange('companyName',e.target.value)} placeholder="기업명" required />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700">사업자번호</label>
                <Input type="text" value={fields.businessNumber} onChange={e=>handleChange('businessNumber',e.target.value)} placeholder="사업자번호" required />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700">담당자명</label>
                <Input type="text" value={fields.managerName} onChange={e=>handleChange('managerName',e.target.value)} placeholder="담당자명" required />
              </div>
            </>
          )}
          {userType === 'person' && (
            <div>
              <label className="block mb-1 text-sm font-medium text-gray-700">이름</label>
              <Input type="text" value={fields.name} onChange={e=>handleChange('name',e.target.value)} placeholder="이름" required />
            </div>
          )}
          {/* 공통 입력 */}
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700">이메일</label>
            <Input type="email" value={fields.email} onChange={e=>handleChange('email',e.target.value)} placeholder="<EMAIL>" autoComplete="username" required />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700">비밀번호</label>
            <Input type="password" value={fields.password} onChange={e=>handleChange('password',e.target.value)} placeholder="비밀번호" autoComplete="new-password" required />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium text-gray-700">비밀번호 확인</label>
            <Input type="password" value={fields.password2} onChange={e=>handleChange('password2',e.target.value)} placeholder="비밀번호 확인" autoComplete="new-password" required />
          </div>
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? '회원가입 중...' : '회원가입'}
          </Button>
        </form>
        <div className="mt-6 text-center text-sm text-gray-500">
          이미 계정이 있으신가요? <a href="/login" className="text-etri-blue-600 hover:underline">로그인</a>
        </div>
      </div>
    </div>
  );
};

export default SignupPage; 