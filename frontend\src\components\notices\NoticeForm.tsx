import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { Notice, NoticeFile } from '@/types/notice';
import { noticeCategories } from '@/data/mockNotices';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { FileUpload } from './FileUpload';
import { Save, Send, Eye } from 'lucide-react';

// 폼 스키마 정의
const noticeFormSchema = z.object({
  title: z.string().min(1, '제목을 입력해주세요').max(200, '제목은 200자 이내로 입력해주세요'),
  summary: z.string().max(500, '요약은 500자 이내로 입력해주세요').optional(),
  content: z.string().min(1, '내용을 입력해주세요'),
  category: z.enum(['general', 'important', 'urgent', 'event']),
  isImportant: z.boolean(),
  isPinned: z.boolean(),
  tags: z.string(),
});

type NoticeFormData = z.infer<typeof noticeFormSchema>;

interface NoticeFormProps {
  notice?: Notice;
  onSubmit: (data: NoticeFormData & { files: NoticeFile[]; status: 'draft' | 'published' }) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export const NoticeForm: React.FC<NoticeFormProps> = ({
  notice,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [files, setFiles] = useState<NoticeFile[]>(notice?.files || []);
  const [previewMode, setPreviewMode] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<NoticeFormData>({
    resolver: zodResolver(noticeFormSchema),
    defaultValues: {
      title: notice?.title || '',
      summary: notice?.summary || '',
      content: notice?.content || '',
      category: notice?.category || 'general',
      isImportant: notice?.isImportant || false,
      isPinned: notice?.isPinned || false,
      tags: notice?.tags.join(', ') || '',
    },
  });

  const watchedContent = watch('content');
  const watchedTitle = watch('title');

  const handleFormSubmit = (data: NoticeFormData, status: 'draft' | 'published') => {
    onSubmit({
      ...data,
      files,
      status,
    });
  };

  const handleSaveDraft = (data: NoticeFormData) => {
    handleFormSubmit(data, 'draft');
  };

  const handlePublish = (data: NoticeFormData) => {
    handleFormSubmit(data, 'published');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* 헤더 */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">
          {notice ? '공지사항 편집' : '공지사항 작성'}
        </h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? '편집 모드' : '미리보기'}
          </Button>
        </div>
      </div>

      {previewMode ? (
        /* 미리보기 모드 */
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">{watchedTitle || '제목 없음'}</CardTitle>
          </CardHeader>
          <CardContent>
            <div 
              className="prose prose-gray max-w-none"
              dangerouslySetInnerHTML={{ __html: watchedContent || '<p>내용이 없습니다.</p>' }}
            />
          </CardContent>
        </Card>
      ) : (
        /* 편집 모드 */
        <form className="space-y-6">
          {/* 기본 정보 */}
          <Card>
            <CardHeader>
              <CardTitle>기본 정보</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 제목 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  제목 *
                </label>
                <Input
                  {...register('title')}
                  placeholder="공지사항 제목을 입력하세요"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && (
                  <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
                )}
              </div>

              {/* 요약 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  요약
                </label>
                <Textarea
                  {...register('summary')}
                  placeholder="공지사항 요약을 입력하세요 (선택사항)"
                  rows={3}
                  className={errors.summary ? 'border-red-500' : ''}
                />
                {errors.summary && (
                  <p className="text-red-500 text-sm mt-1">{errors.summary.message}</p>
                )}
              </div>

              {/* 카테고리 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  카테고리 *
                </label>
                <Select
                  value={watch('category')}
                  onValueChange={(value) => setValue('category', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="카테고리를 선택하세요" />
                  </SelectTrigger>
                  <SelectContent>
                    {noticeCategories.filter(cat => cat.value !== 'all').map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 옵션 */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isImportant"
                    checked={watch('isImportant')}
                    onCheckedChange={(checked) => setValue('isImportant', !!checked)}
                  />
                  <label htmlFor="isImportant" className="text-sm font-medium">
                    중요 공지로 표시
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isPinned"
                    checked={watch('isPinned')}
                    onCheckedChange={(checked) => setValue('isPinned', !!checked)}
                  />
                  <label htmlFor="isPinned" className="text-sm font-medium">
                    상단 고정
                  </label>
                </div>
              </div>

              {/* 태그 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  태그
                </label>
                <Input
                  {...register('tags')}
                  placeholder="태그를 쉼표로 구분하여 입력하세요 (예: 모집공고, 딥테크, 2025년)"
                />
                <p className="text-sm text-gray-500 mt-1">
                  태그는 쉼표(,)로 구분하여 입력하세요
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 내용 */}
          <Card>
            <CardHeader>
              <CardTitle>내용</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                {...register('content')}
                placeholder="공지사항 내용을 입력하세요"
                rows={15}
                className={errors.content ? 'border-red-500' : ''}
              />
              {errors.content && (
                <p className="text-red-500 text-sm mt-1">{errors.content.message}</p>
              )}
              <p className="text-sm text-gray-500 mt-2">
                HTML 태그를 사용할 수 있습니다. (실제 구현에서는 리치 에디터 사용 권장)
              </p>
            </CardContent>
          </Card>

          {/* 파일 첨부 */}
          <Card>
            <CardHeader>
              <CardTitle>파일 첨부</CardTitle>
            </CardHeader>
            <CardContent>
              <FileUpload
                files={files}
                onFilesChange={setFiles}
              />
            </CardContent>
          </Card>
        </form>
      )}

      {/* 액션 버튼 */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          취소
        </Button>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleSubmit(handleSaveDraft)}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            임시저장
          </Button>
          <Button
            onClick={handleSubmit(handlePublish)}
            disabled={isLoading}
          >
            <Send className="h-4 w-4 mr-2" />
            게시하기
          </Button>
        </div>
      </div>
    </div>
  );
};
