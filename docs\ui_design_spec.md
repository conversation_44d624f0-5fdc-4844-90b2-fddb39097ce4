# DeepTech Valley Platform UI 설계 명세서

## 1. 디자인 시스템

### 1.1 컬러 팔레트 (기관용 전문 디자인)
```css
/* Primary Colors - 신뢰감 있는 블루 계열 */
--primary-50: #eff6ff;
--primary-100: #dbeafe;
--primary-500: #3b82f6;  /* 메인 브랜드 컬러 */
--primary-600: #2563eb;
--primary-700: #1d4ed8;
--primary-900: #1e3a8a;

/* Secondary Colors - 세련된 그레이 계열 */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-500: #6b7280;
--gray-700: #374151;
--gray-900: #111827;

/* Accent Colors */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #06b6d4;
```

### 1.2 타이포그래피
```css
/* 폰트 패밀리 */
--font-sans: 'Pretendard', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
--font-mono: 'JetBrains Mono', 'Fira Code', monospace;

/* 폰트 크기 */
--text-xs: 0.75rem;    /* 12px - 캡션 */
--text-sm: 0.875rem;   /* 14px - 보조 텍스트 */
--text-base: 1rem;     /* 16px - 기본 본문 */
--text-lg: 1.125rem;   /* 18px - 큰 본문 */
--text-xl: 1.25rem;    /* 20px - 소제목 */
--text-2xl: 1.5rem;    /* 24px - 제목 */
--text-3xl: 1.875rem;  /* 30px - 큰 제목 */

/* 폰트 굵기 */
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### 1.3 간격 시스템
```css
/* 8px 기반 스페이싱 */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
```

## 2. 레이아웃 구조

### 2.1 전체 레이아웃
```
┌─────────────────────────────────────────┐
│                Header                    │
├─────────────┬───────────────────────────┤
│             │                           │
│   Sidebar   │        Main Content       │
│             │                           │
│             │                           │
├─────────────┴───────────────────────────┤
│                Footer                   │
└─────────────────────────────────────────┘
```

### 2.2 반응형 브레이크포인트
- **Mobile**: 320px ~ 767px
- **Tablet**: 768px ~ 1023px
- **Desktop**: 1024px ~ 1439px
- **Large Desktop**: 1440px+

## 3. 주요 화면별 설계

### 3.1 메인 페이지 (에코시스템 맵)
```
┌─────────────────────────────────────────┐
│  Header: Logo + Navigation + User Menu  │
├─────────────────────────────────────────┤
│                                         │
│        Interactive Ecosystem Map        │
│     ┌─────┐    ┌─────┐    ┌─────┐      │
│     │ 기업A │────│ 기관B │────│ 기업C │      │
│     └─────┘    └─────┘    └─────┘      │
│        │         │         │          │
│     ┌─────┐    ┌─────┐    ┌─────┐      │
│     │ 기관D │────│ 기업E │────│ 기업F │      │
│     └─────┘    └─────┘    └─────┘      │
│                                         │
├─────────────────────────────────────────┤
│  Statistics Bar                         │
│  📊 등록기업: 150  📈 진행프로젝트: 45    │
│  📋 등록기술: 89   ✅ 완료프로젝트: 23    │
└─────────────────────────────────────────┘
```

### 3.2 회원관리 페이지
```
┌─────────────────────────────────────────┐
│  회원 관리                               │
├─────────────────────────────────────────┤
│  [검색] [필터: 사용자타입] [+ 새 회원]    │
├─────────────────────────────────────────┤
│  ┌─────┬─────────┬─────────┬─────────┐  │
│  │선택 │ 이름     │ 타입     │ 상태     │  │
│  ├─────┼─────────┼─────────┼─────────┤  │
│  │ ☐  │ 홍길동   │ 기업     │ 활성     │  │
│  │ ☐  │ 김철수   │ 기관     │ 활성     │  │
│  │ ☐  │ 이영희   │ 개인     │ 비활성   │  │
│  └─────┴─────────┴─────────┴─────────┘  │
├─────────────────────────────────────────┤
│  [이전] 1 2 3 4 5 [다음]                │
└─────────────────────────────────────────┘
```

### 3.3 수요관리 - 사업신청 폼
```
┌─────────────────────────────────────────┐
│  사업 신청서 작성                        │
├─────────────────────────────────────────┤
│  📋 기업 정보                           │
│  ┌─────────────────────────────────────┐ │
│  │ 회사명: [________________]          │ │
│  │ 규모: ○ 스타트업 ○ 중소 ○ 중견 ○ 대기업 │ │
│  │ 사업분야: [________________]        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  📝 프로젝트 정보                       │
│  ┌─────────────────────────────────────┐ │
│  │ 제목: [________________________]   │ │
│  │ 설명: [________________________]   │ │
│  │       [________________________]   │ │
│  │ 예산: [________________] 원        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  📎 첨부파일                           │
│  ┌─────────────────────────────────────┐ │
│  │ 사업계획서: [파일선택] [업로드]      │ │
│  │ 재무제표:   [파일선택] [업로드]      │ │
│  │ 기타문서:   [파일선택] [업로드]      │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  [임시저장]              [제출하기]     │
└─────────────────────────────────────────┘
```

### 3.4 밸리관리 - 통계 대시보드
```
┌─────────────────────────────────────────┐
│  밸리 관리 대시보드                      │
├─────────────────────────────────────────┤
│  📊 주요 지표                           │
│  ┌─────────┬─────────┬─────────┬───────┐ │
│  │ 총 기업수 │ 신규 신청 │ 승인률   │ 평균점수│ │
│  │   150   │   25    │  65%   │  8.2  │ │
│  └─────────┴─────────┴─────────┴───────┘ │
│                                         │
│  📈 월별 트렌드                         │
│  ┌─────────────────────────────────────┐ │
│  │     ▲                               │ │
│  │    ╱ ╲                              │ │
│  │   ╱   ╲     ▲                       │ │
│  │  ╱     ╲   ╱ ╲                      │ │
│  │ ╱       ╲ ╱   ╲                     │ │
│  │╱         ╲╱     ╲                   │ │
│  └─────────────────────────────────────┘ │
│  1월  2월  3월  4월  5월  6월           │
│                                         │
│  🏢 기업 규모별 분포                    │
│  ┌─────────────────────────────────────┐ │
│  │ 스타트업 ████████████ 45%           │ │
│  │ 중소기업 ████████ 30%               │ │
│  │ 중견기업 ████ 15%                   │ │
│  │ 대기업   ██ 10%                     │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 4. 컴포넌트 설계

### 4.1 버튼 컴포넌트
```typescript
// Primary Button
<Button variant="primary" size="md">
  제출하기
</Button>

// Secondary Button  
<Button variant="secondary" size="md">
  취소
</Button>

// Sizes: sm, md, lg
// Variants: primary, secondary, outline, ghost, destructive
```

### 4.2 폼 컴포넌트
```typescript
// Input Field
<FormField>
  <Label>회사명</Label>
  <Input placeholder="회사명을 입력하세요" />
  <FormMessage>필수 입력 항목입니다</FormMessage>
</FormField>

// Select Field
<FormField>
  <Label>기업 규모</Label>
  <Select>
    <SelectTrigger>
      <SelectValue placeholder="선택하세요" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="startup">스타트업</SelectItem>
      <SelectItem value="small">중소기업</SelectItem>
    </SelectContent>
  </Select>
</FormField>
```

### 4.3 테이블 컴포넌트
```typescript
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>이름</TableHead>
      <TableHead>타입</TableHead>
      <TableHead>상태</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>홍길동</TableCell>
      <TableCell>기업</TableCell>
      <TableCell>
        <Badge variant="success">활성</Badge>
      </TableCell>
    </TableRow>
  </TableBody>
</Table>
```

### 4.4 카드 컴포넌트
```typescript
<Card>
  <CardHeader>
    <CardTitle>통계 정보</CardTitle>
    <CardDescription>최근 30일 데이터</CardDescription>
  </CardHeader>
  <CardContent>
    <div className="text-2xl font-bold">150</div>
    <p className="text-sm text-gray-500">총 등록 기업</p>
  </CardContent>
</Card>
```

## 5. 애니메이션 및 상호작용

### 5.1 페이지 전환 애니메이션
```typescript
// Framer Motion을 사용한 페이지 전환
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.3 }}
>
  {children}
</motion.div>
```

### 5.2 호버 효과
```css
/* 버튼 호버 효과 */
.button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

/* 카드 호버 효과 */
.card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}
```

### 5.3 로딩 상태
```typescript
// 스켈레톤 로딩
<div className="animate-pulse">
  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
</div>

// 스피너 로딩
<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
```

## 6. 접근성 가이드라인

### 6.1 키보드 네비게이션
- Tab 키로 모든 상호작용 요소 접근 가능
- Enter/Space 키로 버튼 활성화
- Escape 키로 모달/드롭다운 닫기

### 6.2 스크린 리더 지원
```html
<!-- ARIA 라벨 사용 -->
<button aria-label="사용자 메뉴 열기">
  <UserIcon />
</button>

<!-- 상태 정보 제공 -->
<div aria-live="polite" aria-atomic="true">
  파일 업로드 완료
</div>

<!-- 폼 필드 연결 -->
<label for="company-name">회사명</label>
<input id="company-name" aria-describedby="company-name-help" />
<div id="company-name-help">정확한 회사명을 입력하세요</div>
```

### 6.3 색상 대비
- 텍스트와 배경 간 최소 4.5:1 대비율 유지
- 중요한 정보는 색상 외에 아이콘이나 텍스트로도 표현

## 7. 모바일 최적화

### 7.1 터치 인터페이스
- 최소 터치 영역: 44px × 44px
- 충분한 간격으로 오터치 방지
- 스와이프 제스처 지원

### 7.2 모바일 네비게이션
```
┌─────────────────────┐
│ ☰ Logo        👤   │ ← Header (고정)
├─────────────────────┤
│                     │
│   Main Content      │
│                     │
│                     │
├─────────────────────┤
│ 🏠 📊 📋 👥 ⚙️    │ ← Bottom Navigation
└─────────────────────┘
```

## 8. 다크모드 지원

### 8.1 다크모드 컬러 팔레트
```css
[data-theme="dark"] {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --primary: #3b82f6;
  --primary-foreground: #f8fafc;
}
```

### 8.2 테마 전환
```typescript
const { theme, setTheme } = useTheme();

<Button
  variant="ghost"
  size="sm"
  onClick={() => setTheme(theme === "light" ? "dark" : "light")}
>
  {theme === "light" ? <MoonIcon /> : <SunIcon />}
</Button>
```
