# UI/UX Design Template (Simplified)

## 🎯 목적
웹 프로젝트의 기본 UI/UX 가이드라인을 제공하는 템플릿입니다. AI가 일관된 프론트엔드 개발을 할 수 있도록 필수적인 디자인 기준만 정의합니다.

## 🎨 기본 디자인 시스템

### 타이포그래피 (고정)
```css
/* 폰트 패밀리 - 한글/영문 최적화 */
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", 
               "Noto Sans KR", "Apple SD Gothic Neo", "Malgun Gothic", 
               sans-serif;
  --font-mono: "Fira Code", "SF Mono", "Monaco", "Cascadia Code", 
               "Roboto Mono", monospace;
}

/* 폰트 크기 */
--text-xs: 0.75rem;      /* 12px - 캡션, 라벨 */
--text-sm: 0.875rem;     /* 14px - 보조 텍스트 */
--text-base: 1rem;       /* 16px - 기본 본문 */
--text-lg: 1.125rem;     /* 18px - 큰 본문 */
--text-xl: 1.25rem;      /* 20px - 소제목 */
--text-2xl: 1.5rem;      /* 24px - 제목 */
--text-3xl: 1.875rem;    /* 30px - 큰 제목 */

/* 폰트 굵기 */
--font-normal: 400;      /* 일반 텍스트 */
--font-medium: 500;      /* 서브타이틀, 버튼 */
--font-semibold: 600;    /* 소제목 */
--font-bold: 700;        /* 제목, 강조 */

/* 줄 높이 */
--leading-tight: 1.25;   /* 제목용 */
--leading-normal: 1.5;   /* 본문용 */
--leading-relaxed: 1.75; /* 여유로운 읽기용 */
```

### 간격 시스템 (고정)
```css
/* 8px 기반 스페이싱 시스템 */
:root {
  --space-1: 0.25rem;     /* 4px */
  --space-2: 0.5rem;      /* 8px */
  --space-3: 0.75rem;     /* 12px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-8: 2rem;        /* 32px */
  --space-10: 2.5rem;     /* 40px */
  --space-12: 3rem;       /* 48px */
  --space-16: 4rem;       /* 64px */
}

/* 컴포넌트 기본 크기 */
--btn-height: 2.5rem;    /* 40px - 버튼 높이 */
--input-height: 2.5rem;  /* 40px - 입력 필드 높이 */
--header-height: 4rem;   /* 64px - 헤더 높이 */
```

### 보더 및 효과 (고정)
```css
:root {
  /* 보더 반경 */
  --radius-sm: 0.25rem;   /* 4px */
  --radius: 0.5rem;       /* 8px - 기본 */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-full: 9999px;  /* 완전한 원형 */
  
  /* 보더 두께 */
  --border-width: 1px;
  
  /* 그림자 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
```

## 🎨 색상 시스템 (프로젝트별 커스터마이징)

### 색상 변수 구조
```css
/* 
이 색상들은 프로젝트별로 수정하세요 
각 프로젝트의 브랜딩에 맞게 조정 가능
*/

:root {
  /* Primary 색상 (브랜드 메인 컬러) */
  --primary-50: #eff6ff;
  --primary-500: #3b82f6;    /* 기본 Primary */
  --primary-600: #2563eb;    /* Hover 상태 */
  --primary-700: #1d4ed8;    /* Active 상태 */
  
  /* Neutral 색상 (그레이 스케일) */
  --gray-50: #f9fafb;        /* 배경 */
  --gray-100: #f3f4f6;       /* 연한 배경 */
  --gray-200: #e5e7eb;       /* 보더 */
  --gray-300: #d1d5db;       /* 비활성 요소 */
  --gray-500: #6b7280;       /* 보조 텍스트 */
  --gray-700: #374151;       /* 일반 텍스트 */
  --gray-900: #111827;       /* 메인 텍스트 */
  
  /* Semantic 색상 (상태 표시) */
  --success: #10b981;        /* 성공 - 그린 */
  --warning: #f59e0b;        /* 경고 - 옐로우 */
  --error: #ef4444;          /* 에러 - 레드 */
  --info: #3b82f6;           /* 정보 - 블루 */
  
  /* 배경 색상 */
  --background: #ffffff;      /* 메인 배경 */
  --surface: #f9fafb;        /* 카드 배경 */
}
```

### 색상 사용 가이드
```markdown
**Primary**: 브랜드 색상, 주요 버튼, 링크, 강조 요소
**Gray**: 텍스트, 보더, 배경, 비활성 상태
**Semantic**: 상태 표시 (성공/경고/에러/정보)
**Background**: 페이지 배경, 카드 배경

**색상 변경 방법**:
1. 브랜드에 맞는 Primary 색상 선택
2. --primary-* 변수들을 해당 색상의 명도별로 설정
3. 필요시 Semantic 색상도 브랜드에 맞게 조정
```

## 🧩 기본 컴포넌트 가이드

### 버튼 스타일
```css
/* 버튼 기본 구조 */
.btn {
  height: var(--btn-height);
  padding: 0 var(--space-4);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  border: var(--border-width) solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

/* 버튼 변형 (색상은 프로젝트별 조정) */
.btn-primary {
  background-color: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-600);
  border-color: var(--primary-500);
}

.btn-outline {
  background-color: transparent;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

/* 크기 변형 */
.btn-sm {
  height: 2rem;
  padding: 0 var(--space-3);
  font-size: var(--text-sm);
}

.btn-lg {
  height: 3rem;
  padding: 0 var(--space-6);
  font-size: var(--text-lg);
}
```

### 입력 필드 스타일
```css
.input {
  width: 100%;
  height: var(--input-height);
  padding: 0 var(--space-3);
  font-size: var(--text-base);
  font-family: var(--font-sans);
  color: var(--gray-900);
  background-color: var(--background);
  border: var(--border-width) solid var(--gray-300);
  border-radius: var(--radius);
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
}

.input.error {
  border-color: var(--error);
}

.input:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}
```

### 카드 스타일
```css
.card {
  background-color: var(--background);
  border: var(--border-width) solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: var(--border-width) solid var(--gray-200);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--gray-900);
  margin: 0;
}
```

## 📱 반응형 디자인

### 브레이크포인트 (고정)
```css
/* 모바일 우선 설계 */
:root {
  --breakpoint-sm: 640px;   /* 작은 태블릿 */
  --breakpoint-md: 768px;   /* 태블릿 */
  --breakpoint-lg: 1024px;  /* 데스크톱 */
  --breakpoint-xl: 1280px;  /* 큰 데스크톱 */
}

/* 미디어 쿼리 사용법 */
@media (min-width: 768px) {
  /* 태블릿 이상에서만 적용 */
}
```

### 기본 레이아웃 패턴
```css
/* 컨테이너 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* 그리드 레이아웃 */
.grid {
  display: grid;
  gap: var(--space-4);
  grid-template-columns: 1fr; /* 모바일: 1열 */
}

@media (min-width: 768px) {
  .grid-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-3 { grid-template-columns: repeat(3, 1fr); }
}

/* 플렉스 레이아웃 */
.flex {
  display: flex;
  gap: var(--space-4);
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}
```

## 🎭 기본 UX 패턴

### 로딩 상태
```css
.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--gray-500);
  font-size: var(--text-sm);
}
```

### 상태 표시
```css
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
}

.badge-success {
  background-color: #dcfce7;
  color: #166534;
}

.badge-error {
  background-color: #fee2e2;
  color: #991b1b;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}
```

### 기본 애니메이션
```css
.fade-in {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
```

## 📋 구현 체크리스트

### 필수 구현 사항
- [ ] **폰트 시스템 적용** (font-sans, 크기, 굵기)
- [ ] **간격 시스템 적용** (space-* 변수 사용)
- [ ] **기본 컴포넌트** (Button, Input, Card) 구현
- [ ] **반응형 레이아웃** (모바일 우선)
- [ ] **색상 시스템 커스터마이징** (프로젝트 브랜드에 맞게)

### 권장 구현 사항
- [ ] **로딩 상태 표시**
- [ ] **에러/성공 상태 표시**
- [ ] **호버 효과**
- [ ] **포커스 상태** (접근성)

## 💡 AI 개발 가이드

### 색상 커스터마이징 방법
```markdown
1. **브랜드 Primary 색상 결정** (예: 쇼핑몰 - 주황, 의료 - 파랑, 교육 - 초록)
2. **Primary 색상의 명도 변화 생성** (50, 500, 600, 700)
3. **CSS 변수 업데이트**:
   - --primary-500: [메인 색상]
   - --primary-600: [hover 상태, 약간 어둡게]
   - --primary-700: [active 상태, 더 어둡게]
4. **필요시 Semantic 색상도 브랜드에 맞게 조정**
```

### 컴포넌트 개발 순서
1. **기본 HTML 구조** 작성
2. **CSS 클래스 적용** (btn, input, card 등)
3. **반응형 확인** (모바일 → 데스크톱)
4. **상태별 스타일** 적용 (hover, focus, disabled)
5. **색상 커스터마이징** (프로젝트에 맞게)

### 주의사항
- **CSS 변수를 활용**하여 일관성 유지
- **모바일 우선**으로 개발 후 데스크톱 확장
- **간격은 반드시 space-* 변수** 사용
- **폰트는 지정된 크기/굵기만** 사용
- **색상 변경 시 전체 일관성** 확인

이 가이드를 바탕으로 프로젝트별 브랜딩에 맞는 UI/UX를 구현할 수 있습니다.