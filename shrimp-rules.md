# DeepTech Valley Platform 개발 표준 (AI Agent 전용)

## 프로젝트 개요

### 프로젝트 정보
- **프로젝트명**: DeepTech Valley Platform
- **개발 기간**: 2025.06.17 ~ 2025.09.15 (3개월)
- **개발 방식**: 3단계 순차 개발
- **목적**: 밸리 내 기업 소통, 공모, 선정을 지원하는 통합 오픈플랫폼

### 핵심 기능 모듈
- **회원관리**: 기업/기관/개인/관리자 다중 사용자 타입, 권한 기반 접근 제어
- **사업소개**: 권한 기반 사업 정보, 기술 가이드, 소스코드 브라우징
- **공지관리**: CRUD, 파일 첨부, 검색, 중요 공지 상단 고정
- **밸리관리**: D3.js 에코시스템 관계도 시각화, 참여기업 통계 대시보드
- **수요관리**: 사업신청 폼, 다중 파일 업로드, PDF 문서 자동 생성, 심사 워크플로우

## 기술 스택 및 아키텍처

### Frontend Core
- **React 18+**: 함수형 컴포넌트, Concurrent Features, Hooks
- **TypeScript**: 타입 안정성 및 개발 효율성
- **Vite**: 빠른 개발 환경 및 최적화된 빌드

### Backend Core
- **Python 3.11+**: 메인 백엔드 언어
- **FastAPI**: 고성능 웹 프레임워크, 자동 API 문서 생성
- **SQLAlchemy**: ORM (Object-Relational Mapping)
- **Alembic**: 데이터베이스 마이그레이션
- **Pydantic**: 데이터 검증 및 직렬화

### UI/Styling
- **ShadcnUI**: 고품질 컴포넌트, Radix UI 기반, 접근성 지원 (우선)
- **TailwindCSS**: 유틸리티 우선 스타일링
- **Framer Motion**: 애니메이션 및 상호작용

### 상태 관리
- **Context API**: 전역 상태 (인증, 테마)
- **React Query/TanStack Query**: 서버 상태 관리
- **React Hook Form**: 폼 상태 관리

### 데이터 시각화
- **React Flow**: 에코시스템 맵, 노드-링크 다이어그램
- **Chart.js**: 기본 통계 차트
- **D3.js**: 복잡한 커스텀 시각화 (필요시)

### 백엔드 추가 라이브러리
- **pandas**: 데이터 분석 및 통계 처리
- **ReportLab**: PDF 문서 자동 생성
- **python-multipart**: 파일 업로드 처리
- **python-jose**: JWT 토큰 처리
- **Celery**: 백그라운드 작업 처리
- **Redis**: 캐싱 및 세션 관리

### 아키텍처 원칙
- **컴포넌트 기반 모듈화**: 재사용 가능한 컴포넌트 설계
- **관심사 분리**: UI, 비즈니스 로직, 데이터 레이어 분리
- **서비스 레이어 패턴**: API 호출 로직 추상화

## 디렉토리 구조 표준

### 프론트엔드 디렉토리 구조
```
frontend/src/
├── components/
│   ├── ui/                 # ShadcnUI 컴포넌트
│   ├── common/            # 공통 재사용 컴포넌트
│   ├── layout/            # 레이아웃 컴포넌트
│   ├── forms/             # 폼 관련 컴포넌트
│   └── charts/            # 시각화 컴포넌트
├── pages/
│   ├── auth/              # 인증 관련 페이지
│   ├── dashboard/         # 대시보드
│   ├── admin/             # 관리자 페이지
│   ├── business/          # 사업소개
│   ├── notice/            # 공지관리
│   ├── demand/            # 수요관리
│   └── valley/            # 밸리관리
├── hooks/                 # 커스텀 훅
├── services/              # API 서비스 레이어
├── contexts/              # React Context
├── utils/                 # 유틸리티 함수
├── constants/             # 상수 정의
├── types/                 # TypeScript 타입 정의
└── assets/                # 정적 자원
```

### 백엔드 디렉토리 구조
```
backend/
├── app/
│   ├── api/               # API 라우터
│   │   ├── auth/          # 인증 관련 API
│   │   ├── users/         # 사용자 관리 API
│   │   ├── business/      # 사업소개 API
│   │   ├── notices/       # 공지관리 API
│   │   ├── demands/       # 수요관리 API
│   │   ├── valley/        # 밸리관리 API
│   │   └── statistics/    # 통계 API
│   ├── core/              # 핵심 설정
│   │   ├── config.py      # 환경 설정
│   │   ├── security.py    # 보안 관련
│   │   └── database.py    # 데이터베이스 설정
│   ├── models/            # SQLAlchemy 모델
│   ├── schemas/           # Pydantic 스키마
│   ├── services/          # 비즈니스 로직
│   ├── utils/             # 유틸리티 함수
│   └── main.py            # FastAPI 앱 진입점
├── alembic/               # 데이터베이스 마이그레이션
├── tests/                 # 테스트 코드
└── requirements.txt       # Python 의존성
```

### 파일 명명 규칙
- **컴포넌트 파일**: `ComponentName.tsx` (TypeScript)
- **페이지 파일**: `PageName.tsx`
- **훅 파일**: `useHookName.ts`
- **서비스 파일**: `serviceName.ts`
- **타입 파일**: `types.ts` 또는 `ComponentName.types.ts`
- **테스트 파일**: `ComponentName.test.tsx`
- **인덱스 파일**: 각 폴더에 `index.ts` 생성하여 export

## 코딩 표준

### 명명 규칙
- **컴포넌트명**: PascalCase (`UserProfile`, `AdminDashboard`, `EcosystemMap`)
- **파일명**: PascalCase (컴포넌트), camelCase (유틸리티, 서비스)
- **함수명**: camelCase (`handleSubmit`, `fetchUserData`, `generatePDF`)
- **변수명**: camelCase (`userData`, `isLoading`, `applicationForm`)
- **상수명**: UPPER_SNAKE_CASE (`API_BASE_URL`, `USER_TYPES`, `MAX_FILE_SIZE`)
- **타입명**: PascalCase (`UserType`, `ApplicationFormData`, `ApiResponse`)
- **인터페이스명**: PascalCase, I 접두사 사용 안함 (`User`, `Permission`)
- **CSS 클래스**: kebab-case (`user-profile`, `admin-panel`, `ecosystem-map`)

### TypeScript 타입 정의
```typescript
// 사용자 타입 정의
enum UserType {
  INDIVIDUAL = 'individual',
  COMPANY = 'company',
  INSTITUTION = 'institution',
  ADMIN = 'admin'
}

// 인터페이스 정의
interface User {
  id: number;
  email: string;
  userType: UserType;
  isActive: boolean;
  createdAt: Date;
}

// API 응답 타입
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}
```

### 코드 포맷팅
- **들여쓰기**: 2 스페이스
- **세미콜론**: 항상 사용
- **따옴표**: 단일 따옴표 우선 ('string')
- **줄 길이**: 최대 100자
- **객체/배열**: 마지막 요소 뒤 쉼표 사용 (trailing comma)

### 주석 규칙
```typescript
/**
 * 사용자 프로필을 업데이트합니다.
 * @param userId - 사용자 ID
 * @param profileData - 업데이트할 프로필 데이터
 * @returns 업데이트된 사용자 정보
 */
const updateUserProfile = async (
  userId: number,
  profileData: Partial<User>
): Promise<User> => {
  // TODO: 입력 데이터 검증 추가
  // FIXME: 에러 처리 개선 필요
  return await userService.updateProfile(userId, profileData);
};
```

## 컴포넌트 개발 표준

### React 컴포넌트 규칙
- **함수형 컴포넌트만 사용**: 클래스 컴포넌트 금지
- **TypeScript 타입 정의 필수**: PropTypes 대신 TypeScript 인터페이스 사용
- **기본값 설정**: 기본 매개변수 또는 defaultProps 사용
- **조건부 렌더링**: 명확한 조건 사용, 중첩 삼항 연산자 3단계 이상 금지

### TypeScript 컴포넌트 구조 템플릿
```typescript
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ComponentNameProps {
  title: string;
  description?: string;
  isLoading?: boolean;
  onSubmit?: (data: FormData) => void;
  className?: string;
}

const ComponentName: React.FC<ComponentNameProps> = ({
  title,
  description = '',
  isLoading = false,
  onSubmit,
  className
}) => {
  // 상태 관리
  const [data, setData] = useState<FormData | null>(null);

  // 이벤트 핸들러
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (data && onSubmit) {
      onSubmit(data);
    }
  };

  // 부수 효과
  useEffect(() => {
    // 컴포넌트 마운트 시 실행할 로직
  }, []);

  // 로딩 상태 처리
  if (isLoading) {
    return <div className="animate-pulse">Loading...</div>;
  }

  return (
    <div className={cn('component-wrapper', className)}>
      <h2 className="text-xl font-semibold">{title}</h2>
      {description && (
        <p className="text-gray-600 mt-2">{description}</p>
      )}
      {/* 추가 JSX 내용 */}
    </div>
  );
};

export default ComponentName;
```

### ShadcnUI 컴포넌트 활용
```typescript
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const FormComponent: React.FC = () => {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>사용자 정보</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Input placeholder="이메일을 입력하세요" type="email" />
        <Button className="w-full">제출</Button>
      </CardContent>
    </Card>
  );
};
```

### 필수 구현 사항
- **로딩 상태 처리**: 모든 비동기 작업에 로딩 상태 표시
- **에러 바운더리**: 컴포넌트 레벨 에러 처리
- **접근성 속성**: aria-label, role, tabIndex 등 추가
- **키보드 네비게이션**: Tab, Enter, Space 키 지원
- **반응형 디자인**: 모든 화면 크기에서 정상 동작

## 스타일링 표준

### ShadcnUI + TailwindCSS 우선 사용
- **ShadcnUI 컴포넌트 우선**: 기본 UI 컴포넌트는 ShadcnUI 사용
- **TailwindCSS 유틸리티 클래스**: 레이아웃 및 스타일링
- **커스텀 CSS 최소화**: 필요시에만 CSS 모듈 또는 styled-components 사용

### TailwindCSS 사용 규칙
```typescript
// 좋은 예: 반응형 및 상태별 클래스 사용
<div className="w-full max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
  <Button
    className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700
               focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
               disabled:opacity-50 disabled:cursor-not-allowed"
    disabled={isLoading}
  >
    {isLoading ? 'Loading...' : 'Submit'}
  </Button>
</div>

// 나쁜 예: 인라인 스타일 사용
<div style={{ width: '100%', padding: '16px' }}>
  <button style={{ backgroundColor: 'blue' }}>Submit</button>
</div>
```

### 반응형 디자인 기준
- **모바일 우선 설계**: 320px부터 시작
- **브레이크포인트**:
  - `sm`: 640px (모바일 가로)
  - `md`: 768px (태블릿)
  - `lg`: 1024px (데스크톱)
  - `xl`: 1280px (대형 데스크톱)
  - `2xl`: 1536px (초대형 화면)

### 색상 및 테마 시스템
```typescript
// TailwindCSS 색상 팔레트 사용
const colorClasses = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700',
  secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
  success: 'bg-green-600 text-white hover:bg-green-700',
  warning: 'bg-yellow-500 text-white hover:bg-yellow-600',
  danger: 'bg-red-600 text-white hover:bg-red-700'
};

// 다크모드 지원
<div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
    Title
  </h1>
</div>
```

### 컴포넌트 스타일링 패턴
```typescript
import { cn } from '@/lib/utils';

interface CardProps {
  variant?: 'default' | 'outlined' | 'elevated';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Card: React.FC<CardProps> = ({
  variant = 'default',
  size = 'md',
  className,
  children
}) => {
  return (
    <div className={cn(
      // 기본 스타일
      'rounded-lg border',
      // 변형별 스타일
      {
        'bg-white shadow-sm': variant === 'default',
        'bg-transparent border-2': variant === 'outlined',
        'bg-white shadow-lg': variant === 'elevated'
      },
      // 크기별 스타일
      {
        'p-3': size === 'sm',
        'p-4': size === 'md',
        'p-6': size === 'lg'
      },
      className
    )}>
      {children}
    </div>
  );
};
```

## 권한 관리 표준

### 사용자 타입 및 권한 정의
```typescript
enum UserType {
  INDIVIDUAL = 'individual',    // 개인 사용자
  COMPANY = 'company',         // 기업 사용자
  INSTITUTION = 'institution', // 기관 사용자
  ADMIN = 'admin'             // 관리자
}

interface Permission {
  resource: string;
  action: 'read' | 'write' | 'delete' | 'admin';
  scope?: 'own' | 'all';
}

// 권한 매트릭스
const PERMISSIONS: Record<UserType, Permission[]> = {
  [UserType.INDIVIDUAL]: [
    { resource: 'profile', action: 'read', scope: 'own' },
    { resource: 'profile', action: 'write', scope: 'own' },
    { resource: 'notices', action: 'read' },
    { resource: 'business', action: 'read' }
  ],
  [UserType.COMPANY]: [
    { resource: 'profile', action: 'read', scope: 'own' },
    { resource: 'profile', action: 'write', scope: 'own' },
    { resource: 'applications', action: 'write', scope: 'own' },
    { resource: 'notices', action: 'read' },
    { resource: 'business', action: 'read' }
  ],
  [UserType.INSTITUTION]: [
    { resource: 'profile', action: 'read', scope: 'own' },
    { resource: 'profile', action: 'write', scope: 'own' },
    { resource: 'applications', action: 'read', scope: 'all' },
    { resource: 'notices', action: 'read' },
    { resource: 'business', action: 'read' }
  ],
  [UserType.ADMIN]: [
    { resource: '*', action: 'admin' }
  ]
};
```

### 권한 체크 구현
```typescript
// AuthContext 구현
const AuthContext = createContext({
  user: null,
  permissions: [],
  hasPermission: (resource: string, action: string) => boolean,
  login: (credentials) => Promise<void>,
  logout: () => void
});

// useAuth 훅
const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// 권한 체크 함수
const hasPermission = (
  userPermissions: Permission[],
  resource: string,
  action: string
): boolean => {
  return userPermissions.some(permission =>
    (permission.resource === resource || permission.resource === '*') &&
    (permission.action === action || permission.action === 'admin')
  );
};
```

### 라우트 보호 컴포넌트
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: {
    resource: string;
    action: string;
  };
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  fallback = <Navigate to="/login" />
}) => {
  const { user, hasPermission } = useAuth();

  if (!user) {
    return fallback;
  }

  if (requiredPermission &&
      !hasPermission(requiredPermission.resource, requiredPermission.action)) {
    return <div>접근 권한이 없습니다.</div>;
  }

  return <>{children}</>;
};
```

### 권한별 접근 제어 규칙
- **공개 접근**: 사업소개 조회, 공지사항 조회
- **로그인 필요**: 프로필 관리, 대시보드
- **기업 사용자**: 사업신청서 제출, 자사 신청 내역 조회
- **기관 사용자**: 모든 신청서 조회 및 검토
- **관리자**: 모든 기능 접근, 사용자 관리, 시스템 설정

## API 통신 표준

### API 클라이언트 설정
```typescript
// services/apiClient.ts
import axios, { AxiosInstance, AxiosError } from 'axios';

const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 요청 인터셉터 - JWT 토큰 자동 추가
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 응답 인터셉터 - 에러 처리
apiClient.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      // 인증 에러 - 자동 로그아웃
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

### 서비스 레이어 구현 패턴
```typescript
// services/userService.ts
import apiClient from './apiClient';
import { User, CreateUserRequest, UpdateUserRequest } from '@/types/user';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

class UserService {
  private readonly basePath = '/users';

  async getProfile(): Promise<User> {
    const response = await apiClient.get<ApiResponse<User>>(`${this.basePath}/profile`);
    return response.data.data;
  }

  async updateProfile(data: UpdateUserRequest): Promise<User> {
    const response = await apiClient.put<ApiResponse<User>>(
      `${this.basePath}/profile`,
      data
    );
    return response.data.data;
  }

  async getUsers(page = 1, limit = 10): Promise<{ users: User[]; total: number }> {
    const response = await apiClient.get<ApiResponse<{ users: User[]; total: number }>>(
      `${this.basePath}?page=${page}&limit=${limit}`
    );
    return response.data.data;
  }

  async createUser(data: CreateUserRequest): Promise<User> {
    const response = await apiClient.post<ApiResponse<User>>(this.basePath, data);
    return response.data.data;
  }

  async deleteUser(id: number): Promise<void> {
    await apiClient.delete(`${this.basePath}/${id}`);
  }
}

export const userService = new UserService();
```

### React Query 통합
```typescript
// hooks/useUsers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/userService';

export const useUserProfile = () => {
  return useQuery({
    queryKey: ['user', 'profile'],
    queryFn: () => userService.getProfile(),
    staleTime: 5 * 60 * 1000, // 5분
    cacheTime: 10 * 60 * 1000  // 10분
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userService.updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', 'profile'] });
    }
  });
};

export const useUsers = (page: number, limit: number) => {
  return useQuery({
    queryKey: ['users', page, limit],
    queryFn: () => userService.getUsers(page, limit),
    keepPreviousData: true
  });
};
```

### 에러 처리 규칙
```typescript
// utils/errorHandler.ts
export const handleApiError = (error: AxiosError) => {
  if (error.response) {
    // 서버 응답 에러
    const status = error.response.status;
    const message = error.response.data?.message || '서버 오류가 발생했습니다.';

    switch (status) {
      case 400:
        return '잘못된 요청입니다.';
      case 401:
        return '인증이 필요합니다.';
      case 403:
        return '접근 권한이 없습니다.';
      case 404:
        return '요청한 리소스를 찾을 수 없습니다.';
      case 500:
        return '서버 내부 오류가 발생했습니다.';
      default:
        return message;
    }
  } else if (error.request) {
    // 네트워크 에러
    return '네트워크 연결을 확인해주세요.';
  } else {
    // 기타 에러
    return '알 수 없는 오류가 발생했습니다.';
  }
};
```

## 작업 관리 시스템 (AI Agent 전용)

### 매 실행 사이클 자동화 코드
```typescript
// 매 실행 사이클마다 실행되는 작업 관리 로직
const 작업관리_자동화 = async () => {
  // 현재 상태 확인
  const 현재_상태 = await list_tasks({ status: "all" });

  // 실행 가능한 작업들 식별
  const 실행가능_작업들 = 현재_상태.tasks.filter(task =>
    task.status === "pending" &&
    task.dependencies.every(dep =>
      현재_상태.tasks.find(t => t.name === dep)?.status === "completed"
    )
  );

  // 블로킹된 작업들 확인
  const 블로킹된_작업들 = await query_task("blocked OR dependencies");

  // 문제 상황 자동 감지 및 해결
  if (블로킹된_작업들.length > 0) {
    await 자동_의존성_해결();
    await 우선순위_재조정();
  }

  // 다음 실행할 작업 결정
  const 다음_작업 = 실행가능_작업들
    .sort((a, b) => a.priority - b.priority)[0];

  if (다음_작업) {
    console.log(`다음 실행 작업: ${다음_작업.name}`);
    return 다음_작업;
  }

  return null;
};

// 의존성 자동 해결 함수
const 자동_의존성_해결 = async () => {
  const 모든_작업 = await list_tasks({ status: "all" });

  for (const task of 모든_작업.tasks) {
    if (task.status === "pending") {
      // 순환 의존성 검사
      const 순환_의존성 = 순환의존성_검사(task, 모든_작업.tasks);
      if (순환_의존성) {
        console.warn(`순환 의존성 발견: ${task.name}`);
        // 순환 의존성 해결 로직
      }

      // 누락된 의존성 검사
      const 누락_의존성 = task.dependencies.filter(dep =>
        !모든_작업.tasks.find(t => t.name === dep)
      );

      if (누락_의존성.length > 0) {
        console.warn(`누락된 의존성: ${누락_의존성.join(', ')}`);
        // 누락된 의존성 처리 로직
      }
    }
  }
};

// 우선순위 재조정 함수
const 우선순위_재조정 = async () => {
  const 현재_작업들 = await list_tasks({ status: "pending" });

  // 크리티컬 패스 계산
  const 크리티컬_패스 = 계산_크리티컬_패스(현재_작업들.tasks);

  // 우선순위 업데이트
  for (const task of 크리티컬_패스) {
    await update_task({
      taskId: task.id,
      priority: "high"
    });
  }
};
```

### 작업 진행 상황 모니터링
```typescript
// 작업 진행률 계산
const 계산_진행률 = (tasks: Task[]) => {
  const 전체_작업수 = tasks.length;
  const 완료_작업수 = tasks.filter(t => t.status === "completed").length;
  const 진행중_작업수 = tasks.filter(t => t.status === "in_progress").length;

  return {
    전체: 전체_작업수,
    완료: 완료_작업수,
    진행중: 진행중_작업수,
    진행률: Math.round((완료_작업수 / 전체_작업수) * 100)
  };
};

// 예상 완료 시간 계산
const 계산_예상완료시간 = (tasks: Task[]) => {
  const 남은_작업들 = tasks.filter(t => t.status === "pending");
  const 평균_작업시간 = 4; // 일 단위

  return 남은_작업들.length * 평균_작업시간;
};
```

## 파일 간 상호작용 규칙

### 동시 수정 필수 파일
- **새 페이지 추가 시**: `src/App.tsx` 라우팅 설정 업데이트
- **새 API 서비스 추가 시**: `services/index.ts` export 추가
- **새 상수 추가 시**: `constants/index.ts` export 추가
- **새 타입 추가 시**: `types/index.ts` export 추가
- **새 유틸리티 함수 추가 시**: `utils/index.ts` export 추가

### 컴포넌트 의존성 관리
- **상위 컴포넌트 변경 시**: 하위 컴포넌트 props 인터페이스 확인
- **공통 컴포넌트 수정 시**: 사용하는 모든 페이지에서 타입 검사
- **Context 변경 시**: Provider와 Consumer 모두 타입 안정성 확인
- **훅 변경 시**: 해당 훅을 사용하는 모든 컴포넌트 검증

## 특수 기능 구현 가이드

### React Flow 에코시스템 맵 구현
```typescript
// React Flow 기본 설정
import ReactFlow, {
  Node,
  Edge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  NodeTypes,
  EdgeTypes
} from 'reactflow';
import 'reactflow/dist/style.css';

// 커스텀 노드 타입 정의
interface CompanyNodeData {
  label: string;
  type: 'company' | 'institution';
  size: 'startup' | 'small' | 'medium' | 'large';
  sector: string;
}

// 커스텀 노드 컴포넌트
const CompanyNode: React.FC<NodeProps<CompanyNodeData>> = ({ data }) => {
  return (
    <div className={cn(
      "px-4 py-2 rounded-lg border-2 bg-white shadow-md",
      data.type === 'company' ? 'border-etri-blue' : 'border-etri-orange'
    )}>
      <div className="font-semibold text-sm">{data.label}</div>
      <div className="text-xs text-gray-500">{data.sector}</div>
    </div>
  );
};

// 에코시스템 맵 컴포넌트
const EcosystemMap: React.FC<EcosystemMapProps> = ({
  initialNodes = [],
  initialEdges = []
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const nodeTypes: NodeTypes = {
    company: CompanyNode,
    institution: CompanyNode,
  };

  return (
    <div className="w-full h-[600px] border rounded-lg">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  );
};
```

### PDF 문서 생성 서비스
```python
# backend/app/services/document_service.py
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
import io

class DocumentService:
    @staticmethod
    def generate_application_pdf(application_data: dict) -> bytes:
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=A4)

        # PDF 내용 생성
        p.drawString(100, 750, f"사업신청서")
        p.drawString(100, 700, f"회사명: {application_data['company_name']}")
        p.drawString(100, 650, f"프로젝트: {application_data['project_title']}")

        p.save()
        buffer.seek(0)
        return buffer.getvalue()
```

### 파일 업로드 시스템
```typescript
// 파일 업로드 훅
const useFileUpload = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const uploadFiles = async (files: File[]) => {
    setUploading(true);
    for (const file of files) {
      await uploadFileWithProgress(file, setProgress);
    }
    setUploading(false);
  };

  return { files, uploading, progress, uploadFiles };
};

// 파일 업로드 컴포넌트
const FileUpload: React.FC<FileUploadProps> = ({ onUpload, maxFiles = 5 }) => {
  const { files, uploading, progress, uploadFiles } = useFileUpload();

  return (
    <div className="space-y-4">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <input
          type="file"
          multiple
          onChange={(e) => uploadFiles(Array.from(e.target.files || []))}
          className="hidden"
          id="file-upload"
        />
        <label htmlFor="file-upload" className="cursor-pointer">
          파일을 선택하거나 드래그하세요
        </label>
      </div>
      {uploading && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
};
```

## 금지 사항

### 절대 금지
- **직접적인 DOM 조작**: `document.getElementById`, `querySelector` 등 사용 금지
- **인라인 스타일 사용**: `style` 속성 대신 TailwindCSS 클래스 사용
- **하드코딩된 값**: API 엔드포인트, 설정값은 환경변수 사용
- **권한 체크 없는 민감한 기능**: 모든 보호된 기능에 권한 검증 필수
- **반응형 고려 없는 UI**: 모든 컴포넌트는 반응형 설계 필수
- **console.log 프로덕션 코드**: 개발용 로그는 배포 전 제거
- **any 타입 남용**: TypeScript의 타입 안정성 훼손 금지

### 제한적 사용 (주의 필요)
- **useEffect 의존성 배열 생략**: 무한 루프 위험
- **전역 변수 선언**: Context API 또는 상태 관리 라이브러리 사용
- **중첩된 삼항 연산자**: 3단계 이상 금지, 조건문 사용
- **깊은 객체 중첩**: 5단계 이상 중첩 구조 지양

## 성능 최적화 가이드

### 코드 분할 및 지연 로딩
```typescript
// 페이지별 코드 분할
const Dashboard = lazy(() => import('./pages/Dashboard'));
const AdminPanel = lazy(() => import('./pages/AdminPanel'));
const EcosystemMap = lazy(() => import('./components/EcosystemMap'));

// 컴포넌트 메모이제이션
const ExpensiveChart = memo(({ data }: { data: ChartData }) => {
  const processedData = useMemo(() =>
    processChartData(data), [data]
  );

  const handleClick = useCallback((event: MouseEvent) => {
    // 이벤트 처리 로직
  }, []);

  return <Chart data={processedData} onClick={handleClick} />;
});

// 이미지 지연 로딩
const LazyImage: React.FC<LazyImageProps> = ({ src, alt, ...props }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsInView(true);
        observer.disconnect();
      }
    });

    if (imgRef.current) observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, []);

  return (
    <img
      ref={imgRef}
      src={isInView ? src : '/placeholder.jpg'}
      alt={alt}
      onLoad={() => setIsLoaded(true)}
      className={cn('transition-opacity', isLoaded ? 'opacity-100' : 'opacity-0')}
      {...props}
    />
  );
};
```

### 접근성 구현
```typescript
// 접근성을 고려한 버튼 컴포넌트
const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  onClick,
  children,
  disabled = false,
  ...props
}) => {
  const handleKeyDown = (e: KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <button
      onClick={onClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      aria-label={props['aria-label']}
      className={cn(
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        props.className
      )}
      {...props}
    >
      {children}
    </button>
  );
};
```

## AI 의사결정 가이드라인

### 기술 선택 우선순위
1. **UI 컴포넌트**: ShadcnUI > TailwindCSS > 커스텀 CSS
2. **상태 관리**: Context API + React Query > Redux Toolkit > Zustand
3. **스타일링**: TailwindCSS > CSS Modules > Styled Components
4. **타입 시스템**: TypeScript 인터페이스 > PropTypes
5. **데이터 페칭**: React Query > SWR > 직접 fetch
6. **폼 관리**: React Hook Form > Formik > 직접 구현

### 애매한 상황 처리 규칙
- **디자인 명세 불명확 시**: ShadcnUI 기본 디자인 + 모바일 우선 반응형 적용
- **권한 요구사항 불명확 시**: 보수적 접근 (최소 권한 원칙)
- **성능 최적화 필요 시**: React.memo, useMemo, useCallback 적극 활용
- **에러 처리 방법 불명확 시**: 사용자 친화적 메시지 + 개발자 로그 기록
- **API 응답 구조 불명확 시**: 타입 가드 함수로 런타임 검증

### 코드 품질 기준
- **함수 길이**: 50줄 이하 (복잡한 로직은 분리)
- **컴포넌트 복잡도**: Props 10개 이하, 상태 5개 이하
- **파일 크기**: 300줄 이하 (초과 시 모듈 분리)
- **중첩 깊이**: JSX 4단계, 로직 3단계 이하
- **순환 복잡도**: 함수당 10 이하

### 개발 단계별 우선순위
**1차 개발 (핵심 기능)**:
- 기본 CRUD 기능 완성도 우선
- 사용자 인증 및 권한 시스템 안정성
- 핵심 비즈니스 로직 정확성

**2차 개발 (고급 기능)**:
- 데이터 시각화 및 인터랙션
- 파일 업로드 및 문서 생성
- 실시간 데이터 업데이트

**3차 개발 (최적화)**:
- 성능 최적화 및 번들 크기 최적화
- 접근성 및 사용성 개선
- 보안 강화 및 테스트 커버리지

## 백엔드 개발 표준

### FastAPI 애플리케이션 구조
```python
# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, users, business, notices, demands, valley, statistics

app = FastAPI(title="딥테크 오픈플랫폼 API", version="1.0.0")

# CORS 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 프론트엔드 URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API 라우터 등록
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
# ... 기타 라우터들
```

### 데이터베이스 모델 예시
```python
# models/user.py
from sqlalchemy import Column, Integer, String, Enum, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from enum import Enum as PyEnum
import datetime

Base = declarative_base()

class UserType(PyEnum):
    INDIVIDUAL = "individual"
    COMPANY = "company"
    INSTITUTION = "institution"
    ADMIN = "admin"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    user_type = Column(Enum(UserType), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
```

### API 엔드포인트 구현 패턴
```python
# api/users.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import get_current_user
from app.schemas.user import UserCreate, UserResponse
from app.services.user_service import UserService

router = APIRouter()

@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """사용자 생성"""
    try:
        user_service = UserService(db)
        user = await user_service.create_user(user_data)
        return user
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """현재 사용자 프로필 조회"""
    return current_user
```

## 테스트 전략

### 프론트엔드 테스트
```typescript
// __tests__/components/UserProfile.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import UserProfile from '@/components/UserProfile';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: { queries: { retry: false } }
});

describe('UserProfile', () => {
  it('사용자 정보를 올바르게 표시한다', () => {
    const queryClient = createTestQueryClient();
    const mockUser = { id: 1, email: '<EMAIL>', name: 'Test User' };

    render(
      <QueryClientProvider client={queryClient}>
        <UserProfile user={mockUser} />
      </QueryClientProvider>
    );

    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});
```

### 백엔드 테스트
```python
# tests/test_users.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_user():
    """사용자 생성 테스트"""
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword",
        "user_type": "individual"
    }

    response = client.post("/api/v1/users/", json=user_data)
    assert response.status_code == 201
    assert response.json()["email"] == "<EMAIL>"

def test_get_user_profile():
    """사용자 프로필 조회 테스트"""
    # 로그인 후 토큰 획득
    login_response = client.post("/api/v1/auth/login", data={
        "username": "<EMAIL>",
        "password": "testpassword"
    })
    token = login_response.json()["access_token"]

    # 프로필 조회
    response = client.get(
        "/api/v1/users/profile",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
```

## 개발 후 문서 업데이트 가이드

### 📋 필수 업데이트 문서 목록

#### 🔧 기능 개발 완료 시
```typescript
// 매 작업 완료 후 업데이트해야 할 문서들
const 필수_업데이트_문서 = {
  "API 개발 완료": [
    "docs/api_specification.md",     // API 엔드포인트 추가
    "backend/README.md",             // 설치 및 실행 가이드
    "docs/tech_spec.md"              // 기술 명세 업데이트
  ],
  "UI 컴포넌트 완료": [
    "docs/ui_design_spec.md",        // 컴포넌트 가이드 추가
    "frontend/src/components/README.md", // 컴포넌트 사용법
    "docs/tech_spec.md"              // 프론트엔드 구조 업데이트
  ],
  "데이터베이스 변경": [
    "docs/tech_spec.md",             // 데이터베이스 스키마
    "backend/alembic/README.md",     // 마이그레이션 가이드
    "docs/api_specification.md"      // 관련 API 업데이트
  ],
  "배포 설정 변경": [
    "docs/deployment_guide.md",      // 배포 가이드
    "docker-compose.yml",            // 컨테이너 설정
    "README.md"                      // 프로젝트 루트 가이드
  ]
};
```

#### 📊 작업 완료 시 자동 업데이트
```typescript
// Shrimp 작업 완료 시 자동 실행
const 작업완료_문서업데이트 = async (completedTask: Task) => {
  // 1. 작업 상태 업데이트
  await update_task({
    taskId: completedTask.id,
    status: "completed",
    completedAt: new Date().toISOString()
  });

  // 2. 관련 문서 자동 업데이트
  const 관련문서들 = 식별_관련문서(completedTask.type);
  for (const 문서 of 관련문서들) {
    await 자동_문서_업데이트(문서, completedTask);
  }

  // 3. 진행률 재계산
  const 전체진행률 = await 계산_전체진행률();
  await 업데이트_프로젝트_상태(전체진행률);
};
```

### 📝 문서 업데이트 체크리스트

#### ✅ API 개발 완료 시
- [ ] **API 명세서 업데이트** (`docs/api_specification.md`)
  - 새로운 엔드포인트 추가
  - 요청/응답 스키마 정의
  - 에러 코드 및 메시지 추가
  - 인증 요구사항 명시

- [ ] **Swagger 문서 확인**
  - FastAPI 자동 생성 문서 검증
  - 예제 데이터 추가
  - 태그 및 설명 보완

#### ✅ UI 컴포넌트 완료 시
- [ ] **UI 설계 명세서 업데이트** (`docs/ui_design_spec.md`)
  - 새로운 컴포넌트 가이드 추가
  - Props 인터페이스 문서화
  - 사용 예제 코드 추가
  - 스크린샷 또는 Figma 링크

- [ ] **컴포넌트 README 작성**
  - 컴포넌트 목적 및 기능 설명
  - 설치 및 import 방법
  - Props 및 이벤트 목록
  - 접근성 고려사항

#### ✅ 데이터베이스 변경 시
- [ ] **기술 명세서 업데이트** (`docs/tech_spec.md`)
  - ERD 다이어그램 업데이트
  - 테이블 스키마 변경사항
  - 인덱스 및 제약조건 추가

- [ ] **마이그레이션 가이드 작성**
  - 마이그레이션 실행 방법
  - 롤백 절차
  - 데이터 백업 권장사항

### 🔄 자동화된 문서 업데이트

#### 📊 진행률 자동 반영
```typescript
// 작업 완료 시 자동으로 문서 업데이트
const 자동_진행률_업데이트 = async () => {
  const 현재상태 = await list_tasks({ status: "all" });
  const 진행률 = 계산_진행률(현재상태.tasks);

  // 여러 문서에 진행률 자동 반영
  const 업데이트할_문서들 = [
    "docs/project_summary.md",
    "docs/task_status_tracking.md",
    "README.md"
  ];

  for (const 문서 of 업데이트할_문서들) {
    await 진행률_섹션_업데이트(문서, 진행률);
  }
};
```

#### 📅 마일스톤 자동 체크
```typescript
// 단계별 완료 시 마일스톤 문서 업데이트
const 마일스톤_체크 = async (단계: "1차" | "2차" | "3차") => {
  const 완료된_작업들 = await query_task(`stage:${단계} AND status:completed`);
  const 단계별_작업수 = get단계별_총작업수(단계);

  if (완료된_작업들.length === 단계별_작업수) {
    // 마일스톤 달성 시 문서 업데이트
    await 마일스톤_달성_문서_업데이트(단계);
    await 다음단계_준비_문서_생성(단계);
  }
};
```

### 📋 문서 품질 관리

#### 🔍 문서 일관성 검사
```typescript
// 정기적인 문서 일관성 검사
const 문서_일관성_검사 = async () => {
  const 검사항목들 = [
    "프로젝트명_일치성",
    "작업수_정확성",
    "진행률_일치성",
    "기술스택_통일성",
    "API_URL_일관성"
  ];

  for (const 항목 of 검사항목들) {
    const 결과 = await 검사_실행(항목);
    if (!결과.통과) {
      console.warn(`문서 불일치 발견: ${항목} - ${결과.세부사항}`);
      await 자동_수정_시도(항목, 결과);
    }
  }
};
```

#### 📝 문서 템플릿 활용
```markdown
<!-- 새로운 기능 문서 템플릿 -->
# [기능명] 구현 가이드

## 개요
- **목적**:
- **구현 일자**:
- **담당자**:
- **관련 작업**:

## 기술 사양
- **사용 기술**:
- **의존성**:
- **성능 요구사항**:

## 구현 세부사항
### 백엔드
- API 엔드포인트
- 데이터베이스 변경사항
- 비즈니스 로직

### 프론트엔드
- 컴포넌트 구조
- 상태 관리
- 사용자 인터페이스

## 테스트
- 단위 테스트
- 통합 테스트
- 사용자 시나리오

## 배포 고려사항
- 환경 설정
- 마이그레이션
- 모니터링
```

## 배포 및 운영

### 환경 설정
```bash
# .env.production
REACT_APP_API_URL=https://api.deeptech-valley.com
REACT_APP_ENVIRONMENT=production

# backend/.env
DATABASE_URL=postgresql://user:password@localhost/deeptech_valley
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### Docker 설정
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]

# backend/Dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

---

## 📋 작업별 참고 문서 가이드

### 🎯 **필수 참고 문서**
모든 Shrimp 작업 수행 시 `docs/shrimp_reference_guide.md`를 먼저 확인하여 해당 작업에 필요한 참고 문서들을 파악하세요.

#### 📚 **주요 참고 문서 목록**
1. **shrimp-rules.md** (본 문서) - 개발 표준 및 코딩 규칙
2. **updated-ui-design-spec.md** - 최신 UI/UX 디자인 명세서 (ETRI 기반)
3. **tech_spec.md** - 기술 아키텍처 및 스택 정의
4. **api_specification.md** - RESTful API 설계 문서
5. **database_design.md** - 데이터베이스 설계서 (미정 상태)
6. **sqlalchemy_models.md** - SQLAlchemy 모델 구현 가이드

#### ⚠️ **중요 주의사항**
- **데이터베이스 미정**: PostgreSQL 기준 문서이나 변경 가능성 있음
- **UI 디자인 최신화**: `updated-ui-design-spec.md`가 최신 버전
- **문서 우선순위**: 충돌 시 본 문서(shrimp-rules.md) 우선
- **개발 시작 금지**: 명시적 허가 전까지 개발 시작 금지

### 🔄 **작업 시작 전 필수 체크**
```typescript
// 작업 시작 전 체크리스트
const 작업시작_체크리스트 = async (taskName: string) => {
  // 1. 참고 문서 가이드 확인
  const 참고문서들 = await 확인_필수참고문서(taskName);

  // 2. 최신 UI 디자인 명세 확인
  const UI설계 = await 확인_UI_디자인_명세(taskName);

  // 3. 데이터베이스 상태 확인
  const DB상태 = "미정"; // PostgreSQL 기준이나 변경 가능

  // 4. 문서 충돌 검사
  const 충돌사항 = await 검사_문서_충돌(참고문서들);

  // 5. 개발 시작 허가 확인
  if (!개발시작_허가) {
    throw new Error("개발 시작 전 사용자 확인 필요");
  }

  return { 참고문서들, UI설계, DB상태, 충돌사항 };
};
```

### ✅ **작업 완료 전 필수 검증**
```typescript
// 작업 완료 전 엄격한 검증 체크리스트
const 작업완료_검증 = async (taskName: string, 구현결과: any) => {
  console.log("🔍 작업 완료 검증 시작:", taskName);

  // 1. 코드 오류 검사 (최우선)
  const 코드오류 = await 검사_코드_오류();
  if (코드오류.length > 0) {
    console.error("❌ 코드 오류 발견:", 코드오류);
    throw new Error(`코드 오류 수정 필요: ${코드오류.join(', ')}`);
  }

  // 2. 컴파일/빌드 검사
  const 빌드결과 = await 검사_빌드_성공();
  if (!빌드결과.성공) {
    console.error("❌ 빌드 실패:", 빌드결과.오류);
    throw new Error(`빌드 오류 수정 필요: ${빌드결과.오류}`);
  }

  // 3. 개발 서버 실행 검사
  const 서버상태 = await 검사_서버_실행();
  if (!서버상태.정상실행) {
    console.error("❌ 서버 실행 실패:", 서버상태.오류);
    throw new Error(`서버 실행 오류 수정 필요: ${서버상태.오류}`);
  }

  // 4. 기능 동작 검사
  const 기능테스트 = await 검사_기능_동작(taskName);
  if (!기능테스트.통과) {
    console.error("❌ 기능 테스트 실패:", 기능테스트.실패항목);
    throw new Error(`기능 오류 수정 필요: ${기능테스트.실패항목.join(', ')}`);
  }

  // 5. 코딩 표준 준수 검사
  const 표준준수 = await 검사_코딩_표준(taskName);
  if (!표준준수.통과) {
    console.warn("⚠️ 코딩 표준 위반:", 표준준수.위반항목);
    // 경고는 표시하지만 작업 완료는 허용
  }

  // 6. 문서 업데이트 검사
  const 문서업데이트 = await 검사_문서_업데이트(taskName);
  if (!문서업데이트.완료) {
    console.error("❌ 문서 업데이트 누락:", 문서업데이트.누락항목);
    throw new Error(`문서 업데이트 필요: ${문서업데이트.누락항목.join(', ')}`);
  }

  console.log("✅ 모든 검증 통과 - 작업 완료 승인");
  return true;
};

// 개별 검증 함수들
const 검사_코드_오류 = async () => {
  const 오류목록 = [];

  // TypeScript 컴파일 오류 검사
  const tsErrors = await 실행_명령어("npx tsc --noEmit");
  if (tsErrors.exitCode !== 0) {
    오류목록.push("TypeScript 컴파일 오류");
  }

  // ESLint 오류 검사
  const lintErrors = await 실행_명령어("npx eslint src --ext .ts,.tsx");
  if (lintErrors.exitCode !== 0) {
    오류목록.push("ESLint 오류");
  }

  // 런타임 오류 검사 (콘솔 에러 확인)
  const 콘솔오류 = await 확인_콘솔_에러();
  if (콘솔오류.length > 0) {
    오류목록.push(...콘솔오류);
  }

  return 오류목록;
};

const 검사_빌드_성공 = async () => {
  try {
    // 프론트엔드 빌드 테스트
    const 빌드결과 = await 실행_명령어("npm run build");
    return { 성공: 빌드결과.exitCode === 0, 오류: 빌드결과.stderr };
  } catch (error) {
    return { 성공: false, 오류: error.message };
  }
};

const 검사_서버_실행 = async () => {
  try {
    // 개발 서버 실행 상태 확인
    const 서버응답 = await fetch("http://localhost:5173");
    return { 정상실행: 서버응답.ok, 오류: null };
  } catch (error) {
    return { 정상실행: false, 오류: error.message };
  }
};
```

### 🚫 **작업 완료 금지 조건**
```typescript
// 다음 조건 중 하나라도 해당하면 작업 완료 금지
const 작업완료_금지조건 = [
  "TypeScript 컴파일 오류 존재",
  "ESLint 오류 존재",
  "런타임 에러 발생",
  "빌드 실패",
  "개발 서버 실행 실패",
  "핵심 기능 동작 실패",
  "필수 문서 업데이트 누락",
  "보안 취약점 발견",
  "성능 기준 미달성"
];

// 작업 완료 전 필수 확인사항
const 작업완료_필수확인 = {
  "코드 품질": {
    "컴파일 성공": "필수",
    "린트 통과": "필수",
    "런타임 오류 없음": "필수"
  },
  "기능 동작": {
    "핵심 기능 정상 동작": "필수",
    "UI 렌더링 정상": "필수",
    "API 호출 성공": "해당시 필수"
  },
  "개발 환경": {
    "개발 서버 실행": "필수",
    "빌드 성공": "필수",
    "의존성 설치 완료": "필수"
  },
  "문서화": {
    "README 업데이트": "필수",
    "API 문서 업데이트": "해당시 필수",
    "변경사항 기록": "필수"
  }
};
```

## 요약

이 문서는 DeepTech Valley Platform 개발을 위한 포괄적인 표준을 제시합니다. AI Agent는 이 표준을 엄격히 준수하여 일관성 있고 고품질의 코드를 생성해야 합니다.

**핵심 원칙**:
1. **타입 안정성**: TypeScript 적극 활용
2. **컴포넌트 재사용성**: ShadcnUI + 커스텀 컴포넌트
3. **성능 최적화**: 메모이제이션 및 지연 로딩
4. **접근성**: WCAG 2.1 표준 준수
5. **보안**: 권한 기반 접근 제어
6. **테스트**: 단위/통합/E2E 테스트 커버리지
7. **문서 참조**: 작업별 필수 참고 문서 준수
8. **품질 보증**: 오류 없는 코드만 작업 완료 승인

## ⚠️ **중요: 작업 완료 기준**

### 🚫 **절대 작업 완료 금지 상황**
```
❌ TypeScript 컴파일 오류 존재
❌ ESLint 오류 존재
❌ 런타임 에러 발생 (콘솔 에러)
❌ 빌드 실패
❌ 개발 서버 실행 실패
❌ 핵심 기능 동작 실패
❌ 필수 문서 업데이트 누락
❌ 보안 취약점 발견
❌ 터미널 명령어에서 상대경로 사용
```

### ✅ **작업 완료 승인 조건**
```
✅ 모든 코드 오류 수정 완료
✅ 빌드 성공 확인
✅ 개발 서버 정상 실행
✅ 핵심 기능 정상 동작
✅ 관련 문서 업데이트 완료
✅ 코딩 표준 준수
✅ 다음 작업 진행 가능 상태
```

### 🔍 **작업 완료 전 필수 검증 절차**
1. **코드 오류 검사**: TypeScript, ESLint, 런타임 오류 확인
2. **빌드 테스트**: `npm run build` 성공 확인
3. **서버 실행 테스트**: 개발 서버 정상 실행 확인
4. **기능 동작 테스트**: 구현된 기능 정상 동작 확인
5. **문서 업데이트**: README, API 문서 등 관련 문서 업데이트
6. **표준 준수 확인**: shrimp-rules.md 표준 준수 여부 확인

**⚠️ 위 조건을 모두 만족해야만 작업 완료 처리 가능**

## 🖥️ **터미널 사용 규칙 (필수 준수)**

### 📍 **절대경로 사용 원칙**
```typescript
// ✅ 올바른 터미널 경로 사용 (절대경로)
const 올바른_터미널_사용 = {
  "launch-process": {
    cwd: "d:\\gayun\\2025\\deeptech-valley-platform\\frontend",  // 절대경로 필수
    command: "npm run dev"
  },
  "작업_디렉토리": "항상 절대경로로 지정",
  "경로_형식": "Windows: d:\\path\\to\\dir, Unix: /path/to/dir"
};

// ❌ 잘못된 터미널 경로 사용 (상대경로)
const 잘못된_터미널_사용 = {
  "launch-process": {
    cwd: "./frontend",           // ❌ 상대경로 금지
    cwd: "../backend",           // ❌ 상대경로 금지
    cwd: "frontend/src",         // ❌ 상대경로 금지
  }
};
```

### 🚫 **터미널 사용 금지 사항**
```
❌ 상대경로 사용 (./, ../, 등)
❌ 현재 디렉토리 기준 경로 (frontend, backend 등)
❌ 환경변수 없이 동적 경로 사용
❌ 경로 구분자 혼용 (Windows에서 / 사용 등)
```

### ✅ **터미널 사용 필수 규칙**
```
✅ 항상 절대경로 사용
✅ 운영체제별 경로 구분자 준수
✅ 경로 존재 여부 사전 확인
✅ 명령어 실행 전 작업 디렉토리 검증
```

### 📋 **터미널 명령어 실행 템플릿**
```typescript
// 표준 터미널 명령어 실행 패턴
const 터미널_실행_템플릿 = async (명령어: string, 대상디렉토리: string) => {
  // 1. 절대경로 검증
  if (!path.isAbsolute(대상디렉토리)) {
    throw new Error("❌ 절대경로만 사용 가능합니다");
  }

  // 2. 디렉토리 존재 확인
  if (!fs.existsSync(대상디렉토리)) {
    throw new Error("❌ 대상 디렉토리가 존재하지 않습니다");
  }

  // 3. 명령어 실행
  return await launch_process({
    command: 명령어,
    cwd: 대상디렉토리,  // 절대경로
    wait: true,
    max_wait_seconds: 120
  });
};

// 사용 예시
await 터미널_실행_템플릿(
  "npm install",
  "d:\\gayun\\2025\\deeptech-valley-platform\\frontend"
);
```

### 🔍 **터미널 경로 검증 체크리스트**
```
작업 완료 전 필수 확인:
□ 모든 launch-process 호출에서 절대경로 사용
□ cwd 매개변수에 상대경로 없음
□ 경로 구분자 올바르게 사용
□ 존재하지 않는 경로로 명령어 실행 시도 없음
□ 터미널 명령어 실행 오류 없음
```

### ⚠️ **터미널 경로 위반 시 처리**
- **발견 즉시**: 작업 중단 및 경로 수정
- **작업 완료 불가**: 상대경로 사용 시 절대 작업 완료 금지
- **문서 업데이트**: 올바른 경로 사용법 기록
