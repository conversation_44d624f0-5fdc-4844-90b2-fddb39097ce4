# DeepTech Valley Platform PRD (Product Requirements Document)

## 1. 프로젝트 개요

### 1.1 프로젝트 목적
밸리 내 기업 소통, 공모, 선정을 지원하는 통합 오픈플랫폼 개발

### 1.2 프로젝트 정보
- **프로젝트명**: DeepTech Valley Platform
- **개발 기간**: 2025.06.17 ~ 2025.09.15 (3개월)
- **개발 방식**: 3단계 순차 개발
- **플랫폼 유형**: 반응형 웹 애플리케이션
- **주요 사용자**: 기업, 기관, 개인, 관리자

### 1.3 핵심 가치 제안
- **원스톱 서비스**: 정보 검색부터 지원서 제출까지 통합 관리
- **투명한 프로세스**: 실시간 진행상황 추적 및 공개
- **네트워킹 허브**: 딥테크 생태계 참여자 간 연결
- **데이터 기반 인사이트**: 지원 통계 및 트렌드 분석

## 2. 사용자 정의

### 2.1 주요 사용자 그룹
1. **기업 사용자** (딥테크 스타트업, 중견기업)
   - 공모사업 정보 탐색
   - 지원서 작성 및 제출
   - 진행상황 추적

2. **기관 사용자** (연구기관, 정부기관)
   - 기술 이전 및 협력 파트너 탐색
   - 정책 정보 공유

3. **개인 사용자** (연구자, 전문가)
   - 기술 정보 열람
   - 네트워킹 참여

4. **관리자**
   - 플랫폼 운영 관리
   - 사용자 권한 설정
   - 통계 분석

### 2.2 사용자 여정
#### 기업 사용자 여정
1. 회원가입 → 2. 프로필 설정 → 3. 공모사업 탐색 → 4. 지원서 작성 → 5. 제출 → 6. 진행상황 추적

#### 관리자 여정
1. 로그인 → 2. 대시보드 확인 → 3. 공모사업 등록 → 4. 지원서 검토 → 5. 결과 발표 → 6. 통계 분석

## 3. 기능 요구사항

### 3.0 메인 페이지 (에코시스템 맵)
#### 3.0.1 에코시스템 맵 시각화
- **기업 관계도 기반**: 참여 기업/기관 간의 관계를 시각적으로 표현
- **인터랙티브 맵**: 클릭, 드래그, 줌 기능 지원
- **애니메이션 효과**:
  - 페이지 로드 시 노드 등장 애니메이션
  - 연결선 그리기 애니메이션
  - 호버 시 부드러운 전환 효과

#### 3.0.2 상호작용 기능
- **호버 효과**:
  - 기업/기관 노드 호버 시 관련 정보 툴팁 표시
  - 연결된 관계 하이라이트
  - 관련 기업 목록 사이드 패널 노출
- **클릭 이벤트**:
  - 기업 클릭 시 상세 페이지로 이동
  - 관계선 클릭 시 협력 내역 표시

#### 3.0.3 하단 통계 지표 시각화
- **아이콘 + 숫자 형태**:
  - 등록 기업 수
  - 등록 기술 수
  - 진행 중인 프로젝트 수
  - 완료된 프로젝트 수
- **실시간 업데이트**: 데이터 변경 시 자동 갱신
- **애니메이션**: 숫자 카운트업 효과

### 3.1 회원관리 모듈
#### 3.1.1 사용자 등록/인증
- 다중 사용자 타입 지원 (기업/기관/개인)
- 이메일 인증 기반 회원가입
- 소셜 로그인 연동 (선택사항)
- 비밀번호 재설정 기능

#### 3.1.2 프로필 관리 (향후 구체화 예정)
- 사용자 타입별 맞춤 프로필 양식
- 기업 정보 (사업자등록번호, 업종, 규모) - 상세 항목 추후 정의
- 기관 정보 (기관 유형, 전문 분야) - 상세 항목 추후 정의
- 개인 정보 (전문 분야, 경력) - 상세 항목 추후 정의
- **참고**: 회원 정보 구조는 실제 운영 요구사항에 따라 구체화

#### 3.1.3 권한 관리
- 역할 기반 접근 제어 (RBAC)
- 관리자 권한 설정
- 페이지별 접근 권한 관리

### 3.2 사업소개 모듈
#### 3.2.1 메뉴 구성
- **사업소개** > **기술소개** > **기술가이드**, **기술 소스코드**
- 계층적 네비게이션 구조

#### 3.2.2 사업 정보 관리 (공개)
- 사업 개요 및 목표
- 참여 기관 정보
- 일반 사업 소개 콘텐츠

#### 3.2.3 기술소개 (권한 제한)
- **열람 권한**: 관리자가 권한을 부여한 회원만 접근 가능
- 핵심 기술 상세 정보
- 기술 로드맵 및 발전 방향

#### 3.2.4 기술 가이드 (권한 제한)
- **열람 권한**: 관리자 권한 부여 회원만 이용 가능
- **Wiki 형태**: 편집 가능한 기술 문서
- **트리 구조 게시판**: 계층적 문서 구조
- 기술 문서 업로드/다운로드
- 카테고리별 분류 및 태그 시스템
- 고급 검색 및 필터링

#### 3.2.5 기술 소스코드 브라우징 (권한 제한)
- **열람 권한**: 관리자 권한 부여 회원만 이용 가능
- **소스코드 뷰어**: 문법 하이라이팅, 코드 폴딩
- 파일 트리 네비게이션
- 코드 검색 기능
- 다운로드 권한 세부 관리

### 3.3 공지관리 모듈
#### 3.3.1 단순 게시판 형태
- 공지사항 작성/수정/삭제 (관리자)
- 목록 조회 (모든 사용자)
- 상세 조회 (모든 사용자)

#### 3.3.2 기본 기능
- 제목, 내용, 작성일, 조회수
- 중요 공지 상단 고정
- 첨부파일 업로드/다운로드

#### 3.3.3 검색 및 필터링
- 키워드 검색 (제목, 내용)
- 날짜 범위 필터
- 페이지네이션

### 3.4 밸리관리 모듈
#### 3.4.1 에코시스템 시각화
- 참여 기관 관계도 (단순 버전)
- 기관별 정보 표시
- 상호작용 가능한 그래프

#### 3.4.2 참여 기업 정보 관리
- 기업 목록 및 상세 정보
- 기업별 참여 이력
- 연락처 정보

#### 3.4.3 통계 대시보드
- 참여 기업 수 통계
- 지원 현황 통계
- 월별/분기별 트렌드

### 3.5 수요관리 모듈
#### 3.5.1 공모사업 관리 (관리자)
- 공모사업 등록/수정/삭제
- 지원 자격 요건 설정
- 일정 관리 및 마감일 설정

#### 3.5.2 사업신청 시스템 (폼 기반)
- **입력 방식**: 구조화된 폼 형태의 신청서
- **필수 항목**: 기업 규모, 사업 분야, 연락처 등
- **첨부파일**: 다중 파일 업로드 지원
  - 사업계획서, 재무제표, 기술문서 등
  - 파일 형식 제한 (PDF, DOC, XLS 등)
  - 파일 크기 제한 (개별 10MB, 총 50MB)
- **임시저장**: 작성 중 자동/수동 저장
- **제출 확인**: 이메일 알림 및 접수번호 발급

#### 3.5.3 문서 관리 (관리자)
- **문서 자동 생성**: 입력 내용 기반 표준 문서 생성
- **문서 출력**: PDF 형태로 출력 가능
- **문서 저장**: 체계적인 파일 관리 시스템
- **문서 템플릿**: 사업별 맞춤 템플릿 설정

#### 3.5.4 심사 및 평가
- 지원서 검토 인터페이스
- 첨부파일 온라인 열람
- 평가 점수 입력 및 코멘트
- 심사 결과 관리

#### 3.5.5 수요 통계 시스템
- **기업 통계**:
  - 총 신청 기업 수
  - 기업 규모별 분포 (스타트업/중소/중견/대기업)
  - 지역별 분포
- **분야별 통계**:
  - 기술 분야별 신청 현황
  - 사업 유형별 분포
  - 연도별 트렌드 분석
- **진행 현황**:
  - 신청/심사/선정 단계별 현황
  - 선정률 및 경쟁률 통계
- **시각화**: 차트, 그래프를 통한 직관적 표현

## 4. 기술 요구사항

### 4.1 기술 스택
**Frontend:**
- **React 18+**: 함수형 컴포넌트, Concurrent Features
- **TypeScript**: 타입 안정성 및 개발 효율성
- **Vite**: 빠른 개발 환경 및 최적화된 빌드
- **ShadcnUI**: 고품질 컴포넌트 (최종 선택)
- **TailwindCSS**: 유틸리티 우선 스타일링
- **Framer Motion**: 애니메이션 및 상호작용

**Backend:**
- **Python 3.11+**: 메인 백엔드 언어
- **FastAPI**: 고성능 웹 프레임워크, 자동 API 문서 생성
- **SQLAlchemy**: ORM (Object-Relational Mapping)
- **Pydantic**: 데이터 검증 및 직렬화

**상태관리:**
- **Context API**: 전역 상태 (인증, 테마)
- **React Query**: 서버 상태 관리
- **React Hook Form**: 폼 상태 관리

**데이터 시각화:**
- **D3.js**: 에코시스템 맵, 복잡한 시각화
- **Chart.js**: 기본 통계 차트

**추가 라이브러리:**
- **pandas**: 데이터 분석 및 통계 처리
- **ReportLab**: PDF 문서 자동 생성
- **Celery + Redis**: 백그라운드 작업 및 캐싱

### 4.2 아키텍처 요구사항
- 컴포넌트 기반 모듈화 설계
- 반응형 웹 디자인 (모바일 우선)
- 확장 가능한 폴더 구조
- 재사용 가능한 컴포넌트 설계

### 4.3 성능 요구사항
- 초기 로딩 시간 3초 이내
- 페이지 전환 1초 이내
- 모바일 환경 최적화
- 이미지 최적화 및 지연 로딩

### 4.4 보안 요구사항
- JWT 기반 인증
- HTTPS 통신
- XSS/CSRF 방어
- 개인정보 암호화

## 5. 비기능 요구사항

### 5.1 디자인 요구사항 (기관용 고품질 디자인)
- **전문성**: 기관에서 사용하기에 적합한 전문적이고 신뢰감 있는 디자인
- **세련됨**: 모던하고 깔끔한 UI/UX, 최신 디자인 트렌드 반영
- **일관성**: 통일된 디자인 시스템, 컬러 팔레트, 타이포그래피
- **품질**: 조잡하지 않은 고품질 인터페이스
- **사용성**: 직관적이고 사용하기 쉬운 인터페이스
- **반응형**: 모든 디바이스에서 일관된 사용자 경험

### 5.2 UI 컴포넌트 라이브러리 선택
- **ShadcnUI vs 기타 라이브러리 비교 검토**
- 선택 기준:
  - 디자인 품질 및 일관성
  - 커스터마이징 용이성
  - 성능 및 번들 크기
  - 커뮤니티 지원 및 문서화
  - 접근성 지원 수준

### 5.3 사용성
- 직관적인 사용자 인터페이스
- 접근성 표준 준수 (WCAG 2.1)
- 다국어 지원 준비 (한국어 우선)

### 5.4 호환성
- 모던 브라우저 지원 (Chrome, Firefox, Safari, Edge)
- 모바일 브라우저 지원
- 태블릿 환경 최적화

### 5.5 확장성
- 사용자 증가에 대비한 설계
- 기능 추가 용이성
- 모듈별 독립적 개발 가능

## 6. 개발 일정 (3단계 개발)

### 1차 개발 (1개월): 핵심 모듈 구현
- **프로젝트 초기 설정**
  - UI 라이브러리 선택 (ShadcnUI vs 기타 비교 후 결정)
  - 기본 프로젝트 구조 및 개발 환경 설정
  - 고품질 디자인 시스템 구축

- **회원관리**
  - 사용자 인증/권한 시스템 (세분화된 권한 관리)
  - 다중 사용자 타입 지원 (기업/기관/개인/관리자)
  - 기본 프로필 관리

- **사업소개**
  - 사업 정보 관리 (공개 영역)
  - 기술소개 (권한 제한 영역)
  - 기술 가이드 (Wiki 형태, 권한 제한)
  - 소스코드 브라우징 (권한 제한)

- **공지관리**
  - 단순 게시판 형태 구현
  - 기본 CRUD 기능
  - 검색 및 필터링

- **수요관리**
  - 폼 기반 사업신청 시스템
  - 첨부파일 업로드 기능
  - 문서 자동 생성 및 출력 기능
  - 기본 관리자 대시보드

### 2차 개발 (1개월): 고급 기능 및 시각화
- **밸리관리**
  - 에코시스템 관계도 시각화 기반 구축
  - 참여 기관 정보 관리 시스템

- **참여기업 통계**
  - 수요 통계 시스템 구현
  - 기업 수, 분야별 분포 통계
  - 진행 현황 통계
  - 차트 및 그래프 시각화

- **기업관계도 (메인페이지)**
  - D3.js 기반 인터랙티브 에코시스템 맵
  - 애니메이션 효과 구현
  - 호버 상호작용 및 클릭 이벤트
  - 하단 통계 지표 시각화 (아이콘 + 숫자)

### 3차 개발 (1개월): 최적화 및 완성
- **반응형웹 최적화**
  - 모바일/태블릿/데스크톱 완전 대응
  - 성능 최적화 (로딩 속도, 번들 크기)
  - 크로스 브라우저 호환성 테스트
  - 접근성 표준 준수 (WCAG 2.1)

- **자료백업**
  - 데이터 백업 시스템 구축
  - 파일 백업 및 복구 기능
  - 시스템 로그 관리

- **전체기능완료**
  - 통합 테스트 및 버그 수정
  - 사용자 테스트 및 피드백 반영
  - 문서화 완료 (사용자 매뉴얼, 관리자 가이드)
  - 배포 준비 및 최종 검수

## 7. 성공 지표

### 7.1 사용자 지표
- 사용자 등록 수: 100개 기업/기관 (목표)
- 월 활성 사용자: 70% 이상
- 사용자 만족도: 4.0/5.0 이상

### 7.2 기능 지표
- 지원서 제출 완료율: 90% 이상
- 페이지 로딩 속도: 3초 이내
- 모바일 사용률: 40% 이상

### 7.3 비즈니스 지표
- 공모사업 참여율 증가: 20% 이상
- 플랫폼 이용률: 월 1회 이상 접속 70%
- 기술 문서 다운로드 수: 월 100건 이상

## 8. 위험 요소 및 대응 방안

### 8.1 기술적 위험
- **위험**: 복잡한 시각화 구현 지연
- **대응**: 단순한 차트부터 구현, 단계적 고도화

### 8.2 일정 위험
- **위험**: 3개월 개발 기간 부족
- **대응**: MVP 우선 개발, 핵심 기능 집중

### 8.3 사용자 수용 위험
- **위험**: 기존 시스템 대비 사용성 저하
- **대응**: 사용자 테스트 및 피드백 반영

## 9. 향후 확장 계획

### 9.1 구체화 예정 항목
- **회원 정보 구조**: 실제 운영 요구사항에 따른 상세 프로필 설계
- **수요 통계 고도화**: 더 정교한 분석 지표 및 리포트 기능
- **권한 관리 세분화**: 역할별 세부 권한 매트릭스
- **에코시스템 맵 고도화**: 더 복잡한 관계 표현 및 필터링

### 9.2 고도화 기능
- AI 기반 공모사업 추천
- 실시간 협업 도구
- 고급 데이터 시각화 및 대시보드
- 모바일 앱 개발
- 알림 시스템 (이메일, SMS, 푸시)

### 9.3 통합 기능
- 외부 시스템 API 연동
- 결제 시스템 통합
- 문서 관리 시스템 연동
- 화상회의 시스템 통합
- 전자서명 시스템 연동
