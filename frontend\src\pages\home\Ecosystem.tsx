import React, { useEffect, useRef } from 'react';

interface EcosystemProps {
  className?: string;
}

const Ecosystem: React.FC<EcosystemProps> = ({ className = '' }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const svg = svgRef.current;
    const container = containerRef.current;
    if (!svg || !container) return;

    const ns = 'http://www.w3.org/2000/svg';

    const connections = [
      { from: 'card-institutions', to: 'card-center', id: 'path-institutions-center', gradient: 'url(#grad-etri-flow)' },
      { from: 'card-companies', to: 'card-center', id: 'path-companies-center', gradient: 'url(#grad-etri-flow)' },
      { from: 'card-experts', to: 'card-center', id: 'path-experts-center', gradient: 'url(#grad-etri-flow)' },
      { from: 'card-startups', to: 'card-center', id: 'path-startups-center', gradient: 'url(#grad-etri-flow)' }
    ];

    function getElCenter(el: HTMLElement) {
      const rect = el.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      return {
        x: rect.left - containerRect.left + rect.width / 2,
        y: rect.top - containerRect.top + rect.height / 2
      };
    }

    function createRoundedRectPath(start: { x: number; y: number }, end: { x: number; y: number }) {
      const radius = 20; // 둥근 모서리 반지름
      const dx = end.x - start.x;
      const dy = end.y - start.y;

      // 중간점 계산 (L자 형태로 꺾이는 지점)
      let midX, midY;

      if (Math.abs(dx) > Math.abs(dy)) {
        // 수평 방향이 더 긴 경우: 수평 → 수직
        midX = end.x;
        midY = start.y;
      } else {
        // 수직 방향이 더 긴 경우: 수직 → 수평
        midX = start.x;
        midY = end.y;
      }

      // 방향 계산
      const hDir = start.x < midX ? 1 : -1; // 수평 방향
      const vDir = start.y < midY ? 1 : -1; // 수직 방향

      let path;

      if (Math.abs(dx) > Math.abs(dy)) {
        // 수평 → 수직 경로
        if (Math.abs(midY - start.y) < radius * 2) {
          // 거리가 너무 짧으면 직선
          path = `M ${start.x} ${start.y} L ${end.x} ${end.y}`;
        } else {
          path = `M ${start.x} ${start.y}
                  H ${midX - radius * hDir}
                  A ${radius} ${radius} 0 0 ${hDir * vDir > 0 ? 1 : 0} ${midX} ${start.y + radius * vDir}
                  V ${end.y}`;
        }
      } else {
        // 수직 → 수평 경로
        if (Math.abs(midX - start.x) < radius * 2) {
          // 거리가 너무 짧으면 직선
          path = `M ${start.x} ${start.y} L ${end.x} ${end.y}`;
        } else {
          path = `M ${start.x} ${start.y}
                  V ${midY - radius * vDir}
                  A ${radius} ${radius} 0 0 ${vDir * hDir > 0 ? 0 : 1} ${start.x + radius * hDir} ${midY}
                  H ${end.x}`;
        }
      }

      return path.replace(/\s+/g, ' ').trim();
    }

    function drawConnectors() {
      svg.querySelectorAll('.connector-path, .marker-diamond').forEach(el => el.remove());
      connections.forEach(conn => {
        const fromEl = document.getElementById(conn.from);
        const toEl = document.getElementById(conn.to);
        if (!fromEl || !toEl) return;

        const from = getElCenter(fromEl);
        const to = getElCenter(toEl);

        const path = document.createElementNS(ns, 'path');
        path.setAttribute('id', conn.id);
        path.setAttribute('class', 'connector-path');
        path.setAttribute('d', createRoundedRectPath(from, to));
        path.setAttribute('stroke', '#1c5aa0');
        path.setAttribute('stroke-width', '2.5');
        path.setAttribute('fill', 'none');
        path.setAttribute('opacity', '0.6');
        path.setAttribute('stroke-linecap', 'round');
        svg.appendChild(path);

        const marker = document.createElementNS(ns, 'use');
        marker.setAttribute('href', '#diamond-shape');
        marker.setAttribute('id', `marker-${conn.id}`);
        marker.setAttribute('class', 'marker-diamond');
        marker.setAttribute('fill', '#1c5aa0');
        marker.setAttribute('opacity', '0.8');
        svg.appendChild(marker);

        const animate = document.createElementNS(ns, 'animateMotion');
        animate.setAttribute('dur', '1.5s');
        animate.setAttribute('repeatCount', '1');
        animate.setAttribute('rotate', 'auto');
        animate.setAttribute('begin', 'indefinite');
        const mpath = document.createElementNS(ns, 'mpath');
        mpath.setAttribute('href', `#${conn.id}`);
        animate.appendChild(mpath);
        marker.appendChild(animate);
      });
    }

    function toggleAnimation(elements: (HTMLElement | null)[], active: boolean) {
      elements.forEach(el => {
        if (!el) return;
        const border = el.querySelector('.gradient-border-wrapper');
        if (border) border.classList.toggle('animated-gradient-border', active);
      });
    }

    function togglePaths(pathIds: string[], active: boolean) {
      pathIds.forEach(id => {
        const path = document.getElementById(id);
        const marker = document.getElementById(`marker-${id}`);
        if (path) {
          path.classList.toggle('active', active);
        }
        if (marker) {
          marker.classList.toggle('active', active);
          if (active) {
            const animateMotion = marker.querySelector('animateMotion');
            if (animateMotion) (animateMotion as any).beginElement();
          }
        }
      });
    }

    function setupInteractions() {
      const hoverConfigs = [
        { elementId: 'card-institutions', cardsToAnimate: ['card-institutions', 'card-center'], pathIds: ['path-institutions-center'] },
        { elementId: 'card-companies', cardsToAnimate: ['card-companies', 'card-center'], pathIds: ['path-companies-center'] },
        { elementId: 'card-experts', cardsToAnimate: ['card-experts', 'card-center'], pathIds: ['path-experts-center'] },
        { elementId: 'card-startups', cardsToAnimate: ['card-startups', 'card-center'], pathIds: ['path-startups-center'] },
      ];

      hoverConfigs.forEach(config => {
        const element = document.getElementById(config.elementId);
        if (!element) return;

        const elementsToAnimate = config.cardsToAnimate.map(id => document.getElementById(id));

        element.addEventListener('mouseenter', () => {
          toggleAnimation(elementsToAnimate, true);
          togglePaths(config.pathIds, true);
        });

        element.addEventListener('mouseleave', () => {
          toggleAnimation(elementsToAnimate, false);
          togglePaths(config.pathIds, false);
        });
      });

      // 중심 요소 특별 핸들러
      const centerCard = document.getElementById('card-center');
      if (centerCard) {
        centerCard.addEventListener('mouseenter', () => {
          const allElements = connections.reduce((acc, conn) => {
            acc.add(document.getElementById(conn.from));
            acc.add(document.getElementById(conn.to));
            return acc;
          }, new Set<HTMLElement | null>());
          const allPathIds = connections.map(c => c.id);

          toggleAnimation(Array.from(allElements), true);
          togglePaths(allPathIds, true);
        });

        centerCard.addEventListener('mouseleave', () => {
          const allElements = connections.reduce((acc, conn) => {
            acc.add(document.getElementById(conn.from));
            acc.add(document.getElementById(conn.to));
            return acc;
          }, new Set<HTMLElement | null>());
          const allPathIds = connections.map(c => c.id);

          toggleAnimation(Array.from(allElements), false);
          togglePaths(allPathIds, false);
        });
      }
    }

    drawConnectors();
    setupInteractions();

    // 모든 연결선을 기본적으로 보이게 설정
    setTimeout(() => {
      const allPaths = document.querySelectorAll('#connector-svg path.connector-path');
      const allMarkers = document.querySelectorAll('#connector-svg .marker-diamond');

      console.log('Found paths:', allPaths.length);
      console.log('Found markers:', allMarkers.length);

      allPaths.forEach((path, index) => {
        console.log(`Path ${index}:`, path);
        (path as HTMLElement).style.opacity = '0.8';
        (path as HTMLElement).style.stroke = '#1c5aa0';
        (path as HTMLElement).style.strokeWidth = '3px';
      });
      allMarkers.forEach((marker, index) => {
        console.log(`Marker ${index}:`, marker);
        (marker as HTMLElement).style.opacity = '0.8';
      });
    }, 500);

    const handleResize = () => {
      drawConnectors();
      setupInteractions();
      // 리사이즈 후에도 연결선 표시
      setTimeout(() => {
        const allPaths = document.querySelectorAll('#connector-svg path.connector-path');
        const allMarkers = document.querySelectorAll('#connector-svg .marker-diamond');
        allPaths.forEach(path => {
          (path as HTMLElement).style.opacity = '0.6';
        });
        allMarkers.forEach(marker => {
          (marker as HTMLElement).style.opacity = '0.7';
        });
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`} style={{ backgroundColor: '#f9fbf9' }}>
      <style dangerouslySetInnerHTML={{
        __html: `

          @keyframes rotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
          }

          .gradient-border-wrapper {
            position: relative;
            border-radius: 1rem;
            overflow: hidden;
            z-index: 1;
          }

          .animated-gradient-border::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300%;
            height: 300%;
            background: conic-gradient(from 0.turn, #1c5aa0, #1e4a8c, #f55a2c, #1a3f78, #1c5aa0);
            animation: rotate 4s linear infinite;
            z-index: 1;
          }

          .card-inner {
            position: relative;
            margin: 3px;
            background-color: white;
            border-radius: calc(1rem - 3px);
            width: calc(100% - 6px);
            height: calc(100% - 6px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            z-index: 2;
          }

          #card-center .card-inner {
            margin: 4px;
            width: calc(100% - 8px);
            height: calc(100% - 8px);
            border-radius: calc(1rem - 4px);
          }

          .grid-background {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 90%;
            background-image:
              linear-gradient(to right, #eef3ee 1px, transparent 1px),
              linear-gradient(to bottom, #eef3ee 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 0;
            mask-image: radial-gradient(circle at center, black 25%, transparent 75%);
            -webkit-mask-image: radial-gradient(circle at center, black 25%, transparent 75%);
          }

          .diagram-container {
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: auto;
            padding: 2rem;
          }

          #connector-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            pointer-events: none;
            overflow: visible;
          }

          #connector-svg path.connector-path {
            fill: none !important;
            stroke: #1c5aa0 !important;
            stroke-width: 2.5px !important;
            stroke-linecap: round !important;
            opacity: 0.6 !important;
            transition: all 0.3s ease-in-out;
          }

          #connector-svg .marker-diamond {
            fill: #1c5aa0;
            stroke: white;
            stroke-width: 1px;
            opacity: 0.8;
            transition: opacity 0.3s ease-in-out;
          }

          /* 호버 시 연결선과 마커 강조 */
          #connector-svg path.connector-path.active {
            opacity: 0.9 !important;
            stroke-width: 3.5px !important;
            stroke: #f55a2c !important;
          }

          #connector-svg .marker-diamond.active {
            opacity: 1 !important;
            fill: #f55a2c !important;
            transform: scale(1.3);
          }

          @media (max-width: 768px) {
            .diagram-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4rem;
            }
            #connector-svg, .grid-background {
              display: none;
            }
          }
        `
      }} />

      <div className="diagram-container" id="diagram-container" ref={containerRef}>
        <div className="grid-background"></div>
        <svg id="connector-svg" ref={svgRef}>
          <defs>
            <linearGradient id="grad-etri-flow" x1="0%" y1="50%" x2="100%" y2="50%">
              <stop offset="0%" style={{stopColor:'#1c5aa0'}} />
              <stop offset="50%" style={{stopColor:'#1e4a8c'}} />
              <stop offset="100%" style={{stopColor:'#f55a2c'}} />
            </linearGradient>

            <path id="diamond-shape" d="M 0 -5 L 5 0 L 0 5 L -5 0 Z" fill="#1c5aa0" />
          </defs>

          {/* 테스트용 고정 연결선들 - 둥근 사각형 형태 */}
          <path d="M 150 150 H 380 A 20 20 0 0 1 400 170 V 200" stroke="#1c5aa0" strokeWidth="3" fill="none" opacity="0.8" className="test-path" />
          <path d="M 650 150 H 420 A 20 20 0 0 0 400 170 V 200" stroke="#1c5aa0" strokeWidth="3" fill="none" opacity="0.8" className="test-path" />
          <path d="M 150 450 H 380 A 20 20 0 0 0 400 430 V 400" stroke="#1c5aa0" strokeWidth="3" fill="none" opacity="0.8" className="test-path" />
          <path d="M 650 450 H 420 A 20 20 0 0 1 400 430 V 400" stroke="#1c5aa0" strokeWidth="3" fill="none" opacity="0.8" className="test-path" />
        </svg>

        <div className="relative grid grid-cols-1 md:grid-cols-5 md:grid-rows-3 gap-y-8 md:gap-x-4 md:gap-y-16 items-center">
          {/* 참여기관 - 왼쪽 상단 */}
          <div id="card-institutions" className="md:col-start-1 md:col-end-2 md:row-start-1 md:row-end-2 justify-self-center md:justify-self-start z-10 w-48">
            <div className="gradient-border-wrapper animated-gradient-border">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-etri-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <span className="font-semibold text-gray-800">참여기관</span>
                </div>
                <p className="text-sm text-gray-500">연구기관 및 대학</p>
              </div>
            </div>
          </div>

          {/* 참여기업 - 오른쪽 상단 */}
          <div id="card-companies" className="md:col-start-5 md:col-end-6 md:row-start-1 md:row-end-2 justify-self-center md:justify-self-end z-10 w-48">
            <div className="gradient-border-wrapper animated-gradient-border">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-etri-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <span className="font-semibold text-gray-800">참여기업</span>
                </div>
                <p className="text-sm text-gray-500">대기업 및 중견기업</p>
              </div>
            </div>
          </div>

          {/* ETRI 중앙 로고 */}
          <div id="card-center" className="md:col-start-3 md:col-end-4 md:row-start-2 md:row-end-3 justify-self-center z-10 w-32 h-32">
            <div className="gradient-border-wrapper animated-gradient-border rounded-2xl">
              <div className="card-inner flex items-center justify-center p-4 rounded-xl bg-white">
                <img
                  src="/1.-wordmark.png"
                  alt="ETRI Logo"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>

          {/* 참여전문가 - 왼쪽 하단 */}
          <div id="card-experts" className="md:col-start-1 md:col-end-2 md:row-start-3 md:row-end-4 justify-self-center md:justify-self-start z-10 w-48">
            <div className="gradient-border-wrapper animated-gradient-border">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-etri-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span className="font-semibold text-gray-800">참여전문가</span>
                </div>
                <p className="text-sm text-gray-500">기술 전문가 및 멘토</p>
              </div>
            </div>
          </div>

          {/* 스타트업 - 오른쪽 하단 */}
          <div id="card-startups" className="md:col-start-5 md:col-end-6 md:row-start-3 md:row-end-4 justify-self-center md:justify-self-end z-10 w-48">
            <div className="gradient-border-wrapper animated-gradient-border">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-etri-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span className="font-semibold text-gray-800">스타트업</span>
                </div>
                <p className="text-sm text-gray-500">혁신 스타트업</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ecosystem;
