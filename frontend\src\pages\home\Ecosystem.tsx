import React, { useEffect, useRef } from 'react';

interface EcosystemProps {
  className?: string;
}

const Ecosystem: React.FC<EcosystemProps> = ({ className = '' }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const svg = svgRef.current;
    const container = containerRef.current;
    if (!svg || !container) return;

    const ns = 'http://www.w3.org/2000/svg';

    const connections = [
      { from: 'card-supplier', to: 'card-aria-pays', id: 'path-supplier-aria', gradient: 'url(#grad-supplier-flow)' },
      { from: 'card-aria-pays', to: 'card-center', id: 'path-aria-center', gradient: 'url(#grad-supplier-flow)' },
      { from: 'card-center', to: 'card-buyer-pays', id: 'path-center-buyer', gradient: 'url(#grad-buyer-flow)' },
      { from: 'card-buyer-pays', to: 'card-buyer', id: 'path-buyer-pays-buyer', gradient: 'url(#grad-buyer-flow)' },
      { from: 'card-banks', to: 'card-center', id: 'path-banks-center', gradient: 'url(#grad-banks-flow)' }
    ];

    function getElCenter(el: HTMLElement) {
      const rect = el.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      return {
        x: rect.left - containerRect.left + rect.width / 2,
        y: rect.top - containerRect.top + rect.height / 2
      };
    }

    function createElbowPath(start: { x: number; y: number }, end: { x: number; y: number }) {
      const radius = 20;
      let d;
      if (Math.abs(start.x - end.x) > Math.abs(start.y - end.y)) {
        const midX = end.x;
        const hDir = start.x < end.x ? 1 : -1;
        const vDir = start.y < end.y ? 1 : -1;
        d = `M ${start.x} ${start.y} H ${midX - radius * hDir} A ${radius} ${radius} 0 0 ${hDir * vDir > 0 ? 1 : 0} ${midX} ${start.y + radius * vDir} V ${end.y}`;
      } else {
        const midY = end.y;
        const vDir = start.y < end.y ? 1 : -1;
        const hDir = start.x < end.x ? 1 : -1;
        d = `M ${start.x} ${start.y} V ${midY - radius * vDir} A ${radius} ${radius} 0 0 ${vDir * hDir > 0 ? 0 : 1} ${start.x + radius * hDir} ${midY} H ${end.x}`;
      }
      return d;
    }

    function drawConnectors() {
      svg.querySelectorAll('.connector-path, .marker-diamond').forEach(el => el.remove());
      connections.forEach(conn => {
        const fromEl = document.getElementById(conn.from);
        const toEl = document.getElementById(conn.to);
        if (!fromEl || !toEl) return;

        const from = getElCenter(fromEl);
        const to = getElCenter(toEl);

        const path = document.createElementNS(ns, 'path');
        path.setAttribute('id', conn.id);
        path.setAttribute('class', 'connector-path');
        path.setAttribute('d', createElbowPath(from, to));
        path.style.stroke = conn.gradient;
        svg.appendChild(path);

        const marker = document.createElementNS(ns, 'use');
        marker.setAttribute('href', '#diamond-shape');
        marker.setAttribute('id', `marker-${conn.id}`);
        marker.setAttribute('class', 'marker-diamond');
        svg.appendChild(marker);

        const animate = document.createElementNS(ns, 'animateMotion');
        animate.setAttribute('dur', '1.5s');
        animate.setAttribute('repeatCount', '1');
        animate.setAttribute('rotate', 'auto');
        animate.setAttribute('begin', 'indefinite');
        const mpath = document.createElementNS(ns, 'mpath');
        mpath.setAttribute('href', `#${conn.id}`);
        animate.appendChild(mpath);
        marker.appendChild(animate);
      });
    }

    function toggleAnimation(elements: (HTMLElement | null)[], active: boolean) {
      elements.forEach(el => {
        if (!el) return;
        const border = el.querySelector('.gradient-border-wrapper');
        if (border) border.classList.toggle('animated-gradient-border', active);
      });
    }

    function togglePaths(pathIds: string[], active: boolean) {
      pathIds.forEach(id => {
        const path = document.getElementById(id);
        const marker = document.getElementById(`marker-${id}`);
        if (path) path.style.opacity = active ? '1' : '0';
        if (marker) {
          marker.style.opacity = active ? '1' : '0';
          if (active) {
            const animateMotion = marker.querySelector('animateMotion');
            if (animateMotion) (animateMotion as any).beginElement();
          }
        }
      });
    }

    function setupInteractions() {
      const hoverConfigs = [
        { elementId: 'card-supplier', cardsToAnimate: ['card-supplier', 'card-center'], pathIds: ['path-supplier-aria', 'path-aria-center'] },
        { elementId: 'card-buyer', cardsToAnimate: ['card-buyer', 'card-center'], pathIds: ['path-center-buyer', 'path-buyer-pays-buyer'] },
        { elementId: 'card-banks', cardsToAnimate: ['card-banks', 'card-center'], pathIds: ['path-banks-center'] },
        { elementId: 'card-aria-pays', cardsToAnimate: ['card-aria-pays', 'card-center'], pathIds: ['path-supplier-aria', 'path-aria-center'] },
        { elementId: 'card-buyer-pays', cardsToAnimate: ['card-buyer-pays', 'card-center'], pathIds: ['path-center-buyer', 'path-buyer-pays-buyer'] },
      ];

      hoverConfigs.forEach(config => {
        const element = document.getElementById(config.elementId);
        if (!element) return;

        const elementsToAnimate = config.cardsToAnimate.map(id => document.getElementById(id));

        element.addEventListener('mouseenter', () => {
          toggleAnimation(elementsToAnimate, true);
          togglePaths(config.pathIds, true);
        });

        element.addEventListener('mouseleave', () => {
          toggleAnimation(elementsToAnimate, false);
          togglePaths(config.pathIds, false);
        });
      });

      // 중심 요소 특별 핸들러
      const centerCard = document.getElementById('card-center');
      if (centerCard) {
        centerCard.addEventListener('mouseenter', () => {
          const allElements = connections.reduce((acc, conn) => {
            acc.add(document.getElementById(conn.from));
            acc.add(document.getElementById(conn.to));
            return acc;
          }, new Set<HTMLElement | null>());
          const allPathIds = connections.map(c => c.id);

          toggleAnimation(Array.from(allElements), true);
          togglePaths(allPathIds, true);
        });

        centerCard.addEventListener('mouseleave', () => {
          const allElements = connections.reduce((acc, conn) => {
            acc.add(document.getElementById(conn.from));
            acc.add(document.getElementById(conn.to));
            return acc;
          }, new Set<HTMLElement | null>());
          const allPathIds = connections.map(c => c.id);

          toggleAnimation(Array.from(allElements), false);
          togglePaths(allPathIds, false);
        });
      }
    }

    drawConnectors();
    setupInteractions();

    const handleResize = () => {
      drawConnectors();
      setupInteractions();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`} style={{ backgroundColor: '#f9fbf9' }}>
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes rotate {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
          }

          .gradient-border-wrapper {
            position: relative;
            border-radius: 1rem;
            overflow: hidden;
            z-index: 1;
          }

          .animated-gradient-border::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300%;
            height: 300%;
            background: conic-gradient(from 0.turn, #a8e063, #56ab2f, #89f7fe, #66a6ff, #a8e063);
            animation: rotate 4s linear infinite;
            z-index: 1;
          }

          .card-inner {
            position: relative;
            margin: 3px;
            background-color: white;
            border-radius: calc(1rem - 3px);
            width: calc(100% - 6px);
            height: calc(100% - 6px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            z-index: 2;
          }

          #card-center .card-inner {
            margin: 4px;
            width: calc(100% - 8px);
            height: calc(100% - 8px);
            border-radius: calc(1rem - 4px);
          }

          .grid-background {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50%;
            height: 90%;
            background-image:
              linear-gradient(to right, #eef3ee 1px, transparent 1px),
              linear-gradient(to bottom, #eef3ee 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 0;
            mask-image: radial-gradient(circle at center, black 25%, transparent 75%);
            -webkit-mask-image: radial-gradient(circle at center, black 25%, transparent 75%);
          }

          .diagram-container {
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: auto;
            padding: 2rem;
          }

          #connector-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
          }

          #connector-svg path.connector-path {
            fill: none;
            stroke-width: 2.5px;
            stroke-dasharray: 8 4;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
          }

          #connector-svg .marker-diamond {
            fill: #66a6ff;
            stroke: white;
            stroke-width: 2px;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
          }

          @media (max-width: 768px) {
            .diagram-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4rem;
            }
            #connector-svg, .grid-background {
              display: none;
            }
          }
        `
      }} />

      <div className="diagram-container" id="diagram-container" ref={containerRef}>
        <div className="grid-background"></div>
        <svg id="connector-svg" ref={svgRef}>
          <defs>
            <linearGradient id="grad-supplier-flow" x1="0%" y1="50%" x2="100%" y2="50%">
              <stop offset="0%" style={{stopColor:'#a8e063'}} />
              <stop offset="100%" style={{stopColor:'#56ab2f'}} />
            </linearGradient>
            <linearGradient id="grad-buyer-flow" x1="0%" y1="50%" x2="100%" y2="50%">
              <stop offset="0%" style={{stopColor:'#66a6ff'}} />
              <stop offset="100%" style={{stopColor:'#89f7fe'}} />
            </linearGradient>
            <linearGradient id="grad-banks-flow" x1="50%" y1="100%" x2="50%" y2="0%">
              <stop offset="0%" style={{stopColor:'#a8e063'}} />
              <stop offset="100%" style={{stopColor:'#66a6ff'}} />
            </linearGradient>

            <path id="diamond-shape" d="M 0 -5 L 5 0 L 0 5 L -5 0 Z" />
          </defs>
        </svg>

        <div className="relative grid grid-cols-1 md:grid-cols-5 md:grid-rows-3 gap-y-8 md:gap-x-4 md:gap-y-16 items-center">
          <div id="card-supplier" className="md:col-start-1 md:col-end-2 md:row-start-1 md:row-end-2 justify-self-center md:justify-self-start z-10 w-40">
            <div className="gradient-border-wrapper">
              <div className="card-inner p-4 text-center flex items-center justify-center h-16">
                <span className="font-semibold text-lg text-gray-800">Supplier</span>
              </div>
            </div>
          </div>

          <div id="card-aria-pays" className="md:col-start-2 md:col-end-3 md:row-start-2 md:row-end-3 justify-self-center z-10 w-56">
            <div className="gradient-border-wrapper">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  <span className="font-semibold text-gray-800">Aria pays supplier</span>
                </div>
                <p className="text-sm text-gray-500 mt-2">Payment terms set by buyer</p>
              </div>
            </div>
          </div>

          <div id="card-center" className="md:col-start-3 md:col-end-4 md:row-start-2 md:row-end-3 justify-self-center z-10 w-28 h-28">
            <div className="gradient-border-wrapper rounded-2xl">
              <div className="card-inner flex items-center justify-center p-6 rounded-xl">
                <svg width="60" height="60" viewBox="0 0 84 96" fill="none" xmlns="http://www.w3.org/2000/svg" className="transform -rotate-12">
                  <path d="M41.5629 0.999999L83.1257 25L41.5629 49L0 25L41.5629 0.999999Z" fill="#2d3748"/>
                  <path d="M41.5629 47L83.1257 71L41.5629 95L0 71L41.5629 47Z" fill="#2d3748" opacity="0.8"/>
                  <path d="M25.7812 37.5L57.3441 56.5L41.5629 66L10 47L25.7812 37.5Z" fill="#2d3748" opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>

          <div id="card-buyer-pays" className="md:col-start-4 md:col-end-5 md:row-start-2 md:row-end-3 justify-self-center z-10 w-56">
            <div className="gradient-border-wrapper">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  <span className="font-semibold text-gray-800">Buyer pays Aria</span>
                </div>
                <p className="text-sm text-gray-500 mt-2">Receivable amount paid</p>
              </div>
            </div>
          </div>

          <div id="card-buyer" className="md:col-start-5 md:col-end-6 md:row-start-1 md:row-end-2 justify-self-center md:justify-self-end z-10 w-40">
            <div className="gradient-border-wrapper">
              <div className="card-inner p-4 text-center flex items-center justify-center h-16">
                <span className="font-semibold text-lg text-gray-800">Buyer</span>
              </div>
            </div>
          </div>

          <div id="card-banks" className="md:col-start-3 md:col-end-4 md:row-start-3 md:row-end-4 justify-self-center z-10 w-64">
            <div className="gradient-border-wrapper">
              <div className="card-inner p-4 text-center">
                <div className="flex items-center justify-center gap-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v.01M12 6v-1h4v1m-4 0a2 2 0 100 4 2 2 0 000-4zm-7 8a2 2 0 11-4 0 2 2 0 014 0z" />
                    <path d="M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11m16-11v11M12 10v11" />
                  </svg>
                  <span className="font-semibold text-gray-800">Banks</span>
                </div>
                <p className="text-sm text-gray-500 mt-2">Funding partners provide liquidity</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ecosystem;
