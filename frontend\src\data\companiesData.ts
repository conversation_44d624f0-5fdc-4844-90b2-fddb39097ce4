export interface Company {
  id: string;
  name: string;
  logo: string;
  website?: string;
  category: 'major' | 'startup' | 'institution' | 'research';
}

export const participatingCompanies: Company[] = [
  // 대기업
  {
    id: 'kt',
    name: 'KT',
    logo: '/company-logos/kt.svg',
    website: 'https://www.kt.com',
    category: 'major'
  },
  {
    id: 'samsung',
    name: '삼성전자',
    logo: '/company-logos/samsung.svg',
    website: 'https://www.samsung.com',
    category: 'major'
  },
  {
    id: 'lg',
    name: 'LG전자',
    logo: '/company-logos/lg.svg',
    website: 'https://www.lg.com',
    category: 'major'
  },
  {
    id: 'sk',
    name: 'SK텔레콤',
    logo: '/company-logos/sk.svg',
    website: 'https://www.sktelecom.com',
    category: 'major'
  },
  {
    id: 'naver',
    name: 'NAVER',
    logo: '/company-logos/naver.svg',
    website: 'https://www.naver.com',
    category: 'major'
  },
  {
    id: 'kakao',
    name: '<PERSON><PERSON><PERSON>',
    logo: '/company-logos/kakao.svg',
    website: 'https://www.kakaocorp.com',
    category: 'major'
  },
  
  // 연구기관
  {
    id: 'kaist',
    name: 'KAIST',
    logo: '/company-logos/kaist.svg',
    website: 'https://www.kaist.ac.kr',
    category: 'research'
  },
  {
    id: 'snu',
    name: '서울대학교',
    logo: '/company-logos/snu.svg',
    website: 'https://www.snu.ac.kr',
    category: 'research'
  },
  {
    id: 'kist',
    name: 'KIST',
    logo: '/company-logos/kist.svg',
    website: 'https://www.kist.re.kr',
    category: 'research'
  },
  {
    id: 'postech',
    name: 'POSTECH',
    logo: '/company-logos/postech.svg',
    website: 'https://www.postech.ac.kr',
    category: 'research'
  },
  
  // 스타트업
  {
    id: 'startup1',
    name: 'DeepTech Startup A',
    logo: '/company-logos/startup-a.svg',
    category: 'startup'
  },
  {
    id: 'startup2',
    name: 'AI Innovation B',
    logo: '/company-logos/startup-b.svg',
    category: 'startup'
  },
  {
    id: 'startup3',
    name: 'Quantum Tech C',
    logo: '/company-logos/startup-c.svg',
    category: 'startup'
  },
  {
    id: 'startup4',
    name: 'BioTech D',
    logo: '/company-logos/startup-d.svg',
    category: 'startup'
  },
  {
    id: 'startup5',
    name: 'RoboTech E',
    logo: '/company-logos/startup-e.svg',
    category: 'startup'
  },
  {
    id: 'startup6',
    name: 'CleanTech F',
    logo: '/company-logos/startup-f.svg',
    category: 'startup'
  }
];

export const getCompaniesByCategory = (category: Company['category']): Company[] => {
  return participatingCompanies.filter(company => company.category === category);
};

export const getMajorCompanies = (): Company[] => getCompaniesByCategory('major');
export const getStartups = (): Company[] => getCompaniesByCategory('startup');
export const getResearchInstitutions = (): Company[] => getCompaniesByCategory('research');
