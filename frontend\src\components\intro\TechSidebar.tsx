import React from 'react';
import { techList } from '@/data/techData';
import { Briefcase, BookOpen } from 'lucide-react';
import { Sidebar } from '@/components/common/Sidebar';
import type { SidebarMenuItem } from '@/components/common/types';



export const TechSidebar: React.FC = () => {
  // 사이드바 메뉴 구성
  const menuItems: SidebarMenuItem[] = [
    {
      id: 'business',
      name: '사업소개',
      icon: Briefcase,
      path: '/intro/about'
    },
    {
      id: 'tech',
      name: '기술소개',
      icon: BookOpen,
      path: '/intro/tech',
      subItems: techList.map(tech => ({
        id: tech.id,
        name: tech.name,
        path: `/intro/tech/${tech.id}`
      }))
    }
  ];

  return (
    <Sidebar
      title="사업소개"
      menuItems={menuItems}
      width="w-80"
    />
  );
};