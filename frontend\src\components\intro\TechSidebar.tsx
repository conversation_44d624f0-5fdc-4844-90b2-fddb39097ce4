import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { techList } from '@/data/techData';
import { ChevronRight, Briefcase, BookOpen } from 'lucide-react';



export const TechSidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  // openMenu: 현재 열려있는 상단 메뉴 ('business' | 'tech' | null)
  const [openMenu, setOpenMenu] = useState<'business' | 'tech' | null>(null);
  // 하단 메뉴(기술목록) 애니메이션 트리거
  const [showTechList, setShowTechList] = useState(false);
  // 실제 DOM 렌더링 여부 (애니메이션 자연스럽게)
  const [isTechListVisible, setIsTechListVisible] = useState(false);

  const techListRef = useRef<HTMLDivElement>(null);

  // 현재 상세 페이지라면 id 추출 (하이픈 포함)
  const match = location.pathname.match(/\/intro\/tech\/([^/]+)/);
  const selectedTechId = match ? match[1] : null;

  // 기술소개 메뉴가 선택된 상태인지 판별 (메인, 상세 모두 포함)
  const isTechMenuSelected =
    location.pathname === '/intro/tech' ||
    location.pathname === '/intro/tech/' ||
    location.pathname.startsWith('/intro/tech/');

  // 경로에 따라 openMenu, showTechList 자동 동기화
  useEffect(() => {
    if (selectedTechId) {
      setOpenMenu('tech');
      setShowTechList(true); // 상세 진입 시 항상 펼침
    } else if (
      location.pathname.startsWith('/business') ||
      location.pathname === '/intro/about' // 사업소개(About) 경로도 선택
    ) {
      setOpenMenu('business');
      setShowTechList(false);
    } else if (
      location.pathname === '/intro/tech' || location.pathname === '/intro/tech/'
    ) {
      // 기술소개 메인 진입 시: 메뉴는 선택, 하단은 닫힘
      setOpenMenu('tech');
      setShowTechList(false);
    } else if (location.pathname.startsWith('/intro/tech')) {
      // 기타 기술소개 경로(예외 상황)도 닫힘 유지
      setOpenMenu('tech');
      setShowTechList(false);
    }
  }, [location.pathname, selectedTechId]);

  // showTechList가 true가 되면 바로 DOM 렌더링, false가 되면 transition 후 제거
  useEffect(() => {
    if (showTechList) {
      setIsTechListVisible(true);
    }
  }, [showTechList]);

  // transition 끝난 후 닫힘이면 DOM 제거
  const handleTransitionEnd = () => {
    if (!showTechList) {
      setIsTechListVisible(false);
    }
  };

  // 상단 메뉴 텍스트 클릭 핸들러
  const handleMenuClick = (menu: 'business' | 'tech') => {
    if (menu === 'business') {
      // 사업소개 클릭 시 intro/about으로 이동
      if (openMenu !== 'business') {
        navigate('/intro/about');
        setOpenMenu('business');
      }
      // 이미 선택된 상태에서 클릭 시 아무 동작 없음(하단 메뉴 없음)
    } else if (menu === 'tech') {
      // 기술소개 클릭 시
      if (openMenu !== 'tech') {
        navigate('/intro/tech');
        setOpenMenu('tech');
      } else if (selectedTechId) {
        // 하단 메뉴에서 항목이 선택된 상태에서 상단 메뉴 클릭 시, 상단 메뉴로 이동
        navigate('/intro/tech');
        setShowTechList(true);
      } else {
        // 이미 선택된 상태에서 상세가 아니면 하단 메뉴 토글
        setShowTechList((prev) => !prev);
      }
    }
  };

  // 상단 메뉴 화살표 클릭 핸들러 (즉시 토글)
  const handleArrowClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 상위 클릭 이벤트 방지
    setShowTechList((prev) => !prev);
    setOpenMenu('tech');
  };

  // 하단 메뉴 클릭 핸들러
  const handleTechClick = (id: string) => {
    navigate(`/intro/tech/${id}`);
    setOpenMenu('tech');
    if (!showTechList) {
      setShowTechList(true); // 이미 펼쳐진 상태가 아니면 펼침
    }
  };

  // 하단 메뉴의 maxHeight 계산 (아이템 개수에 따라 동적)
  const maxHeight = techList.length * 44 + 16; // 44px per item + margin

  return (
    <div className="w-80 flex-shrink-0">
      <div className="p-6">
        <div className="mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-2">사업소개</h2>
          <div className="w-8 h-0.5 bg-gray-300"></div>
        </div>
        <nav className="space-y-2">
          {/* 상단 메뉴: 사업소개 */}
          <button
            onClick={() => handleMenuClick('business')}
            className={`group flex items-center px-4 py-2 text-base rounded-lg w-full text-left transition-all duration-200 relative ${
              openMenu === 'business'
                ? 'text-gray-900 bg-gray-100 border-l-3 border-etri-blue-600 font-medium'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <Briefcase className="w-5 h-5 mr-3 transition-colors duration-200" />
            <span className="flex-1">사업소개</span>
          </button>
          {/* 상단 메뉴: 기술소개 */}
          <button
            onClick={() => handleMenuClick('tech')}
            className={`group flex items-center px-4 py-2 text-base rounded-lg w-full text-left transition-all duration-200 relative ${
              isTechMenuSelected
                ? 'text-gray-900 bg-gray-100 border-l-3 border-etri-blue-600 font-medium'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <BookOpen className="w-5 h-5 mr-3 transition-colors duration-200" />
            <span className="flex-1">기술소개</span>
            <ChevronRight
              onClick={handleArrowClick}
              className={`w-4 h-4 transition-all duration-200 cursor-pointer ${
                showTechList ? 'rotate-90' : ''
              } ${isTechMenuSelected ? 'text-gray-600' : 'text-gray-400 group-hover:text-gray-600'}`}
            />
          </button>
          {/* 하단 기술목록: 현대적인 서브메뉴 디자인 */}
          <div
            ref={techListRef}
            className="overflow-hidden transition-all duration-500 ease-in-out"
            style={{
              maxHeight: showTechList ? maxHeight : 0,
              opacity: showTechList ? 1 : 0,
            }}
            onTransitionEnd={handleTransitionEnd}
          >
            {isTechListVisible && (
              <div className="mt-3 space-y-1 pl-4">
                <div className="w-6 h-px bg-gray-300 mb-3"></div>
                {techList.map((tech, index) => (
                  <button
                    key={tech.id}
                    onClick={() => handleTechClick(tech.id)}
                    className={`group flex items-center px-3 py-1.5 text-sm rounded-md w-full text-left transition-all duration-200 relative ${
                      selectedTechId === tech.id
                        ? 'text-gray-900 bg-gray-100 border-l-2 border-etri-blue-600 font-medium'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                    style={{
                      animationDelay: `${index * 50}ms`,
                      animation: showTechList ? 'slideInLeft 0.3s ease-out forwards' : 'none'
                    }}
                  >
                    <div className={`w-1.5 h-1.5 rounded-full mr-3 transition-all duration-200 ${
                      selectedTechId === tech.id
                        ? 'bg-etri-blue-600'
                        : 'bg-gray-400 group-hover:bg-gray-600'
                    }`}></div>
                    <span className={selectedTechId === tech.id ? 'font-medium' : ''}>
                      {tech.name}
                    </span>
                  </button>
                ))}
              </div>
            )}
          </div>
        </nav>
      </div>
    </div>
  );
};