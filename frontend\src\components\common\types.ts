// 사이드바 관련 타입 정의

// 서브 메뉴 아이템 타입
export interface SidebarSubItem {
  id: string;
  name: string;
  path: string;
}

// 사이드바 메뉴 아이템 타입
export interface SidebarMenuItem {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  subItems?: SidebarSubItem[];
}

// 사이드바 컴포넌트 Props
export interface SidebarProps {
  title: string;
  menuItems: SidebarMenuItem[];
  className?: string;
  width?: 'w-64' | 'w-72' | 'w-80' | 'w-96';
}
