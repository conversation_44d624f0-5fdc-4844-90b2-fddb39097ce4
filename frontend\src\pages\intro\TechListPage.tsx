import React from 'react';
import { useNavigate } from 'react-router-dom';
import { techList } from '@/data/techData';
import { TechSidebar } from '@/components/intro/TechSidebar';

const TechListPage: React.FC = () => {
  const navigate = useNavigate();

  const handleTechClick = (techId: string) => {
    navigate(`/intro/tech/${techId}`);
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* 좌측 사이드바 */}
          <TechSidebar />

          {/* 메인 콘텐츠 */}
          <div className="flex-1">
            <div className="mb-10">
              <nav className="flex items-center text-base text-gray-500 mb-4" aria-label="Breadcrumb">
                <a href="/" className="hover:text-gray-700">홈</a>
                <svg className="w-5 h-5 mx-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-900 font-medium">기술소개</span>
              </nav>
              <h1 className="text-3xl font-bold text-gray-900">기술소개</h1>
            </div>
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-left">주요 기술</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {techList.slice(0, 6).map((tech) => (
                  <div key={tech.id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer p-5"
                       onClick={() => handleTechClick(tech.id)}>
                    <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                      <span className="text-lg text-gray-400 font-bold">{tech.name}</span>
                    </div>
                    <h3 className="font-semibold text-gray-900 text-base line-clamp-2 mb-3">
                      {tech.name}
                    </h3>
                    <div className="text-sm text-gray-500 mb-2 line-clamp-2">{tech.summary}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechListPage; 